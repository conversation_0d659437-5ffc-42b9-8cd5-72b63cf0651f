import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useUndoRedoStore } from "@/lib/stores/undo-redo-store";
import type { Clip } from "@/types/video-editor";
import { useCallback, useRef } from "react";

interface ClipboardData {
  clips: Clip[];
  timestamp: Date;
}

export function useTimelineClipboard() {
  const timelineStore = useTimelineStore();
  const undoRedoStore = useUndoRedoStore();
  const clipboardRef = useRef<ClipboardData | null>(null);

  // Copy selected clips to clipboard
  const copyClips = useCallback(() => {
    const selectedClips = timelineStore.getSelectedClips();
    if (selectedClips.length === 0) return false;

    clipboardRef.current = {
      clips: selectedClips.map((clip) => ({ ...clip })), // Deep copy
      timestamp: new Date(),
    };

    return true;
  }, [timelineStore]);

  // Cut selected clips (copy + delete)
  const cutClips = useCallback(() => {
    const selectedClips = timelineStore.getSelectedClips();
    if (selectedClips.length === 0) return false;

    // Record state for undo
    const beforeState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    // Copy to clipboard
    clipboardRef.current = {
      clips: selectedClips.map((clip) => ({ ...clip })),
      timestamp: new Date(),
    };

    // Delete clips
    selectedClips.forEach((clip) => {
      timelineStore.removeClip(clip.id);
    });
    timelineStore.clearSelection();

    // Record action for undo
    const afterState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    undoRedoStore.pushAction({
      type: "cut_clips",
      description: `Cut ${selectedClips.length} clip(s)`,
      beforeState,
      afterState,
    });

    return true;
  }, [timelineStore, undoRedoStore]);

  // Paste clips from clipboard at playhead position
  const pasteClips = useCallback(
    (targetTrackId?: string) => {
      if (!clipboardRef.current || clipboardRef.current.clips.length === 0) {
        return false;
      }

      const { clips } = clipboardRef.current;
      const playheadPosition = timelineStore.playheadPosition;

      // Record state for undo
      const beforeState = {
        tracks: timelineStore.tracks,
        duration: timelineStore.duration,
        markers: timelineStore.markers,
      };

      // Find the earliest start time among copied clips to calculate offset
      const earliestStartTime = Math.min(
        ...clips.map((clip) => clip.startTime)
      );
      const timeOffset = playheadPosition - earliestStartTime;

      const newClipIds: string[] = [];

      clips.forEach((clip) => {
        // Calculate new timing
        const newStartTime = clip.startTime + timeOffset;
        const newEndTime = clip.endTime + timeOffset;

        // Find appropriate track
        let trackId = targetTrackId;
        if (!trackId) {
          // Try to find a track of the same type as the original clip
          const originalTrack = timelineStore.tracks.find((track) =>
            track.clips.some((c) => c.id === clip.id)
          );

          if (originalTrack) {
            // Find a track of the same type
            const sameTypeTrack = timelineStore.tracks.find(
              (track) => track.type === originalTrack.type
            );
            trackId = sameTypeTrack?.id;
          }

          // Fallback to first available track
          if (!trackId && timelineStore.tracks.length > 0) {
            trackId = timelineStore.tracks[0].id;
          }
        }

        if (!trackId) return; // No track available

        // Check for overlaps and adjust position if necessary
        const track = timelineStore.getTrackById(trackId);
        if (track) {
          let adjustedStartTime = newStartTime;
          let adjustedEndTime = newEndTime;
          const clipDuration = newEndTime - newStartTime;

          // Find a non-overlapping position
          let hasOverlap = true;
          while (hasOverlap) {
            hasOverlap = track.clips.some(
              (existingClip) =>
                adjustedStartTime < existingClip.endTime &&
                adjustedEndTime > existingClip.startTime
            );

            if (hasOverlap) {
              // Move clip to the right
              adjustedStartTime += 0.1;
              adjustedEndTime = adjustedStartTime + clipDuration;
            }
          }

          // Create new clip with unique ID
          const newClip: Omit<Clip, "id"> = {
            ...clip,
            startTime: adjustedStartTime,
            endTime: adjustedEndTime,
          };

          timelineStore.addClip(trackId, newClip);

          // Store the new clip ID for selection
          const addedClip = track.clips[track.clips.length - 1];
          if (addedClip) {
            newClipIds.push(addedClip.id);
          }

          // Update timeline duration if necessary
          if (adjustedEndTime > timelineStore.duration) {
            timelineStore.setDuration(adjustedEndTime);
          }
        }
      });

      // Select the pasted clips
      if (newClipIds.length > 0) {
        timelineStore.selectClips(newClipIds);
      }

      // Record action for undo
      const afterState = {
        tracks: timelineStore.tracks,
        duration: timelineStore.duration,
        markers: timelineStore.markers,
      };

      undoRedoStore.pushAction({
        type: "paste_clips",
        description: `Paste ${clips.length} clip(s)`,
        beforeState,
        afterState,
      });

      return true;
    },
    [timelineStore, undoRedoStore]
  );

  // Duplicate selected clips
  const duplicateClips = useCallback(() => {
    const selectedClips = timelineStore.getSelectedClips();
    if (selectedClips.length === 0) return false;

    // Record state for undo
    const beforeState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    const newClipIds: string[] = [];

    selectedClips.forEach((clip) => {
      // Find the track containing this clip
      const track = timelineStore.tracks.find((t) =>
        t.clips.some((c) => c.id === clip.id)
      );

      if (!track) return;

      // Calculate new position (right after the original clip)
      const newStartTime = clip.endTime;
      const newEndTime = newStartTime + (clip.endTime - clip.startTime);

      // Check for overlaps and adjust if necessary
      let adjustedStartTime = newStartTime;
      let adjustedEndTime = newEndTime;
      const clipDuration = clip.endTime - clip.startTime;

      let hasOverlap = true;
      while (hasOverlap) {
        hasOverlap = track.clips.some(
          (existingClip) =>
            adjustedStartTime < existingClip.endTime &&
            adjustedEndTime > existingClip.startTime
        );

        if (hasOverlap) {
          adjustedStartTime += 0.1;
          adjustedEndTime = adjustedStartTime + clipDuration;
        }
      }

      // Create duplicate clip
      const duplicateClip: Omit<Clip, "id"> = {
        ...clip,
        startTime: adjustedStartTime,
        endTime: adjustedEndTime,
      };

      timelineStore.addClip(track.id, duplicateClip);

      // Find the newly added clip
      const addedClip = track.clips[track.clips.length - 1];
      if (addedClip) {
        newClipIds.push(addedClip.id);
      }

      // Update timeline duration if necessary
      if (adjustedEndTime > timelineStore.duration) {
        timelineStore.setDuration(adjustedEndTime);
      }
    });

    // Select the duplicated clips
    if (newClipIds.length > 0) {
      timelineStore.selectClips(newClipIds);
    }

    // Record action for undo
    const afterState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    undoRedoStore.pushAction({
      type: "duplicate_clips",
      description: `Duplicate ${selectedClips.length} clip(s)`,
      beforeState,
      afterState,
    });

    return true;
  }, [timelineStore, undoRedoStore]);

  // Check if clipboard has data
  const hasClipboardData = useCallback(() => {
    return (
      clipboardRef.current !== null && clipboardRef.current.clips.length > 0
    );
  }, []);

  // Get clipboard info
  const getClipboardInfo = useCallback(() => {
    if (!clipboardRef.current) return null;

    return {
      clipCount: clipboardRef.current.clips.length,
      timestamp: clipboardRef.current.timestamp,
    };
  }, []);

  return {
    copyClips,
    cutClips,
    pasteClips,
    duplicateClips,
    hasClipboardData,
    getClipboardInfo,
  };
}
