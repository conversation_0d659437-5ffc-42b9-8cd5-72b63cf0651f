import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ExportService } from "@/lib/services/export-service";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ operationId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { operationId } = await params;

    // Try to cancel the export operation
    const cancelled = await ExportService.cancelExport(operationId);

    if (!cancelled) {
      // Check if the operation exists and belongs to the user
      const exportJob = ExportService.getExportJob(operationId);

      if (!exportJob) {
        return NextResponse.json(
          { error: "Operation not found" },
          { status: 404 }
        );
      }

      if (exportJob.userId !== session.user.id) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
      }

      if (exportJob.status === "completed" || exportJob.status === "failed") {
        return NextResponse.json(
          { error: "Cannot cancel completed or failed operation" },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: "Failed to cancel operation" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      operationId,
      status: "cancelled",
      message: "Operation cancelled successfully",
    });
  } catch (error) {
    console.error("Cancel operation API error:", error);
    return NextResponse.json(
      { error: "Failed to cancel operation" },
      { status: 500 }
    );
  }
}
