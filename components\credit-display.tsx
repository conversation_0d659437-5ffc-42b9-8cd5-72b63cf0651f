import { Badge } from "@/components/ui/badge"
import { Clock } from "lucide-react"
import { useSession } from "next-auth/react"
import { useEffect } from "react"
import { useCreditStore } from "@/lib/stores/creditStore"

interface CreditDisplayProps {
  showProgress?: boolean;
  showReset?: boolean;
  variant?: "default" | "compact" | "large";
}

export function CreditDisplay({ 
  showProgress = false, 
  showReset = false,
  variant = "default" 
}: CreditDisplayProps) {
  const { data: session } = useSession();
  const { 
    credits, 
    hoursUntilReset,
    fetchCredits,
    syncWithSession,
    isLoading
  } = useCreditStore();
  
  // Sync with session immediately when component loads or session changes
  useEffect(() => {
    if (session?.user) {
      // Immediately sync with session to get the latest credits
      syncWithSession();
      
      // Setup polling to refresh credits every minute
      const intervalId = setInterval(fetchCredits, 60000);
      return () => clearInterval(intervalId);
    }
  }, [session, syncWithSession, fetchCredits]);

  // Set max credits based on user type
  useEffect(() => {
    if (session?.user?.userType) {
      const setMaxCredits = useCreditStore.getState().setMaxCredits;
      const userType = session.user.userType;
      
      if (userType === 'unlimited') {
        setMaxCredits(10);
      } else {
        setMaxCredits(5); // basic, agency, admin all get 5
      }
    }
  }, [session?.user?.userType]);
  
  if (variant === "compact") {
    return (
      <div className="flex items-center text-xs text-white bg-gray-800 px-2 py-1 rounded">
        <Badge className={`${isLoading ? 'animate-pulse' : ''} bg-purple-600 text-white mr-1`}>
          {credits}
        </Badge>
        Credits
      </div>
    );
  }
  
  if (variant === "large") {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex justify-between items-start mb-3">
          <p className="text-gray-400 text-sm">Available Credits</p>
          {showReset && hoursUntilReset !== null && (
            <div className="flex items-center text-xs text-gray-500">
              <Clock className="w-3 h-3 mr-1" />
              <span>Reset in {hoursUntilReset}h</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2 mb-3">
          <span className={`text-3xl font-bold text-white ${isLoading ? 'animate-pulse' : ''}`}>
            {credits}
          </span>
          <Badge className="bg-purple-600 text-white">
            Credits
          </Badge>
        </div>
        
        {showProgress && session?.user?.userType && (
          <div className="w-full bg-gray-700 h-2 rounded-full overflow-hidden">
            <div 
              className="h-full bg-purple-600 rounded-full transition-all duration-1000 ease-out"
              style={{ 
                width: `${
                  session.user.userType === 'unlimited' 
                    ? (credits / 10) * 100 
                    : (credits / 5) * 100
                }%` 
              }}
            />
          </div>
        )}
      </div>
    );
  }
  
  // Default variant
  return (
    <div className="bg-gray-800 rounded-lg p-3">
      <div className="flex justify-between items-start mb-2">
        <p className="text-gray-400 text-xs">Available Credits</p>
        {showReset && hoursUntilReset !== null && (
          <div className="flex items-center text-xs text-gray-500">
            <Clock className="w-3 h-3 mr-1" />
            <span>Reset in {hoursUntilReset}h</span>
          </div>
        )}
      </div>
      <div className="flex items-center gap-2 mb-2">
        <span className={`text-2xl font-bold text-white ${isLoading ? 'animate-pulse' : ''}`}>
          {credits}
        </span>
        <Badge className="bg-purple-600 text-white text-xs">
          Credits
        </Badge>
      </div>
      
      {showProgress && session?.user?.userType && (
        <div className="w-full bg-gray-700 h-1.5 rounded-full overflow-hidden">
          <div 
            className="h-full bg-purple-600 rounded-full transition-all duration-1000 ease-out"
            style={{ 
              width: `${
                session.user.userType === 'unlimited' 
                  ? (credits / 10) * 100 
                  : (credits / 5) * 100
              }%` 
            }}
          />
        </div>
      )}
    </div>
  );
} 