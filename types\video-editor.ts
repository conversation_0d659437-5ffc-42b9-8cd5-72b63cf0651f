// Video Editor Data Models
// Based on design.md specifications

export interface ProjectSettings {
  width: number;
  height: number;
  fps: number;
  duration: number;
  backgroundColor: string;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  settings: ProjectSettings;
  timeline: TimelineData;
  assets: Asset[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface TimelineData {
  tracks: Track[];
  duration: number;
  markers: Marker[];
}

export interface Track {
  id: string;
  type: "video" | "audio" | "effects";
  name: string;
  clips: Clip[];
  muted: boolean;
  locked: boolean;
  height: number;
}

export interface Clip {
  id: string;
  assetId: string;
  startTime: number;
  endTime: number;
  trimStart: number;
  trimEnd: number;
  effects: Effect[];
  properties: ClipProperties;
}

export interface ClipProperties {
  volume?: number;
  opacity?: number;
  scale?: number;
  rotation?: number;
  position?: {
    x: number;
    y: number;
  };
  filters?: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    hue?: number;
    blur?: number;
    sepia?: number;
    grayscale?: number;
    invert?: number;
    [key: string]: number | string | boolean | undefined;
  };
}

export interface Effect {
  id: string;
  type: string;
  name: string;
  parameters: Record<string, number | string | boolean>;
  enabled: boolean;
}

export interface Marker {
  id: string;
  time: number;
  label: string;
  color?: string;
}

export interface Asset {
  id: string;
  name: string;
  type: "video" | "audio" | "image" | "ai-generated";
  url: string;
  thumbnailUrl?: string;
  duration?: number;
  metadata: AssetMetadata;
  userId: string;
  projectId?: string;
  folderId?: string;
  createdAt: Date;
}

export interface AssetMetadata {
  width?: number;
  height?: number;
  fps?: number;
  fileSize: number;
  mimeType: string;
  aiPrompt?: string; // For AI-generated content
}

// Remotion Integration Models
export interface RemotionComposition {
  id: string;
  component: React.ComponentType<Record<string, unknown>>;
  durationInFrames: number;
  fps: number;
  width: number;
  height: number;
  props: Record<string, unknown>;
}

export interface TimelineComposition extends RemotionComposition {
  tracks: RemotionTrack[];
}

export interface RemotionTrack {
  id: string;
  sequences: RemotionSequence[];
}

export interface RemotionSequence {
  id: string;
  from: number;
  durationInFrames: number;
  component: React.ComponentType<Record<string, unknown>>;
  props: Record<string, unknown>;
}

// Export Settings
export interface ExportSettings {
  format: "mp4" | "webm" | "mov";
  quality: "low" | "medium" | "high" | "ultra";
  resolution: {
    width: number;
    height: number;
  };
  fps: number;
  bitrate?: number;
}

// AI Generation Settings
export interface AISettings {
  model: string;
  prompt: string;
  negativePrompt?: string;
  duration?: number;
  aspectRatio?: string;
  seed?: number;
}

// Folder organization for assets
export interface Folder {
  id: string;
  name: string;
  parentId?: string;
  createdAt: Date;
}

// Error types for better error handling
export interface MediaError {
  type: "format" | "size" | "corruption" | "network";
  message: string;
  file?: string;
}

export interface AIError {
  type: "api" | "quota" | "timeout" | "validation";
  message: string;
  requestId?: string;
}

export interface RenderError {
  type: "memory" | "codec" | "export" | "timeout";
  message: string;
  projectId?: string;
}
