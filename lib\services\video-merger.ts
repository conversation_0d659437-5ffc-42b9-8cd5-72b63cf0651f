import type { Clip, Track, Asset, ProjectSettings } from "@/types/video-editor";

export interface MergeOperation {
  id: string;
  clips: Clip[];
  outputSettings: {
    format: "mp4" | "webm" | "mov";
    quality: "low" | "medium" | "high" | "ultra";
    resolution: {
      width: number;
      height: number;
    };
    fps: number;
    audioSync: boolean;
  };
  status: "pending" | "processing" | "completed" | "failed";
  progress: number;
  outputUrl?: string;
  error?: string;
  createdAt: Date;
}

export interface BatchMergeOperation {
  id: string;
  operations: MergeOperation[];
  status: "pending" | "processing" | "completed" | "failed";
  progress: number;
  completedCount: number;
  totalCount: number;
  createdAt: Date;
}

export interface CompositeVideoSettings {
  tracks: Track[];
  projectSettings: ProjectSettings;
  outputFormat: "mp4" | "webm" | "mov";
  quality: "low" | "medium" | "high" | "ultra";
  audioSync: boolean;
  backgroundAudio?: {
    url: string;
    volume: number;
    fadeIn?: number;
    fadeOut?: number;
  };
}

export class VideoMergerService {
  private static instance: VideoMergerService;
  private mergeOperations: Map<string, MergeOperation> = new Map();
  private batchOperations: Map<string, BatchMergeOperation> = new Map();

  static getInstance(): VideoMergerService {
    if (!VideoMergerService.instance) {
      VideoMergerService.instance = new VideoMergerService();
    }
    return VideoMergerService.instance;
  }

  /**
   * Merge multiple clips into a single video
   */
  async mergeClips(
    clips: Clip[],
    assets: Asset[],
    outputSettings: MergeOperation["outputSettings"]
  ): Promise<MergeOperation> {
    const operationId = this.generateId();

    const operation: MergeOperation = {
      id: operationId,
      clips,
      outputSettings,
      status: "pending",
      progress: 0,
      createdAt: new Date(),
    };

    this.mergeOperations.set(operationId, operation);

    // Start merge process
    this.processMergeOperation(operationId, assets);

    return operation;
  }

  /**
   * Create composite video from timeline tracks
   */
  async createCompositeVideo(
    settings: CompositeVideoSettings,
    assets: Asset[]
  ): Promise<MergeOperation> {
    const operationId = this.generateId();

    // Flatten all clips from all tracks
    const allClips = settings.tracks.flatMap((track) => track.clips);

    const operation: MergeOperation = {
      id: operationId,
      clips: allClips,
      outputSettings: {
        format: settings.outputFormat,
        quality: settings.quality,
        resolution: {
          width: settings.projectSettings.width,
          height: settings.projectSettings.height,
        },
        fps: settings.projectSettings.fps,
        audioSync: settings.audioSync,
      },
      status: "pending",
      progress: 0,
      createdAt: new Date(),
    };

    this.mergeOperations.set(operationId, operation);

    // Start composite video creation
    this.processCompositeVideo(operationId, settings, assets);

    return operation;
  }

  /**
   * Batch merge multiple sets of clips
   */
  async batchMerge(
    mergeRequests: Array<{
      clips: Clip[];
      outputSettings: MergeOperation["outputSettings"];
    }>,
    assets: Asset[]
  ): Promise<BatchMergeOperation> {
    const batchId = this.generateId();

    const operations: MergeOperation[] = [];

    for (const request of mergeRequests) {
      const operation = await this.mergeClips(
        request.clips,
        assets,
        request.outputSettings
      );
      operations.push(operation);
    }

    const batchOperation: BatchMergeOperation = {
      id: batchId,
      operations,
      status: "pending",
      progress: 0,
      completedCount: 0,
      totalCount: operations.length,
      createdAt: new Date(),
    };

    this.batchOperations.set(batchId, batchOperation);

    // Monitor batch progress
    this.monitorBatchProgress(batchId);

    return batchOperation;
  }

  /**
   * Get merge operation status
   */
  getMergeOperation(id: string): MergeOperation | undefined {
    return this.mergeOperations.get(id);
  }

  /**
   * Get batch operation status
   */
  getBatchOperation(id: string): BatchMergeOperation | undefined {
    return this.batchOperations.get(id);
  }

  /**
   * Cancel merge operation
   */
  async cancelMergeOperation(id: string): Promise<void> {
    const operation = this.mergeOperations.get(id);
    if (operation && operation.status === "processing") {
      operation.status = "failed";
      operation.error = "Operation cancelled by user";

      // Cancel the actual processing (implementation depends on backend)
      await this.cancelBackendOperation(id);
    }
  }

  /**
   * Get all active merge operations
   */
  getActiveMergeOperations(): MergeOperation[] {
    return Array.from(this.mergeOperations.values()).filter(
      (op) => op.status === "processing" || op.status === "pending"
    );
  }

  /**
   * Get all active batch operations
   */
  getActiveBatchOperations(): BatchMergeOperation[] {
    return Array.from(this.batchOperations.values()).filter(
      (op) => op.status === "processing" || op.status === "pending"
    );
  }

  private async processMergeOperation(
    operationId: string,
    assets: Asset[]
  ): Promise<void> {
    const operation = this.mergeOperations.get(operationId);
    if (!operation) return;

    try {
      operation.status = "processing";

      // Validate clips and assets
      const validationResult = this.validateClipsAndAssets(
        operation.clips,
        assets
      );
      if (!validationResult.isValid) {
        throw new Error(validationResult.error);
      }

      // Convert formats if needed
      const convertedAssets = await this.convertFormats(
        operation.clips,
        assets,
        operation.outputSettings
      );

      // Synchronize audio if required
      if (operation.outputSettings.audioSync) {
        await this.synchronizeAudio(operation.clips, convertedAssets);
      }

      // Perform the actual merge
      const outputUrl = await this.performMerge(
        operation.clips,
        convertedAssets,
        operation.outputSettings,
        (progress) => {
          operation.progress = progress;
        }
      );

      operation.status = "completed";
      operation.progress = 100;
      operation.outputUrl = outputUrl;
    } catch (error) {
      operation.status = "failed";
      operation.error =
        error instanceof Error ? error.message : "Unknown error";
    }
  }

  private async processCompositeVideo(
    operationId: string,
    settings: CompositeVideoSettings,
    assets: Asset[]
  ): Promise<void> {
    const operation = this.mergeOperations.get(operationId);
    if (!operation) return;

    try {
      operation.status = "processing";

      // Create timeline composition
      const composition = await this.createTimelineComposition(
        settings.tracks,
        settings.projectSettings,
        assets
      );

      // Render composite video
      const outputUrl = await this.renderComposition(
        composition,
        operation.outputSettings,
        (progress) => {
          operation.progress = progress;
        }
      );

      operation.status = "completed";
      operation.progress = 100;
      operation.outputUrl = outputUrl;
    } catch (error) {
      operation.status = "failed";
      operation.error =
        error instanceof Error ? error.message : "Unknown error";
    }
  }

  private validateClipsAndAssets(
    clips: Clip[],
    assets: Asset[]
  ): { isValid: boolean; error?: string } {
    // Check if all clips have corresponding assets
    for (const clip of clips) {
      const asset = assets.find((a) => a.id === clip.assetId);
      if (!asset) {
        return {
          isValid: false,
          error: `Asset not found for clip ${clip.id}`,
        };
      }

      // Validate clip timing
      if (clip.startTime >= clip.endTime) {
        return {
          isValid: false,
          error: `Invalid clip timing for clip ${clip.id}`,
        };
      }

      // Validate trim settings
      if (clip.trimStart >= clip.trimEnd) {
        return {
          isValid: false,
          error: `Invalid trim settings for clip ${clip.id}`,
        };
      }
    }

    return { isValid: true };
  }

  private async convertFormats(
    clips: Clip[],
    assets: Asset[],
    outputSettings: MergeOperation["outputSettings"]
  ): Promise<Asset[]> {
    const convertedAssets: Asset[] = [];

    for (const clip of clips) {
      const asset = assets.find((a) => a.id === clip.assetId);
      if (!asset) continue;

      // Check if format conversion is needed
      const needsConversion = this.needsFormatConversion(
        asset.metadata.mimeType,
        outputSettings.format
      );

      if (needsConversion) {
        const convertedAsset = await this.convertAssetFormat(
          asset,
          outputSettings.format
        );
        convertedAssets.push(convertedAsset);
      } else {
        convertedAssets.push(asset);
      }
    }

    return convertedAssets;
  }

  private needsFormatConversion(
    inputMimeType: string,
    outputFormat: string
  ): boolean {
    const formatMap: Record<string, string[]> = {
      mp4: ["video/mp4"],
      webm: ["video/webm"],
      mov: ["video/quicktime", "video/mov"],
    };

    const supportedMimeTypes = formatMap[outputFormat] || [];
    return !supportedMimeTypes.includes(inputMimeType);
  }

  private async convertAssetFormat(
    asset: Asset,
    targetFormat: string
  ): Promise<Asset> {
    // This would typically call a backend service for format conversion
    const response = await fetch("/api/video-editor/convert", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        assetId: asset.id,
        targetFormat,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to convert asset ${asset.id} to ${targetFormat}`);
    }

    return await response.json();
  }

  private async synchronizeAudio(
    clips: Clip[],
    assets: Asset[]
  ): Promise<void> {
    // Audio synchronization logic
    const audioClips = clips.filter((clip) => {
      const asset = assets.find((a) => a.id === clip.assetId);
      return asset?.type === "audio" || asset?.type === "video";
    });

    if (audioClips.length <= 1) return;

    // Call backend service for audio synchronization
    await fetch("/api/video-editor/sync-audio", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        clips: audioClips.map((clip) => ({
          id: clip.id,
          assetId: clip.assetId,
          startTime: clip.startTime,
          endTime: clip.endTime,
          volume: clip.properties.volume || 1,
        })),
      }),
    });
  }

  private async performMerge(
    clips: Clip[],
    assets: Asset[],
    outputSettings: MergeOperation["outputSettings"],
    onProgress: (progress: number) => void
  ): Promise<string> {
    // Call backend merge service
    const response = await fetch("/api/video-editor/merge", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        clips: clips.map((clip) => ({
          id: clip.id,
          assetId: clip.assetId,
          startTime: clip.startTime,
          endTime: clip.endTime,
          trimStart: clip.trimStart,
          trimEnd: clip.trimEnd,
          properties: clip.properties,
          effects: clip.effects,
        })),
        assets: assets.map((asset) => ({
          id: asset.id,
          url: asset.url,
          type: asset.type,
          metadata: asset.metadata,
        })),
        outputSettings,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to start merge operation");
    }

    const { jobId } = await response.json();

    // Poll for progress
    return this.pollMergeProgress(jobId, onProgress);
  }

  private async createTimelineComposition(
    tracks: Track[],
    projectSettings: ProjectSettings,
    assets: Asset[]
  ): Promise<Record<string, unknown>> {
    // Create Remotion composition from timeline tracks
    const composition = {
      id: this.generateId(),
      width: projectSettings.width,
      height: projectSettings.height,
      fps: projectSettings.fps,
      durationInFrames: Math.ceil(
        projectSettings.duration * projectSettings.fps
      ),
      tracks: tracks.map((track) => ({
        id: track.id,
        type: track.type,
        clips: track.clips.map((clip) => {
          const asset = assets.find((a) => a.id === clip.assetId);
          return {
            id: clip.id,
            asset,
            startFrame: Math.floor(clip.startTime * projectSettings.fps),
            durationInFrames: Math.floor(
              (clip.endTime - clip.startTime) * projectSettings.fps
            ),
            properties: clip.properties,
            effects: clip.effects,
          };
        }),
      })),
    };

    return composition;
  }

  private async renderComposition(
    composition: Record<string, unknown>,
    outputSettings: MergeOperation["outputSettings"],
    onProgress: (progress: number) => void
  ): Promise<string> {
    // Call Remotion rendering service
    const response = await fetch("/api/video-editor/render-composition", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        composition,
        outputSettings,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to start composition rendering");
    }

    const { jobId } = await response.json();

    // Poll for progress
    return this.pollMergeProgress(jobId, onProgress);
  }

  private async pollMergeProgress(
    jobId: string,
    onProgress: (progress: number) => void
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const response = await fetch(
            `/api/video-editor/merge-status/${jobId}`
          );
          const data = await response.json();

          onProgress(data.progress);

          if (data.status === "completed") {
            resolve(data.outputUrl);
          } else if (data.status === "failed") {
            reject(new Error(data.error || "Merge operation failed"));
          } else {
            // Continue polling
            setTimeout(poll, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  private async monitorBatchProgress(batchId: string): Promise<void> {
    const batchOperation = this.batchOperations.get(batchId);
    if (!batchOperation) return;

    batchOperation.status = "processing";

    const checkProgress = () => {
      const completedOps = batchOperation.operations.filter(
        (op) => op.status === "completed" || op.status === "failed"
      );

      batchOperation.completedCount = completedOps.length;
      batchOperation.progress =
        (completedOps.length / batchOperation.totalCount) * 100;

      if (completedOps.length === batchOperation.totalCount) {
        const hasFailures = batchOperation.operations.some(
          (op) => op.status === "failed"
        );
        batchOperation.status = hasFailures ? "failed" : "completed";
        return;
      }

      // Continue monitoring
      setTimeout(checkProgress, 1000);
    };

    checkProgress();
  }

  private async cancelBackendOperation(operationId: string): Promise<void> {
    await fetch(`/api/video-editor/cancel-operation/${operationId}`, {
      method: "POST",
    });
  }

  private generateId(): string {
    return `merge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const videoMergerService = VideoMergerService.getInstance();
