"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Video, Download, Loader2, Search, SortDesc, RefreshCcw, Music } from "lucide-react"
import { useState, useEffect, useCallback, useRef } from "react"
import { VideoGeneration } from "@/lib/schema"
import { checkVideoStatusWithBackoff, VideoStatusResponse } from "@/lib/utils/videoUtils"

export default function MyVideosPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("newest");
  const [videos, setVideos] = useState<VideoGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const videosRef = useRef<VideoGeneration[]>([]);
  const checkingRef = useRef<Set<string>>(new Set()); // Track videos being checked
  
  // Update ref when videos change
  useEffect(() => {
    videosRef.current = videos;
  }, [videos]);
  
  // Fetch videos from the API
  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/video/list");
      
      if (!response.ok) {
        throw new Error("Failed to fetch videos");
      }
      
      const data = await response.json();
      setVideos(data.videos);
    } catch (err) {
      console.error("Error fetching videos:", err);
      setError("Failed to load videos. Please try again.");
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Update a single video in the list
  const updateVideoInList = useCallback((updatedVideo: VideoStatusResponse) => {
    setVideos(prevVideos => 
      prevVideos.map(video => 
        video.id === updatedVideo.id 
          ? { 
              ...video, 
              status: updatedVideo.status, 
              videoUrl: updatedVideo.videoUrl || video.videoUrl 
            } 
          : video
      )
    );
  }, []);
  
  // Skip audio generation for a video
  const handleSkipAudio = useCallback(async (videoId: string) => {
    try {
      const response = await fetch("/api/video/skip-audio", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ videoId }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to skip audio");
      }
      
      const data = await response.json();
      updateVideoInList(data);
    } catch (error) {
      console.error("Error skipping audio:", error);
      // Refresh videos to get current state
      fetchVideos();
    }
  }, [fetchVideos, updateVideoInList]);
  
  // Check status of a single pending video with exponential backoff
  const checkVideoStatus = useCallback(async (videoId: string) => {
    // Skip if already checking this video
    if (checkingRef.current.has(videoId)) {
      return;
    }
    
    try {
      // Mark as being checked
      checkingRef.current.add(videoId);
      
      // Get the current video status
      const currentVideo = videosRef.current.find(v => v.id === videoId);
      
      // Use the utility function to check status with backoff
      const result = await checkVideoStatusWithBackoff(videoId, {
        maxAttempts: 3, // Limit attempts for the background checker
        initialDelay: 5000,
        onStatusUpdate: updateVideoInList,
        // Only auto-generate audio if the video is not already processing audio
        autoGenerateAudio: currentVideo?.status !== "processing_audio"
      });
      
      // If status changed to completed or failed, update the video
      if (result && (result.status === "completed" || result.status === "failed" || result.status === "failed_audio")) {
        updateVideoInList(result);
      }
    } finally {
      // Remove from checking set when done
      checkingRef.current.delete(videoId);
    }
  }, [updateVideoInList]);
  
  // Check status of pending videos
  const checkPendingVideos = useCallback(async () => {
    // Use the ref to get the latest videos
    const currentVideos = videosRef.current;
    const pendingVideos = currentVideos.filter(video => 
      video.status === "pending" || video.status === "processing_audio"
    );
    
    if (pendingVideos.length === 0) return;
    
    setRefreshing(true);
    
    try {
      // Check each pending video (in parallel but limited by checkVideoStatus)
      await Promise.all(
        pendingVideos.map(video => checkVideoStatus(video.id))
      );
    } finally {
      setRefreshing(false);
    }
  }, [checkVideoStatus]);
  
  // Manual refresh of all videos
  const handleRefresh = useCallback(async () => {
    // For manual refresh, just fetch all videos
    await fetchVideos();
  }, [fetchVideos]);
  
  // Initial fetch
  useEffect(() => {
    fetchVideos();
    
    // Set up interval to check pending videos
    const interval = setInterval(checkPendingVideos, 15000); // Check every 15 seconds
    
    return () => clearInterval(interval);
  }, [fetchVideos, checkPendingVideos]);
  
  // Filter videos based on search term
  const filteredVideos = videos.filter(video => 
    video.prompt.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Sort videos
  const sortedVideos = [...filteredVideos].sort((a, b) => {
    if (sortBy === "newest") {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } else if (sortBy === "oldest") {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    } else {
      return 0;
    }
  });

  return (
    <div className="w-full">
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Video className="w-5 h-5 text-purple-400" /> My Videos
          </CardTitle>
          <CardDescription className="text-gray-400">
            Manage and view your generated videos.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
              <Input 
                placeholder="Search videos..." 
                className="bg-gray-800 border-gray-700 text-white pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2 sm:w-auto w-full justify-between">
              <div className="flex items-center gap-2">
                <SortDesc className="text-gray-400 h-4 w-4" />
                <select 
                  className="bg-gray-800 border border-gray-700 rounded-md text-gray-300 text-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-purple-600"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                >
                  <option value="newest">Newest</option>
                  <option value="oldest">Oldest</option>
                </select>
              </div>
              <Button 
                variant="outline" 
                size="icon" 
                className="text-gray-300 border-gray-700 hover:bg-gray-800"
                onClick={handleRefresh}
                disabled={loading || refreshing}
              >
                <RefreshCcw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
          
          {/* Loading State */}
          {loading && (
            <div className="text-center py-12">
              <Loader2 className="h-12 w-12 text-purple-500 mx-auto mb-4 animate-spin" />
              <h3 className="text-gray-300 font-medium">Loading videos...</h3>
            </div>
          )}
          
          {/* Error State */}
          {error && !loading && (
            <div className="text-center py-12">
              <p className="text-red-400">{error}</p>
              <Button 
                variant="outline" 
                className="mt-4 border-gray-700 text-gray-300"
                onClick={() => fetchVideos()}
              >
                Try Again
              </Button>
            </div>
          )}
          
          {/* Videos Grid */}
          {!loading && !error && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {sortedVideos.map((video) => (
                <div key={video.id} className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden">
                  <div className="aspect-video bg-gray-900 relative flex items-center justify-center">
                    {video.status === "completed" && video.videoUrl ? (
                      <video 
                        src={video.videoUrl} 
                        controls
                        className="w-full h-full"
                      />
                    ) : video.status === "pending" || video.status === "processing_audio" ? (
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                          <Loader2 className="h-8 w-8 text-purple-500 animate-spin" />
                          {video.status === "processing_audio" && (
                            <Music className="h-5 w-5 text-blue-400 ml-2" />
                          )}
                        </div>
                        <p className="text-gray-400 text-sm">
                          {video.status === "pending" ? "Generating video..." : "Adding audio to video..."}
                        </p>
                        {video.status === "processing_audio" && (
                          <div className="mt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs border-gray-700 bg-purple-600 hover:bg-purple-700"
                              onClick={() => handleSkipAudio(video.id)}
                            >
                              Skip audio
                            </Button>
                          </div>
                        )}
                      </div>
                    ) : video.status === "failed_audio" ? (
                      <div className="text-center">
                        <p className="text-amber-400 text-sm mb-2">Video generated but audio failed</p>
                        {video.videoUrl && (
                          <video 
                            src={video.videoUrl} 
                            controls
                            className="w-full h-full"
                          />
                        )}
                      </div>
                    ) : (
                      <div className="text-center">
                        <p className="text-red-400 text-sm">Generation failed</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-4">
                    <h3 className="font-semibold text-white mb-1 truncate">{video.prompt.substring(0, 50)}...</h3>
                    <p className="text-gray-400 text-sm mb-3">
                      {new Date(video.createdAt).toLocaleString()}
                      {video.imageUrl && (
                        <span className="ml-2 bg-blue-900/50 text-blue-400 text-xs px-2 py-0.5 rounded-full">
                          Image to Video
                        </span>
                      )}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        video.status === "completed" ? "bg-green-900/50 text-green-400" :
                        video.status === "pending" ? "bg-yellow-900/50 text-yellow-400" :
                        video.status === "processing_audio" ? "bg-blue-900/50 text-blue-400" :
                        video.status === "failed_audio" ? "bg-amber-900/50 text-amber-400" :
                        "bg-red-900/50 text-red-400"
                      }`}>
                        {video.status === "processing_audio" ? "Adding Audio" : 
                         video.status === "failed_audio" ? "Audio Failed" : 
                         video.status}
                      </span>
                      
                      {video.status === "completed" && video.videoUrl && (
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="text-gray-400 hover:text-white h-8 w-8"
                          onClick={() => {
                            if (video.videoUrl) {
                              window.open(video.videoUrl, "_blank");
                            }
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {!loading && !error && sortedVideos.length === 0 && (
            <div className="text-center py-12">
              <Video className="h-12 w-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-gray-300 font-medium mb-1">No videos found</h3>
              <p className="text-gray-500">Try creating a new video in the Video Generator</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 