"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Type, Loader2, Lock } from "lucide-react"
import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRouter } from "next/navigation"
import { useCreditStore } from "@/lib/stores/creditStore"

export default function VideoPage() {
  const router = useRouter();
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("worst quality, inconsistent motion, blurry, jittery, distorted");
  const [resolution, setResolution] = useState("720p");
  const [aspectRatio, setAspectRatio] = useState("16:9");
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generationStatus, setGenerationStatus] = useState<string | null>(null);
  
  // Get credits from global store
  const { credits, fetchCredits, decrementCredit } = useCreditStore();
  const hasCredits = credits > 0;
  
  // Fetch credits when component mounts
  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);
  
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError("Please enter a prompt");
      return;
    }
    
    if (!hasCredits) {
      setError("You don't have enough credits to generate a video. Credits reset daily.");
      return;
    }
    
    try {
      setIsGenerating(true);
      setError(null);
      setGenerationStatus("Submitting request...");
      
      // Submit the request
      const response = await fetch("/api/video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          negative_prompt: negativePrompt,
          resolution,
          aspect_ratio: aspectRatio,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate video");
      }
      
      // Immediately decrement credit in the UI for responsive feedback
      decrementCredit();
      
      // Redirect to the videos page
      router.push("/dashboard/myvideos");
      router.refresh();
    } catch (err) {
      console.error("Error generating video:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
      
      // Refresh credits to get current state from server
      fetchCredits();
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="w-full">
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Type className="w-5 h-5 text-purple-400" /> Text to Video
          </CardTitle>
          <CardDescription className="text-gray-400">
            Describe your video and generate it using AI.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label htmlFor="prompt" className="text-gray-200 font-medium mb-1">
              Video Prompt
            </Label>
            <Textarea
              id="prompt"
              placeholder="Describe your video..."
              className="min-h-28 bg-gray-800 border-gray-700 text-white"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="negativePrompt" className="text-gray-200 font-medium mb-1">
              Negative Prompt
            </Label>
            <Textarea
              id="negativePrompt"
              placeholder="What to avoid in the generated video..."
              className="min-h-20 bg-gray-800 border-gray-700 text-white"
              value={negativePrompt}
              onChange={(e) => setNegativePrompt(e.target.value)}
            />
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-1/2">
              <Label className="text-gray-200 font-medium mb-1">Resolution</Label>
              <Select value={resolution} onValueChange={setResolution}>
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select resolution" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="480p">480p</SelectItem>
                  <SelectItem value="720p">720p</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full sm:w-1/2">
              <Label className="text-gray-200 font-medium mb-1">Aspect Ratio</Label>
              <Select value={aspectRatio} onValueChange={setAspectRatio}>
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select aspect ratio" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="16:9">16:9 (Landscape)</SelectItem>
                  <SelectItem value="1:1">1:1 (Square)</SelectItem>
                  <SelectItem value="9:16">9:16 (Portrait)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <Button 
            className="w-full bg-purple-600 hover:bg-purple-700 text-white text-base h-12"
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim() || !hasCredits}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" /> Generating...
              </>
            ) : !hasCredits ? (
              <>
                <Lock className="w-5 h-5 mr-2" /> Insufficient Credits
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" /> Generate Video
              </>
            )}
          </Button>
          
          {error && (
            <div className="mt-3 p-3 bg-red-500/20 border border-red-500/50 rounded-md">
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          )}
          
          {isGenerating && (
            <div className="mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin text-purple-400" />
                <span className="text-gray-300">{generationStatus || "Processing your request..."}</span>
              </div>
              <div className="mt-2 h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                <div className="h-full bg-purple-600 rounded-full animate-pulse" style={{ width: "60%" }}></div>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                Video generation may take several minutes. You&apos;ll be redirected to your videos page when complete.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 