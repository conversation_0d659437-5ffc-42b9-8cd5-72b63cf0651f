"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { <PERSON>rkles, ImageIcon, Loader2, Download } from "lucide-react"
import { useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Image from "next/image"

export default function TextToImagePage() {
  const [prompt, setPrompt] = useState("");
  const [aspectRatio, setAspectRatio] = useState("1:1");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState("");
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError("Please enter a prompt");
      return;
    }
    
    try {
      setIsGenerating(true);
      setError("");
      setImageUrl(null);
      
      const response = await fetch("/api/text-to-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          aspectRatio,
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to generate image");
      }
      
      const data = await response.json();
      
      // Create a proxied URL for display and download
      const proxiedUrl = `/api/proxy-image?url=${encodeURIComponent(data.imageUrl)}`;
      setImageUrl(proxiedUrl);
      
    } catch (err) {
      console.error("Error generating image:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!imageUrl) return;
    
    try {
      setIsDownloading(true);
      setError("");
      
      // Use our proxy endpoint to download the image
      const response = await fetch(imageUrl);
      
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = `generated-image-${new Date().getTime()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (err) {
      console.error("Error downloading image:", err);
      setError("Failed to download image");
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="w-full">
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <ImageIcon className="w-5 h-5 text-purple-400" /> Text to Image
          </CardTitle>
          <CardDescription className="text-gray-400">
            Generate images from text descriptions using AI.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label htmlFor="prompt" className="text-gray-200 font-medium mb-1">
              Image Prompt
            </Label>
            <Textarea
              id="prompt"
              placeholder="Describe the image you want to generate..."
              className="min-h-28 bg-gray-800 border-gray-700 text-white"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              required
            />
          </div>
          
          <div className="w-full sm:w-1/2">
            <Label className="text-gray-200 font-medium mb-1">Aspect Ratio</Label>
            <Select value={aspectRatio} onValueChange={setAspectRatio}>
              <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                <SelectValue placeholder="Select aspect ratio" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700 text-white">
                <SelectItem value="1:1">1:1 (Square)</SelectItem>
                <SelectItem value="4:3">4:3 (Landscape)</SelectItem>
                <SelectItem value="3:4">3:4 (Portrait)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {error && (
            <div className="text-red-500 text-sm">{error}</div>
          )}
          
          <Button 
            className="w-full bg-purple-600 hover:bg-purple-700 text-white text-base h-12"
            onClick={handleGenerate}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" /> Generating...
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" /> Generate Image
              </>
            )}
          </Button>
          
          {isGenerating && (
            <div className="mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin text-purple-400" />
                <span className="text-gray-300">Processing your request...</span>
              </div>
              <div className="mt-2 h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                <div className="h-full bg-purple-600 rounded-full animate-pulse" style={{ width: "60%" }}></div>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                Image generation may take several seconds.
              </p>
            </div>
          )}
          
          {imageUrl && !isGenerating && (
            <div className="mt-6 space-y-4">
              <div className="border border-gray-700 rounded-lg overflow-hidden bg-gray-800 relative">
                <div className="aspect-square relative w-full">
                  <Image 
                    src={imageUrl}
                    alt="Generated image" 
                    fill
                    className="object-contain"
                    unoptimized
                  />
                </div>
              </div>
              
              <Button 
                variant="outline" 
                className="w-full border-gray-700 bg-purple-600 hover:bg-purple-700 text-white"
                onClick={handleDownload}
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" /> Downloading...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" /> Download Image
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 