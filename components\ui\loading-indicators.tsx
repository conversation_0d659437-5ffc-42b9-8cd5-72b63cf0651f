"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Loader2, X, AlertCircle } from "lucide-react";
import { useLoadingService, type LoadingState } from "@/lib/services/loading-service";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function LoadingSpinner({ size = "md", className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8",
  };

  return (
    <Loader2 
      className={cn("animate-spin", sizeClasses[size], className)} 
    />
  );
}

interface LoadingOverlayProps {
  loading: boolean;
  message?: string;
  children: React.ReactNode;
  className?: string;
}

export function LoadingOverlay({ 
  loading, 
  message = "Loading...", 
  children, 
  className 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {loading && (
        <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="flex flex-col items-center space-y-2">
            <LoadingSpinner size="lg" />
            <p className="text-sm text-gray-600 dark:text-gray-400">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface ProgressIndicatorProps {
  progress: number;
  message?: string;
  showPercentage?: boolean;
  className?: string;
}

export function ProgressIndicator({ 
  progress, 
  message, 
  showPercentage = true,
  className 
}: ProgressIndicatorProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {message && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-700 dark:text-gray-300">{message}</span>
          {showPercentage && (
            <span className="text-gray-500 dark:text-gray-400">
              {Math.round(progress)}%
            </span>
          )}
        </div>
      )}
      <Progress value={progress} className="h-2" />
    </div>
  );
}

interface LoadingStateCardProps {
  loadingState: LoadingState;
  onCancel?: (id: string) => void;
  className?: string;
}

export function LoadingStateCard({ 
  loadingState, 
  onCancel, 
  className 
}: LoadingStateCardProps) {
  const handleCancel = () => {
    if (loadingState.onCancel) {
      loadingState.onCancel();
    }
    if (onCancel) {
      onCancel(loadingState.id);
    }
  };

  return (
    <div className={cn(
      "flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm",
      className
    )}>
      <LoadingSpinner size="sm" />
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {loadingState.message}
        </p>
        {loadingState.progress !== undefined && (
          <div className="mt-1">
            <ProgressIndicator 
              progress={loadingState.progress} 
              showPercentage={false}
            />
          </div>
        )}
      </div>

      {loadingState.cancellable && (
        <Button
          size="sm"
          variant="ghost"
          onClick={handleCancel}
          className="flex-shrink-0"
        >
          <X className="w-4 h-4" />
        </Button>
      )}
    </div>
  );
}

export function GlobalLoadingIndicator() {
  const { loadingStates, stopLoading } = useLoadingService();

  if (loadingStates.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 max-w-sm space-y-2">
      {loadingStates.map((loadingState) => (
        <LoadingStateCard
          key={loadingState.id}
          loadingState={loadingState}
          onCancel={stopLoading}
        />
      ))}
    </div>
  );
}

interface InlineLoadingProps {
  loading: boolean;
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function InlineLoading({ 
  loading, 
  message, 
  size = "md", 
  className 
}: InlineLoadingProps) {
  if (!loading) return null;

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <LoadingSpinner size={size} />
      {message && (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {message}
        </span>
      )}
    </div>
  );
}

interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export function LoadingButton({ 
  loading = false, 
  loadingText, 
  children, 
  disabled,
  className,
  ...props 
}: LoadingButtonProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={className}
      {...props}
    >
      {loading && <LoadingSpinner size="sm" className="mr-2" />}
      {loading && loadingText ? loadingText : children}
    </Button>
  );
}

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
}

export function LoadingSkeleton({ className, lines = 3 }: LoadingSkeletonProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            "h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse",
            i === lines - 1 && "w-3/4" // Last line is shorter
          )}
        />
      ))}
    </div>
  );
}

interface ErrorStateProps {
  error: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorState({ error, onRetry, className }: ErrorStateProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center p-6 text-center space-y-3",
      className
    )}>
      <AlertCircle className="w-12 h-12 text-red-500" />
      <div>
        <h3 className="font-medium text-gray-900 dark:text-gray-100">
          Something went wrong
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {error}
        </p>
      </div>
      {onRetry && (
        <Button onClick={onRetry} variant="outline" size="sm">
          Try Again
        </Button>
      )}
    </div>
  );
}
