"use client";

import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useMediaImportWithErrorHandling } from "@/lib/hooks/use-media-import-with-error-handling";
import {
  LoadingOverlay,
  InlineLoading,
} from "@/components/ui/loading-indicators";
import { SectionErrorBoundary } from "@/components/ui/error-boundary";
import {
  Upload,
  Search,
  Filter,
  FolderPlus,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Loader2,
  Wand2,
  FolderOpen,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AssetLibrary } from "./asset-library";
import { FolderNavigation } from "./folder-navigation";
import { FileUploadZone } from "./file-upload-zone";
import { CreateFolderDialog } from "./create-folder-dialog";
import { AIVideoGenerator } from "./ai-video-generator";

interface ClipManagerProps {
  className?: string;
}

export function ClipManager({ className }: ClipManagerProps) {
  const assetStore = useAssetStore();
  const projectStore = useProjectStore();
  const { importMultipleFiles, isImporting } =
    useMediaImportWithErrorHandling();

  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(
    async (files: FileList) => {
      const fileArray = Array.from(files);

      try {
        await importMultipleFiles(fileArray, {
          maxFileSize: 500 * 1024 * 1024, // 500MB
          retryOnFailure: true,
        });
      } catch (error) {
        console.error("Failed to import files:", error);
      }
    },
    [importMultipleFiles]
  );

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      handleFileSelect(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = "";
  };

  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleSortChange = (sortBy: "name" | "date" | "type" | "size") => {
    const currentOrder = assetStore.sortOrder;
    const newOrder =
      assetStore.sortBy === sortBy && currentOrder === "asc" ? "desc" : "asc";
    assetStore.setSorting(sortBy, newOrder);
  };

  const handleVideoGenerated = useCallback(() => {
    // Refresh assets to show the new AI-generated video
    assetStore.loadAssets(projectStore.currentProject?.id);

    // Optionally switch to the assets tab to show the new video
    // This could be enhanced with a notification or highlight
  }, [assetStore, projectStore.currentProject?.id]);

  return (
    <SectionErrorBoundary sectionName="Asset Manager" className={className}>
      <LoadingOverlay
        loading={isImporting()}
        message="Importing media files..."
      >
        <div className={`bg-gray-900 rounded-lg p-4 space-y-4 ${className}`}>
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <h3 className="text-xl font-bold text-white">Asset Manager</h3>
              <InlineLoading loading={isImporting()} size="sm" />
            </div>
          </div>

          {/* Tabbed Interface */}
          <Tabs defaultValue="assets" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-gray-800">
              <TabsTrigger
                value="assets"
                className="data-[state=active]:bg-gray-700"
              >
                <FolderOpen className="w-4 h-4 mr-2" />
                Assets
              </TabsTrigger>
              <TabsTrigger
                value="ai-generator"
                className="data-[state=active]:bg-purple-600"
              >
                <Wand2 className="w-4 h-4 mr-2" />
                AI Generator
              </TabsTrigger>
            </TabsList>

            <TabsContent value="assets" className="space-y-4 mt-4">
              {/* Asset Management Controls */}
              <div className="flex items-center gap-2">
                <Button
                  onClick={triggerFileUpload}
                  disabled={assetStore.isImporting}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {assetStore.isImporting ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  Upload
                </Button>
                <Button
                  onClick={() => setShowCreateFolder(true)}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <FolderPlus className="w-4 h-4 mr-2" />
                  New Folder
                </Button>
              </div>

              {/* Search and Filter Bar */}
              <div className="flex items-center gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search assets..."
                    value={assetStore.searchQuery}
                    onChange={(e) => assetStore.setSearchQuery(e.target.value)}
                    className="pl-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  />
                </div>

                {/* Filter Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      <Filter className="w-4 h-4 mr-2" />
                      {assetStore.filterType === "all"
                        ? "All Types"
                        : assetStore.filterType}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-gray-800 border-gray-600">
                    <DropdownMenuItem
                      onClick={() => assetStore.setFilterType("all")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      All Types
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => assetStore.setFilterType("video")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Video
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => assetStore.setFilterType("audio")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Audio
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => assetStore.setFilterType("image")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Image
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => assetStore.setFilterType("ai-generated")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      AI Generated
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Sort Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      {assetStore.sortOrder === "asc" ? (
                        <SortAsc className="w-4 h-4 mr-2" />
                      ) : (
                        <SortDesc className="w-4 h-4 mr-2" />
                      )}
                      Sort
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-gray-800 border-gray-600">
                    <DropdownMenuItem
                      onClick={() => handleSortChange("name")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Name
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleSortChange("date")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Date
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleSortChange("type")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Type
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleSortChange("size")}
                      className="text-gray-300 hover:bg-gray-700"
                    >
                      Size
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* View Mode Toggle */}
                <div className="flex border border-gray-600 rounded-md">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none border-r border-gray-600"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Folder Navigation */}
              <FolderNavigation />

              {/* File Upload Zone */}
              <FileUploadZone onFilesSelected={handleFileSelect} />

              {/* Asset Library */}
              <AssetLibrary viewMode={viewMode} />
            </TabsContent>

            <TabsContent value="ai-generator" className="mt-4">
              <AIVideoGenerator
                className="h-full"
                onVideoGenerated={handleVideoGenerated}
              />
            </TabsContent>
          </Tabs>

          {/* Hidden File Input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            multiple
            accept="video/*,audio/*,image/*"
            className="hidden"
          />

          {/* Create Folder Dialog */}
          <CreateFolderDialog
            open={showCreateFolder}
            onOpenChange={setShowCreateFolder}
          />
        </div>
      </LoadingOverlay>
    </SectionErrorBoundary>
  );
}
