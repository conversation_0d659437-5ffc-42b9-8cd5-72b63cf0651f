"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import { signIn } from "next-auth/react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useCreditStore } from "@/lib/stores/creditStore";
import { motion } from "framer-motion";

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export function LoginForm() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const { syncWithSession } = useCreditStore();

  // Get the callback URL from the search params or default to dashboard
  const callbackUrl = searchParams?.get("callbackUrl") || "/dashboard";

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (data: LoginFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        setError("Invalid email or password");
        setIsLoading(false);
        return;
      }

      // Sync the credits with the session after successful login
      await syncWithSession();

      // Redirect using window.location for a full page reload
      window.location.href = callbackUrl;
    } catch (error) {
      setError("An error occurred. Please try again.");
      console.error(error);
      setIsLoading(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.1,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.form
      className="space-y-6"
      onSubmit={handleSubmit(onSubmit)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="space-y-5">
        <motion.div className="space-y-2" variants={itemVariants}>
          <Label htmlFor="email" className="text-sm font-medium text-white/90">
            Email address
          </Label>
          <div className="relative group">
            <motion.div
              className="absolute left-3 top-1/2 transform -translate-y-1/2"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Mail className="w-5 h-5 text-white/60 group-focus-within:text-purple-300 transition-colors duration-200" />
            </motion.div>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              className="pl-10 h-14 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-purple-400 focus:ring-purple-400/50 backdrop-blur-sm transition-all duration-300 hover:bg-white/15"
              disabled={isLoading}
              {...register("email")}
            />
          </div>
          {errors.email && (
            <motion.p
              className="text-sm text-red-300"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {errors.email.message}
            </motion.p>
          )}
        </motion.div>

        <motion.div className="space-y-2" variants={itemVariants}>
          <Label
            htmlFor="password"
            className="text-sm font-medium text-white/90"
          >
            Password
          </Label>
          <div className="relative group">
            <motion.div
              className="absolute left-3 top-1/2 transform -translate-y-1/2"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Lock className="w-5 h-5 text-white/60 group-focus-within:text-purple-300 transition-colors duration-200" />
            </motion.div>
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="Enter your password"
              className="pl-10 pr-12 h-14 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-purple-400 focus:ring-purple-400/50 backdrop-blur-sm transition-all duration-300 hover:bg-white/15"
              disabled={isLoading}
              {...register("password")}
            />
            <motion.button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg hover:bg-white/10 transition-colors duration-200"
              onClick={togglePasswordVisibility}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {showPassword ? (
                <Eye className="w-5 h-5 text-white/60 hover:text-white/80" />
              ) : (
                <EyeOff className="w-5 h-5 text-white/60 hover:text-white/80" />
              )}
            </motion.button>
          </div>
          {errors.password && (
            <motion.p
              className="text-sm text-red-300"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {errors.password.message}
            </motion.p>
          )}
        </motion.div>
      </div>

      <motion.div
        className="flex items-center justify-between"
        variants={itemVariants}
      >
        <motion.div
          className="flex items-center space-x-3"
          whileHover={{ scale: 1.02 }}
        >
          <input
            id="remember"
            type="checkbox"
            className="w-4 h-4 text-purple-500 bg-white/10 border-white/30 rounded focus:ring-purple-400 focus:ring-offset-0 backdrop-blur-sm"
          />
          <Label
            htmlFor="remember"
            className="text-sm text-white/70 cursor-pointer"
          >
            Remember me
          </Label>
        </motion.div>
        <Link href="/forgot-password">
          <motion.span
            className="text-sm text-purple-300 hover:text-purple-200 font-medium transition-colors duration-200 cursor-pointer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Forgot password?
          </motion.span>
        </Link>
      </motion.div>

      {error && (
        <motion.div
          className="rounded-xl bg-red-500/20 backdrop-blur-sm border border-red-400/30 p-4"
          initial={{ opacity: 0, scale: 0.9, y: -10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-sm text-red-200">{error}</p>
        </motion.div>
      )}

      <motion.div variants={itemVariants}>
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full h-14 text-base font-semibold bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 border-0 rounded-xl shadow-lg hover:shadow-purple-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <motion.div
                  className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>Signing in...</span>
              </div>
            ) : (
              "Sign In"
            )}
          </Button>
        </motion.div>
      </motion.div>
    </motion.form>
  );
}
