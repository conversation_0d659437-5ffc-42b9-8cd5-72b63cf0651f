import type { AISettings } from "@/types/video-editor";

export interface AIVideoPreset {
  id: string;
  name: string;
  description: string;
  prompt: string;
  settings: Partial<AISettings>;
  category:
    | "cinematic"
    | "animation"
    | "nature"
    | "abstract"
    | "commercial"
    | "social";
  thumbnail?: string;
  tags: string[];
}

export const AI_VIDEO_PRESETS: AIVideoPreset[] = [
  // Cinematic Presets
  {
    id: "cinematic-landscape",
    name: "Cinematic Landscape",
    description:
      "Sweeping cinematic shots of natural landscapes with dramatic lighting",
    prompt:
      "A cinematic aerial shot of a vast mountain landscape at golden hour, with dramatic clouds and warm lighting, smooth camera movement",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "16:9",
    },
    category: "cinematic",
    tags: ["landscape", "aerial", "golden hour", "mountains"],
  },
  {
    id: "cinematic-city",
    name: "Urban Cinematic",
    description: "Dynamic city shots with cinematic camera movements",
    prompt:
      "A cinematic shot of a bustling city at night, with neon lights reflecting on wet streets, smooth camera pan",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 10,
      aspectRatio: "16:9",
    },
    category: "cinematic",
    tags: ["city", "night", "neon", "urban"],
  },
  {
    id: "cinematic-portrait",
    name: "Cinematic Portrait",
    description: "Professional portrait shots with cinematic lighting",
    prompt:
      "A cinematic portrait shot with dramatic lighting, shallow depth of field, and subtle camera movement",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 6,
      aspectRatio: "16:9",
    },
    category: "cinematic",
    tags: ["portrait", "dramatic lighting", "shallow focus"],
  },

  // Animation Presets
  {
    id: "smooth-animation",
    name: "Smooth Animation",
    description: "Fluid animated sequences with smooth transitions",
    prompt:
      "A smooth animated sequence with flowing particles and gentle transitions, vibrant colors",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 6,
      aspectRatio: "16:9",
    },
    category: "animation",
    tags: ["particles", "smooth", "transitions", "colorful"],
  },
  {
    id: "morphing-shapes",
    name: "Morphing Shapes",
    description: "Geometric shapes transforming and morphing smoothly",
    prompt:
      "Geometric shapes morphing and transforming into each other with smooth animations and vibrant colors",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "16:9",
    },
    category: "animation",
    tags: ["geometric", "morphing", "shapes", "transformation"],
  },
  {
    id: "liquid-motion",
    name: "Liquid Motion",
    description: "Fluid liquid-like animations with organic movement",
    prompt:
      "Liquid-like fluid motion with organic shapes flowing and merging, iridescent colors",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 10,
      aspectRatio: "16:9",
    },
    category: "animation",
    tags: ["liquid", "fluid", "organic", "iridescent"],
  },

  // Nature Presets
  {
    id: "nature-timelapse",
    name: "Nature Timelapse",
    description: "Time-lapse footage of natural phenomena",
    prompt:
      "A beautiful timelapse of clouds moving over a serene lake with mountains in the background",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 10,
      aspectRatio: "16:9",
    },
    category: "nature",
    tags: ["timelapse", "clouds", "lake", "mountains"],
  },
  {
    id: "ocean-waves",
    name: "Ocean Waves",
    description: "Peaceful ocean waves with natural movement",
    prompt:
      "Gentle ocean waves rolling onto a pristine beach, with soft sunlight and natural movement",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 12,
      aspectRatio: "16:9",
    },
    category: "nature",
    tags: ["ocean", "waves", "beach", "peaceful"],
  },
  {
    id: "forest-breeze",
    name: "Forest Breeze",
    description: "Peaceful forest scenes with gentle wind movement",
    prompt:
      "A peaceful forest with sunlight filtering through leaves, gentle breeze moving the branches",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "16:9",
    },
    category: "nature",
    tags: ["forest", "sunlight", "breeze", "peaceful"],
  },

  // Abstract Presets
  {
    id: "abstract-motion",
    name: "Abstract Motion",
    description: "Abstract geometric patterns and motion graphics",
    prompt:
      "Abstract geometric shapes morphing and flowing with vibrant colors and smooth transitions",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "16:9",
    },
    category: "abstract",
    tags: ["geometric", "abstract", "vibrant", "motion graphics"],
  },
  {
    id: "color-explosion",
    name: "Color Explosion",
    description: "Dynamic color explosions and paint-like effects",
    prompt:
      "Explosive bursts of vibrant colors mixing and flowing like paint in water, dynamic movement",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 6,
      aspectRatio: "16:9",
    },
    category: "abstract",
    tags: ["colors", "explosion", "paint", "dynamic"],
  },
  {
    id: "digital-glitch",
    name: "Digital Glitch",
    description: "Digital glitch effects with cyberpunk aesthetics",
    prompt:
      "Digital glitch effects with neon colors, data streams, and cyberpunk aesthetic",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "16:9",
    },
    category: "abstract",
    tags: ["glitch", "digital", "cyberpunk", "neon"],
  },

  // Commercial Presets
  {
    id: "product-showcase",
    name: "Product Showcase",
    description: "Professional product presentation videos",
    prompt:
      "A sleek product showcase with elegant lighting and smooth camera movements, professional presentation",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 6,
      aspectRatio: "16:9",
    },
    category: "commercial",
    tags: ["product", "professional", "elegant", "showcase"],
  },
  {
    id: "corporate-intro",
    name: "Corporate Intro",
    description: "Professional corporate introduction sequences",
    prompt:
      "A professional corporate introduction with clean lines, modern design, and smooth transitions",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "16:9",
    },
    category: "commercial",
    tags: ["corporate", "professional", "clean", "modern"],
  },
  {
    id: "brand-animation",
    name: "Brand Animation",
    description: "Dynamic brand animations and logo reveals",
    prompt:
      "Dynamic brand animation with logo reveal, modern typography, and professional presentation",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 6,
      aspectRatio: "16:9",
    },
    category: "commercial",
    tags: ["brand", "logo", "typography", "reveal"],
  },

  // Social Media Presets
  {
    id: "social-vertical",
    name: "Social Media Vertical",
    description: "Vertical format videos optimized for social media",
    prompt:
      "Dynamic vertical video with engaging visuals, perfect for social media platforms",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 6,
      aspectRatio: "9:16",
    },
    category: "social",
    tags: ["vertical", "social media", "engaging", "mobile"],
  },
  {
    id: "social-square",
    name: "Social Media Square",
    description: "Square format videos for Instagram and other platforms",
    prompt:
      "Engaging square format video with vibrant colors and dynamic movement, social media optimized",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 8,
      aspectRatio: "1:1",
    },
    category: "social",
    tags: ["square", "instagram", "vibrant", "social"],
  },
  {
    id: "trending-style",
    name: "Trending Style",
    description: "Videos in current trending styles and aesthetics",
    prompt:
      "Trendy video with current popular aesthetics, engaging visuals, and modern style",
    settings: {
      model: "fal-ai/ltx-video",
      duration: 10,
      aspectRatio: "16:9",
    },
    category: "social",
    tags: ["trending", "modern", "popular", "aesthetic"],
  },
];

// Helper functions for working with presets
export function getPresetsByCategory(
  category: AIVideoPreset["category"]
): AIVideoPreset[] {
  return AI_VIDEO_PRESETS.filter((preset) => preset.category === category);
}

export function getPresetById(id: string): AIVideoPreset | undefined {
  return AI_VIDEO_PRESETS.find((preset) => preset.id === id);
}

export function searchPresets(query: string): AIVideoPreset[] {
  const lowercaseQuery = query.toLowerCase();
  return AI_VIDEO_PRESETS.filter(
    (preset) =>
      preset.name.toLowerCase().includes(lowercaseQuery) ||
      preset.description.toLowerCase().includes(lowercaseQuery) ||
      preset.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
  );
}

export function getPresetCategories(): AIVideoPreset["category"][] {
  return [
    "cinematic",
    "animation",
    "nature",
    "abstract",
    "commercial",
    "social",
  ];
}

export function getRandomPreset(): AIVideoPreset {
  const randomIndex = Math.floor(Math.random() * AI_VIDEO_PRESETS.length);
  return AI_VIDEO_PRESETS[randomIndex];
}

export function getPresetsByAspectRatio(aspectRatio: string): AIVideoPreset[] {
  return AI_VIDEO_PRESETS.filter(
    (preset) => preset.settings.aspectRatio === aspectRatio
  );
}

export function getPresetsByDuration(
  minDuration: number,
  maxDuration: number
): AIVideoPreset[] {
  return AI_VIDEO_PRESETS.filter((preset) => {
    const duration = preset.settings.duration || 8;
    return duration >= minDuration && duration <= maxDuration;
  });
}
