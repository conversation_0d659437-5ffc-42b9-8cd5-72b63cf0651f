import { auth, signOut } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { compare } from "bcrypt";

export async function DELETE(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await req.json();
    const { password } = body;
    
    if (!password) {
      return NextResponse.json({ error: "Password is required to delete account" }, { status: 400 });
    }
    
    // Get the user from the database
    const user = await db.query.users.findFirst({
      where: (users) => eq(users.id, session.user.id)
    });
    
    if (!user || !user.password) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Verify the password
    const isPasswordValid = await compare(password, user.password);
    
    if (!isPasswordValid) {
      return NextResponse.json({ error: "Incorrect password" }, { status: 400 });
    }
    
    // Delete the user from the database
    await db.delete(users).where(eq(users.id, session.user.id));
    
    // Sign out the user
    await signOut({ redirect: false });
    
    return NextResponse.json({ 
      success: true,
      message: "Account deleted successfully",
    });
    
  } catch (error) {
    console.error("Error deleting account:", error);
    return NextResponse.json({ error: "Failed to delete account" }, { status: 500 });
  }
} 