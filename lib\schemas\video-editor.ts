// Zod validation schemas for video editor data models
import { z } from "zod";

// Project Settings Schema
export const ProjectSettingsSchema = z.object({
  width: z.number().min(1).max(7680), // Up to 8K width
  height: z.number().min(1).max(4320), // Up to 8K height
  fps: z.number().min(1).max(120),
  duration: z.number().min(0),
  backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, "Invalid hex color"),
});

// Asset Metadata Schema
export const AssetMetadataSchema = z.object({
  width: z.number().optional(),
  height: z.number().optional(),
  fps: z.number().optional(),
  fileSize: z.number().min(0),
  mimeType: z.string(),
  aiPrompt: z.string().optional(),
});

// Asset Schema
export const AssetSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  type: z.enum(["video", "audio", "image", "ai-generated"]),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  duration: z.number().min(0).optional(),
  metadata: AssetMetadataSchema,
  createdAt: z.date(),
});

// Clip Properties Schema
export const ClipPropertiesSchema = z.object({
  volume: z.number().min(0).max(1).optional(),
  opacity: z.number().min(0).max(1).optional(),
  scale: z.number().min(0.1).max(10).optional(),
  rotation: z.number().min(-360).max(360).optional(),
  position: z
    .object({
      x: z.number(),
      y: z.number(),
    })
    .optional(),
  filters: z.record(z.any()).optional(),
});

// Effect Schema
export const EffectSchema = z.object({
  id: z.string().uuid(),
  type: z.string().min(1),
  name: z.string().min(1).max(100),
  parameters: z.record(z.any()),
  enabled: z.boolean(),
});

// Clip Schema
export const ClipSchema = z
  .object({
    id: z.string().uuid(),
    assetId: z.string().uuid(),
    startTime: z.number().min(0),
    endTime: z.number().min(0),
    trimStart: z.number().min(0),
    trimEnd: z.number().min(0),
    effects: z.array(EffectSchema),
    properties: ClipPropertiesSchema,
  })
  .refine((data) => data.endTime > data.startTime, {
    message: "End time must be greater than start time",
    path: ["endTime"],
  })
  .refine((data) => data.trimEnd >= data.trimStart, {
    message: "Trim end must be greater than or equal to trim start",
    path: ["trimEnd"],
  });

// Marker Schema
export const MarkerSchema = z.object({
  id: z.string().uuid(),
  time: z.number().min(0),
  label: z.string().min(1).max(100),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Invalid hex color")
    .optional(),
});

// Track Schema
export const TrackSchema = z.object({
  id: z.string().uuid(),
  type: z.enum(["video", "audio", "effects"]),
  name: z.string().min(1).max(100),
  clips: z.array(ClipSchema),
  muted: z.boolean(),
  locked: z.boolean(),
  height: z.number().min(50).max(200),
});

// Timeline Data Schema
export const TimelineDataSchema = z.object({
  tracks: z.array(TrackSchema),
  duration: z.number().min(0),
  markers: z.array(MarkerSchema),
});

// Project Schema
export const ProjectSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  settings: ProjectSettingsSchema,
  timeline: TimelineDataSchema,
  assets: z.array(AssetSchema),
  createdAt: z.date(),
  updatedAt: z.date(),
  userId: z.string().uuid(),
});

// Export Settings Schema
export const ExportSettingsSchema = z.object({
  format: z.enum(["mp4", "webm", "mov"]),
  quality: z.enum(["low", "medium", "high", "ultra"]),
  resolution: z.object({
    width: z.number().min(1).max(7680),
    height: z.number().min(1).max(4320),
  }),
  fps: z.number().min(1).max(120),
  bitrate: z.number().min(100).optional(),
});

// AI Settings Schema
export const AISettingsSchema = z.object({
  model: z.string().min(1),
  prompt: z.string().min(1).max(2000),
  negativePrompt: z.string().max(1000).optional(),
  duration: z.number().min(1).max(300).optional(), // Max 5 minutes
  aspectRatio: z
    .string()
    .regex(/^\d+:\d+$/)
    .optional(),
  seed: z.number().int().min(0).optional(),
});

// Folder Schema
export const FolderSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  parentId: z.string().uuid().optional(),
  createdAt: z.date(),
});

// Remotion Composition Schema
export const RemotionCompositionSchema = z.object({
  id: z.string().uuid(),
  durationInFrames: z.number().min(1),
  fps: z.number().min(1).max(120),
  width: z.number().min(1).max(7680),
  height: z.number().min(1).max(4320),
  props: z.record(z.any()),
});

// Remotion Sequence Schema
export const RemotionSequenceSchema = z.object({
  id: z.string().uuid(),
  from: z.number().min(0),
  durationInFrames: z.number().min(1),
  props: z.record(z.any()),
});

// Remotion Track Schema
export const RemotionTrackSchema = z.object({
  id: z.string().uuid(),
  sequences: z.array(RemotionSequenceSchema),
});

// Timeline Composition Schema
export const TimelineCompositionSchema = RemotionCompositionSchema.extend({
  tracks: z.array(RemotionTrackSchema),
});

// Input validation schemas for API endpoints
export const CreateProjectInputSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  settings: ProjectSettingsSchema.optional(),
});

export const UpdateProjectInputSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  settings: ProjectSettingsSchema.optional(),
  timeline: TimelineDataSchema.optional(),
});

export const CreateAssetInputSchema = z.object({
  name: z.string().min(1).max(255),
  type: z.enum(["video", "audio", "image", "ai-generated"]),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  duration: z.number().min(0).optional(),
  metadata: AssetMetadataSchema,
});

export const CreateTrackInputSchema = z.object({
  type: z.enum(["video", "audio", "effects"]),
  name: z.string().min(1).max(100),
  height: z.number().min(50).max(200).optional(),
});

export const CreateClipInputSchema = z
  .object({
    assetId: z.string().uuid(),
    trackId: z.string().uuid(),
    startTime: z.number().min(0),
    endTime: z.number().min(0),
    trimStart: z.number().min(0).optional(),
    trimEnd: z.number().min(0).optional(),
    properties: ClipPropertiesSchema.optional(),
  })
  .refine((data) => data.endTime > data.startTime, {
    message: "End time must be greater than start time",
    path: ["endTime"],
  });

// Type inference from schemas
export type ProjectSettingsInput = z.infer<typeof ProjectSettingsSchema>;
export type AssetInput = z.infer<typeof AssetSchema>;
export type ClipInput = z.infer<typeof ClipSchema>;
export type TrackInput = z.infer<typeof TrackSchema>;
export type TimelineDataInput = z.infer<typeof TimelineDataSchema>;
export type ProjectInput = z.infer<typeof ProjectSchema>;
export type ExportSettingsInput = z.infer<typeof ExportSettingsSchema>;
export type AISettingsInput = z.infer<typeof AISettingsSchema>;
export type FolderInput = z.infer<typeof FolderSchema>;

export type CreateProjectInput = z.infer<typeof CreateProjectInputSchema>;
export type UpdateProjectInput = z.infer<typeof UpdateProjectInputSchema>;
export type CreateAssetInput = z.infer<typeof CreateAssetInputSchema>;
export type CreateTrackInput = z.infer<typeof CreateTrackInputSchema>;
export type CreateClipInput = z.infer<typeof CreateClipInputSchema>;
