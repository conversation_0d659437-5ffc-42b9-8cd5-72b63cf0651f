import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET() {
  try {
    // Get user session
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user details including credits
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id)
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Calculate hours until credits reset
    const now = new Date();
    
    // We reset credits at midnight UTC
    const tomorrow = new Date(now);
    tomorrow.setUTCHours(0, 0, 0, 0);
    tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
    
    // Calculate hours until reset
    const hoursUntilReset = Math.max(0, Math.ceil((tomorrow.getTime() - now.getTime()) / (1000 * 60 * 60)));

    return NextResponse.json({
      stats: {
        credits: user.credits,
        userType: user.userType,
        hoursUntilReset,
        lastUpdated: now.toISOString(),
      }
    });
  } catch (error) {
    console.error("Error fetching user stats:", error);
    return NextResponse.json({ error: "Failed to fetch user stats" }, { status: 500 });
  }
} 