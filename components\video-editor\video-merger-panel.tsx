"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Merge,
  Download,
  X,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Settings,
  Layers,
  Film,
} from "lucide-react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useVideoMergerStore } from "@/lib/stores/video-merger-store";
import type { MergeOperation } from "@/lib/services/video-merger";
import { cn } from "@/lib/utils";

interface VideoMergerPanelProps {
  className?: string;
}

export function VideoMergerPanel({ className }: VideoMergerPanelProps) {
  const timelineStore = useTimelineStore();
  const assetStore = useAssetStore();
  const projectStore = useProjectStore();
  const mergerStore = useVideoMergerStore();

  const [activeTab, setActiveTab] = useState("merge");
  const [mergeDialogOpen, setMergeDialogOpen] = useState(false);
  const [compositeDialogOpen, setCompositeDialogOpen] = useState(false);
  const [batchDialogOpen, setBatchDialogOpen] = useState(false);

  // Merge settings state
  const [mergeSettings, setMergeSettings] = useState<{
    format: "mp4" | "webm" | "mov";
    quality: "low" | "medium" | "high" | "ultra";
    resolution: { width: number; height: number };
    fps: number;
    audioSync: boolean;
  }>({
    format: "mp4",
    quality: "high",
    resolution: { width: 1920, height: 1080 },
    fps: 30,
    audioSync: true,
  });

  const selectedClips = timelineStore.getSelectedClips();
  const allAssets = assetStore.assets;
  const currentProject = projectStore.currentProject;
  const mergeOperations = mergerStore.mergeOperations;
  const batchOperations = mergerStore.batchOperations;

  const handleMergeSelectedClips = async () => {
    if (selectedClips.length < 2) return;

    try {
      await mergerStore.mergeClips(selectedClips, allAssets, mergeSettings);
      setMergeDialogOpen(false);
    } catch (error) {
      console.error("Failed to merge clips:", error);
    }
  };

  const handleCreateCompositeVideo = async () => {
    if (!currentProject) return;

    try {
      await mergerStore.createCompositeVideo(
        {
          tracks: timelineStore.tracks,
          projectSettings: currentProject.settings,
          outputFormat: mergeSettings.format,
          quality: mergeSettings.quality,
          audioSync: mergeSettings.audioSync,
        },
        allAssets
      );
      setCompositeDialogOpen(false);
    } catch (error) {
      console.error("Failed to create composite video:", error);
    }
  };

  const handleBatchMerge = async () => {
    // Group clips by track for batch merging
    const trackGroups = timelineStore.tracks
      .filter((track) => track.clips.length > 1)
      .map((track) => ({
        clips: track.clips,
        outputSettings: mergeSettings,
      }));

    if (trackGroups.length === 0) return;

    try {
      await mergerStore.batchMergeClips(trackGroups, allAssets);
      setBatchDialogOpen(false);
    } catch (error) {
      console.error("Failed to start batch merge:", error);
    }
  };

  const getStatusIcon = (status: MergeOperation["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "processing":
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "failed":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: MergeOperation["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500/20 text-yellow-300";
      case "processing":
        return "bg-blue-500/20 text-blue-300";
      case "completed":
        return "bg-green-500/20 text-green-300";
      case "failed":
        return "bg-red-500/20 text-red-300";
    }
  };

  return (
    <div className={cn("bg-gray-900 border-l border-gray-700", className)}>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Merge className="w-5 h-5" />
          Video Merger
        </h3>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4 w-full">
            <TabsTrigger value="merge" className="text-xs">
              <Merge className="w-3 h-3 mr-1" />
              Merge
            </TabsTrigger>
            <TabsTrigger value="composite" className="text-xs">
              <Layers className="w-3 h-3 mr-1" />
              Composite
            </TabsTrigger>
            <TabsTrigger value="operations" className="text-xs">
              <Film className="w-3 h-3 mr-1" />
              Operations
            </TabsTrigger>
          </TabsList>

          {/* Merge Tab */}
          <TabsContent value="merge" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Merge Selected Clips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-gray-400">
                  {selectedClips.length} clips selected
                </div>

                <Dialog
                  open={mergeDialogOpen}
                  onOpenChange={setMergeDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      className="w-full"
                      disabled={selectedClips.length < 2}
                    >
                      <Merge className="w-4 h-4 mr-2" />
                      Merge Clips
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Merge Video Clips</DialogTitle>
                      <DialogDescription>
                        Configure merge settings for {selectedClips.length}{" "}
                        selected clips
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Format</Label>
                          <Select
                            value={mergeSettings.format}
                            onValueChange={(value: "mp4" | "webm" | "mov") =>
                              setMergeSettings((prev) => ({
                                ...prev,
                                format: value,
                              }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="mp4">MP4</SelectItem>
                              <SelectItem value="webm">WebM</SelectItem>
                              <SelectItem value="mov">MOV</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Quality</Label>
                          <Select
                            value={mergeSettings.quality}
                            onValueChange={(value: "low" | "medium" | "high" | "ultra") =>
                              setMergeSettings((prev) => ({
                                ...prev,
                                quality: value,
                              }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="ultra">Ultra</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="audio-sync"
                          checked={mergeSettings.audioSync}
                          onCheckedChange={(checked) =>
                            setMergeSettings((prev) => ({
                              ...prev,
                              audioSync: checked,
                            }))
                          }
                        />
                        <Label htmlFor="audio-sync">
                          Synchronize audio tracks
                        </Label>
                      </div>
                    </div>

                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setMergeDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleMergeSelectedClips}>
                        Start Merge
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Separator />

                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-white">
                    Quick Actions
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        timelineStore.selectClips(
                          timelineStore.tracks.flatMap((track) =>
                            track.clips.map((clip) => clip.id)
                          )
                        )
                      }
                    >
                      Select All Clips
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => timelineStore.clearSelection()}
                    >
                      Clear Selection
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Composite Tab */}
          <TabsContent value="composite" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">
                  Create Composite Video
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm text-gray-400">
                  {timelineStore.tracks.length} tracks •{" "}
                  {timelineStore.tracks.reduce(
                    (sum, track) => sum + track.clips.length,
                    0
                  )}{" "}
                  total clips
                </div>

                <Dialog
                  open={compositeDialogOpen}
                  onOpenChange={setCompositeDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      className="w-full"
                      disabled={timelineStore.tracks.length === 0}
                    >
                      <Layers className="w-4 h-4 mr-2" />
                      Create Composite
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Create Composite Video</DialogTitle>
                      <DialogDescription>
                        Render all timeline tracks into a single video
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Format</Label>
                          <Select
                            value={mergeSettings.format}
                            onValueChange={(value: "mp4" | "webm" | "mov") =>
                              setMergeSettings((prev) => ({
                                ...prev,
                                format: value,
                              }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="mp4">MP4</SelectItem>
                              <SelectItem value="webm">WebM</SelectItem>
                              <SelectItem value="mov">MOV</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Quality</Label>
                          <Select
                            value={mergeSettings.quality}
                            onValueChange={(value: "low" | "medium" | "high" | "ultra") =>
                              setMergeSettings((prev) => ({
                                ...prev,
                                quality: value,
                              }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="ultra">Ultra</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Project Settings</Label>
                        <div className="text-sm text-gray-400">
                          {currentProject?.settings.width}x
                          {currentProject?.settings.height} •{" "}
                          {currentProject?.settings.fps}fps
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="composite-audio-sync"
                          checked={mergeSettings.audioSync}
                          onCheckedChange={(checked) =>
                            setMergeSettings((prev) => ({
                              ...prev,
                              audioSync: checked,
                            }))
                          }
                        />
                        <Label htmlFor="composite-audio-sync">
                          Synchronize audio tracks
                        </Label>
                      </div>
                    </div>

                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setCompositeDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleCreateCompositeVideo}>
                        Create Composite
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Separator />

                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-white">
                    Batch Operations
                  </h4>
                  <Dialog
                    open={batchDialogOpen}
                    onOpenChange={setBatchDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        disabled={
                          timelineStore.tracks.filter(
                            (track) => track.clips.length > 1
                          ).length === 0
                        }
                      >
                        <Settings className="w-4 h-4 mr-2" />
                        Batch Merge Tracks
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Batch Merge Operations</DialogTitle>
                        <DialogDescription>
                          Merge clips from each track separately
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-4">
                        <div className="text-sm text-gray-400">
                          {
                            timelineStore.tracks.filter(
                              (track) => track.clips.length > 1
                            ).length
                          }{" "}
                          tracks will be processed
                        </div>

                        <div className="space-y-2">
                          {timelineStore.tracks
                            .filter((track) => track.clips.length > 1)
                            .map((track) => (
                              <div
                                key={track.id}
                                className="flex items-center justify-between bg-gray-800 rounded p-2"
                              >
                                <span className="text-sm text-white">
                                  {track.name}
                                </span>
                                <Badge variant="secondary">
                                  {track.clips.length} clips
                                </Badge>
                              </div>
                            ))}
                        </div>
                      </div>

                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setBatchDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleBatchMerge}>
                          Start Batch Merge
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Operations Tab */}
          <TabsContent value="operations" className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-white">
                Active Operations
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => mergerStore.clearCompletedOperations()}
              >
                Clear Completed
              </Button>
            </div>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {mergeOperations.length === 0 ? (
                <div className="text-center py-8">
                  <Film className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400 text-sm">No merge operations</p>
                </div>
              ) : (
                mergeOperations.map((operation) => (
                  <Card key={operation.id}>
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(operation.status)}
                          <span className="text-sm font-medium text-white">
                            Merge Operation
                          </span>
                          <Badge
                            className={cn(
                              "text-xs",
                              getStatusColor(operation.status)
                            )}
                          >
                            {operation.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          {operation.status === "processing" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() =>
                                mergerStore.cancelMergeOperation(operation.id)
                              }
                              className="h-6 w-6 p-0"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          )}
                          {operation.status === "completed" &&
                            operation.outputUrl && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  window.open(operation.outputUrl, "_blank")
                                }
                                className="h-6 w-6 p-0"
                              >
                                <Download className="w-3 h-3" />
                              </Button>
                            )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="text-xs text-gray-400">
                          {operation.clips.length} clips •{" "}
                          {operation.outputSettings.format.toUpperCase()} •{" "}
                          {operation.outputSettings.quality}
                        </div>

                        {(operation.status === "processing" ||
                          operation.status === "pending") && (
                          <Progress
                            value={operation.progress}
                            className="h-1"
                          />
                        )}

                        {operation.status === "failed" && operation.error && (
                          <div className="text-xs text-red-400">
                            Error: {operation.error}
                          </div>
                        )}

                        <div className="text-xs text-gray-500">
                          {new Date(operation.createdAt).toLocaleString()}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>

            {batchOperations.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="text-sm font-medium text-white mb-2">
                    Batch Operations
                  </h4>
                  <div className="space-y-2">
                    {batchOperations.map((batch) => (
                      <Card key={batch.id}>
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(batch.status)}
                              <span className="text-sm font-medium text-white">
                                Batch Operation
                              </span>
                              <Badge
                                className={cn(
                                  "text-xs",
                                  getStatusColor(batch.status)
                                )}
                              >
                                {batch.status}
                              </Badge>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="text-xs text-gray-400">
                              {batch.completedCount} / {batch.totalCount}{" "}
                              completed
                            </div>

                            <Progress value={batch.progress} className="h-1" />

                            <div className="text-xs text-gray-500">
                              {new Date(batch.createdAt).toLocaleString()}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
