import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  category: string;
  defaultKeys: string[];
  keys: string[];
  action: () => void;
  enabled: boolean;
  global?: boolean; // If true, works even when inputs are focused
}

export interface ShortcutCategory {
  id: string;
  name: string;
  description: string;
  shortcuts: KeyboardShortcut[];
}

interface KeyboardShortcutState {
  shortcuts: Record<string, KeyboardShortcut>;
  categories: Record<string, ShortcutCategory>;
  isEnabled: boolean;
  pressedKeys: Set<string>;
  
  // Actions
  registerShortcut: (shortcut: Omit<KeyboardShortcut, "action">, action: () => void) => void;
  updateShortcutKeys: (id: string, keys: string[]) => void;
  toggleShortcut: (id: string) => void;
  resetShortcut: (id: string) => void;
  resetAllShortcuts: () => void;
  setEnabled: (enabled: boolean) => void;
  executeShortcut: (keys: string[]) => boolean;
  getShortcutByKeys: (keys: string[]) => KeyboardShortcut | null;
  getShortcutsByCategory: (category: string) => KeyboardShortcut[];
}

// Key mapping for consistent key names
const KEY_MAP: Record<string, string> = {
  " ": "Space",
  "Control": "Ctrl",
  "Meta": "Cmd",
  "ArrowLeft": "←",
  "ArrowRight": "→",
  "ArrowUp": "↑",
  "ArrowDown": "↓",
  "Escape": "Esc",
  "Delete": "Del",
  "Backspace": "Backspace",
  "Enter": "Enter",
  "Tab": "Tab",
  "Shift": "Shift",
  "Alt": "Alt",
};

export const normalizeKey = (key: string): string => {
  return KEY_MAP[key] || key.toUpperCase();
};

export const formatShortcut = (keys: string[]): string => {
  return keys.join(" + ");
};

export const parseShortcut = (shortcut: string): string[] => {
  return shortcut.split(" + ").map(key => key.trim());
};

export const useKeyboardShortcuts = create<KeyboardShortcutState>()(
  persist(
    (set, get) => ({
      shortcuts: {},
      categories: {},
      isEnabled: true,
      pressedKeys: new Set(),

      registerShortcut: (shortcut, action) => {
        const fullShortcut: KeyboardShortcut = {
          ...shortcut,
          action,
        };

        set((state) => ({
          shortcuts: {
            ...state.shortcuts,
            [shortcut.id]: fullShortcut,
          },
          categories: {
            ...state.categories,
            [shortcut.category]: {
              id: shortcut.category,
              name: shortcut.category,
              description: `${shortcut.category} shortcuts`,
              shortcuts: [
                ...(state.categories[shortcut.category]?.shortcuts || []),
                fullShortcut,
              ],
            },
          },
        }));
      },

      updateShortcutKeys: (id, keys) => {
        set((state) => ({
          shortcuts: {
            ...state.shortcuts,
            [id]: {
              ...state.shortcuts[id],
              keys,
            },
          },
        }));
      },

      toggleShortcut: (id) => {
        set((state) => ({
          shortcuts: {
            ...state.shortcuts,
            [id]: {
              ...state.shortcuts[id],
              enabled: !state.shortcuts[id].enabled,
            },
          },
        }));
      },

      resetShortcut: (id) => {
        set((state) => ({
          shortcuts: {
            ...state.shortcuts,
            [id]: {
              ...state.shortcuts[id],
              keys: [...state.shortcuts[id].defaultKeys],
            },
          },
        }));
      },

      resetAllShortcuts: () => {
        set((state) => {
          const resetShortcuts = { ...state.shortcuts };
          Object.keys(resetShortcuts).forEach((id) => {
            resetShortcuts[id] = {
              ...resetShortcuts[id],
              keys: [...resetShortcuts[id].defaultKeys],
            };
          });
          return { shortcuts: resetShortcuts };
        });
      },

      setEnabled: (enabled) => {
        set({ isEnabled: enabled });
      },

      executeShortcut: (keys) => {
        const state = get();
        if (!state.isEnabled) return false;

        const shortcut = state.getShortcutByKeys(keys);
        if (shortcut && shortcut.enabled) {
          shortcut.action();
          return true;
        }
        return false;
      },

      getShortcutByKeys: (keys) => {
        const state = get();
        const keyString = keys.join("+");
        
        return Object.values(state.shortcuts).find((shortcut) => {
          const shortcutKeyString = shortcut.keys.join("+");
          return shortcutKeyString === keyString && shortcut.enabled;
        }) || null;
      },

      getShortcutsByCategory: (category) => {
        const state = get();
        return Object.values(state.shortcuts).filter(
          (shortcut) => shortcut.category === category
        );
      },
    }),
    {
      name: "keyboard-shortcuts",
      partialize: (state) => ({
        shortcuts: Object.fromEntries(
          Object.entries(state.shortcuts).map(([id, shortcut]) => [
            id,
            {
              id: shortcut.id,
              name: shortcut.name,
              description: shortcut.description,
              category: shortcut.category,
              defaultKeys: shortcut.defaultKeys,
              keys: shortcut.keys,
              enabled: shortcut.enabled,
              global: shortcut.global,
            },
          ])
        ),
        isEnabled: state.isEnabled,
      }),
    }
  )
);

// Global keyboard event handler
export class KeyboardShortcutManager {
  private static instance: KeyboardShortcutManager;
  private pressedKeys = new Set<string>();
  private keySequence: string[] = [];
  private sequenceTimeout: NodeJS.Timeout | null = null;

  private constructor() {
    this.setupEventListeners();
  }

  static getInstance(): KeyboardShortcutManager {
    if (!KeyboardShortcutManager.instance) {
      KeyboardShortcutManager.instance = new KeyboardShortcutManager();
    }
    return KeyboardShortcutManager.instance;
  }

  private setupEventListeners() {
    document.addEventListener("keydown", this.handleKeyDown.bind(this));
    document.addEventListener("keyup", this.handleKeyUp.bind(this));
    document.addEventListener("blur", this.handleBlur.bind(this));
    window.addEventListener("blur", this.handleBlur.bind(this));
  }

  private handleKeyDown(event: KeyboardEvent) {
    const { shortcuts, isEnabled, executeShortcut } = useKeyboardShortcuts.getState();
    
    if (!isEnabled) return;

    // Check if we're in an input field (unless shortcut is global)
    const target = event.target as HTMLElement;
    const isInInput = target.tagName === "INPUT" || 
                     target.tagName === "TEXTAREA" || 
                     target.isContentEditable;

    const normalizedKey = normalizeKey(event.key);
    this.pressedKeys.add(normalizedKey);

    // Build current key combination
    const modifiers: string[] = [];
    if (event.ctrlKey) modifiers.push("Ctrl");
    if (event.metaKey) modifiers.push("Cmd");
    if (event.shiftKey) modifiers.push("Shift");
    if (event.altKey) modifiers.push("Alt");

    // Add the main key if it's not a modifier
    const mainKey = normalizedKey;
    if (!["Ctrl", "Cmd", "Shift", "Alt"].includes(mainKey)) {
      const currentKeys = [...modifiers, mainKey];
      
      // Check if this matches any shortcut
      const matchingShortcut = Object.values(shortcuts).find(shortcut => {
        if (!shortcut.enabled) return false;
        if (isInInput && !shortcut.global) return false;
        
        const shortcutKeys = shortcut.keys.join("+");
        const currentKeysString = currentKeys.join("+");
        return shortcutKeys === currentKeysString;
      });

      if (matchingShortcut) {
        event.preventDefault();
        event.stopPropagation();
        executeShortcut(currentKeys);
      }
    }
  }

  private handleKeyUp(event: KeyboardEvent) {
    const normalizedKey = normalizeKey(event.key);
    this.pressedKeys.delete(normalizedKey);
  }

  private handleBlur() {
    this.pressedKeys.clear();
    this.keySequence = [];
    if (this.sequenceTimeout) {
      clearTimeout(this.sequenceTimeout);
      this.sequenceTimeout = null;
    }
  }

  public getCurrentKeys(): string[] {
    return Array.from(this.pressedKeys);
  }

  public isKeyPressed(key: string): boolean {
    return this.pressedKeys.has(normalizeKey(key));
  }
}

// Initialize the manager
export const keyboardManager = KeyboardShortcutManager.getInstance();
