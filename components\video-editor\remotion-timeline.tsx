"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { useTimelineDragDrop } from "@/lib/hooks/use-timeline-drag-drop";
import { useTimelineEditing } from "@/lib/hooks/use-timeline-editing";
import { useTimelineClipboard } from "@/lib/hooks/use-timeline-clipboard";
import { useTimelineUndoRedo } from "@/lib/hooks/use-timeline-undo-redo";
import { useTimelineShortcuts } from "@/lib/hooks/use-timeline-shortcuts";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import {
  ClipContextMenu,
  TrackContextMenu,
  TimelineContextMenu,
} from "./timeline-context-menus";
import {
  Copy,
  Move,
  Pause,
  Play,
  Redo,
  Scissors,
  SkipBack,
  SkipForward,
  Trash2,
  Undo,
  ZoomIn,
  ZoomOut,
} from "lucide-react";
import React, { useCallback, useEffect, useRef } from "react";

interface RemotionTimelineProps {
  className?: string;
}

export function RemotionTimeline({ className = "" }: RemotionTimelineProps) {
  const timelineStore = useTimelineStore();
  const assetStore = useAssetStore();
  const { dragState, dropZone, startClipDrag, handleDragOver, handleDrop } =
    useTimelineDragDrop();

  // Initialize editing hooks
  const editing = useTimelineEditing();
  const clipboard = useTimelineClipboard();
  const undoRedo = useTimelineUndoRedo();

  const timelineRef = useRef<HTMLDivElement>(null);

  // Timeline dimensions and scaling
  const TRACK_HEIGHT = 80;
  const TIMELINE_PADDING = 20;
  const PIXELS_PER_SECOND = 100 * timelineStore.zoomLevel;

  // Set up editing hook with current dimensions
  useEffect(() => {
    editing.setPixelsPerSecond(PIXELS_PER_SECOND);
  }, [PIXELS_PER_SECOND, editing]);

  // Initialize keyboard shortcuts
  useTimelineShortcuts({
    onTogglePlayback: () =>
      timelineStore.setIsPlaying(!timelineStore.isPlaying),
    onSeekToStart: () => timelineStore.setPlayheadPosition(0),
    onSeekToEnd: () =>
      timelineStore.setPlayheadPosition(timelineStore.duration),
  });

  // Convert time to pixel position
  const timeToPixels = useCallback(
    (time: number) => {
      return time * PIXELS_PER_SECOND + TIMELINE_PADDING;
    },
    [PIXELS_PER_SECOND]
  );

  // Convert pixel position to time
  const pixelsToTime = useCallback(
    (pixels: number) => {
      return Math.max(0, (pixels - TIMELINE_PADDING) / PIXELS_PER_SECOND);
    },
    [PIXELS_PER_SECOND]
  );

  // Handle timeline mouse down (for playhead scrubbing and box selection)
  const handleTimelineMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!timelineRef.current) return;

      const rect = timelineRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Check if clicking on a trim handle or clip
      const target = e.target as HTMLElement;
      if (
        target.classList.contains("trim-handle") ||
        target.closest(".timeline-clip")
      ) {
        return; // Let clip/trim handle events handle this
      }

      // If not dragging and not on a clip, start box selection or scrub playhead
      if (!dragState.isDragging && !editing.trimState.isActive) {
        if (e.shiftKey) {
          // Start box selection
          editing.startBoxSelection(x, y);
        } else {
          // Scrub playhead
          const time = pixelsToTime(x);
          timelineStore.setPlayheadPosition(
            Math.min(time, timelineStore.duration)
          );

          // Clear selection if not holding Ctrl/Cmd
          if (!e.ctrlKey && !e.metaKey) {
            timelineStore.clearSelection();
          }
        }
      }
    },
    [pixelsToTime, timelineStore, dragState.isDragging, editing]
  );

  // Handle mouse move for drag operations and editing
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!timelineRef.current) return;

      const rect = timelineRef.current.getBoundingClientRect();

      // Handle trimming
      if (editing.trimState.isActive) {
        editing.updateTrim(e.clientX - rect.left);
        return;
      }

      // Handle box selection
      if (editing.selectionState.isSelecting) {
        editing.updateBoxSelection(e.clientX - rect.left, e.clientY - rect.top);
        return;
      }

      // Handle drag and drop
      if (dragState.isDragging) {
        handleDragOver(
          e,
          rect,
          PIXELS_PER_SECOND,
          TRACK_HEIGHT,
          TIMELINE_PADDING
        );
      }
    },
    [
      dragState.isDragging,
      editing,
      handleDragOver,
      PIXELS_PER_SECOND,
      TRACK_HEIGHT,
      TIMELINE_PADDING,
    ]
  );

  // Handle mouse up for all operations
  const handleMouseUp = useCallback(() => {
    // End trimming
    if (editing.trimState.isActive) {
      editing.endTrim();
      return;
    }

    // End box selection
    if (editing.selectionState.isSelecting) {
      editing.endBoxSelection(TIMELINE_PADDING, TRACK_HEIGHT, 32);
      return;
    }

    // End drag and drop
    if (dragState.isDragging) {
      handleDrop();
    }
  }, [
    editing,
    dragState.isDragging,
    handleDrop,
    TIMELINE_PADDING,
    TRACK_HEIGHT,
  ]);

  // Set up global mouse event listeners for all operations
  useEffect(() => {
    if (
      dragState.isDragging ||
      editing.trimState.isActive ||
      editing.selectionState.isSelecting
    ) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [
    dragState.isDragging,
    editing.trimState.isActive,
    editing.selectionState.isSelecting,
    handleMouseMove,
    handleMouseUp,
  ]);

  // Zoom controls
  const handleZoomIn = () => {
    timelineStore.setZoomLevel(Math.min(timelineStore.zoomLevel * 1.5, 10));
  };

  const handleZoomOut = () => {
    timelineStore.setZoomLevel(Math.max(timelineStore.zoomLevel / 1.5, 0.1));
  };

  // Playback controls
  const togglePlayback = () => {
    timelineStore.setIsPlaying(!timelineStore.isPlaying);
  };

  const skipBackward = () => {
    const newTime = Math.max(0, timelineStore.playheadPosition - 1);
    timelineStore.setPlayheadPosition(newTime);
  };

  const skipForward = () => {
    const newTime = Math.min(
      timelineStore.duration,
      timelineStore.playheadPosition + 1
    );
    timelineStore.setPlayheadPosition(newTime);
  };

  return (
    <div className={`bg-gray-900 rounded-lg ${className}`}>
      {/* Timeline Controls */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={togglePlayback}
            className="text-white hover:bg-gray-700"
          >
            {timelineStore.isPlaying ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4" />
            )}
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={skipBackward}
            className="text-white hover:bg-gray-700"
          >
            <SkipBack className="w-4 h-4" />
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={skipForward}
            className="text-white hover:bg-gray-700"
          >
            <SkipForward className="w-4 h-4" />
          </Button>

          <div className="mx-4 text-sm text-gray-400">
            {timelineStore.playheadPosition.toFixed(2)}s /{" "}
            {timelineStore.duration.toFixed(2)}s
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Undo/Redo */}
          <Button
            size="sm"
            variant="ghost"
            onClick={undoRedo.undo}
            disabled={!undoRedo.canUndo}
            className="text-white hover:bg-gray-700"
            title="Undo (Ctrl+Z)"
          >
            <Undo className="w-4 h-4" />
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={undoRedo.redo}
            disabled={!undoRedo.canRedo}
            className="text-white hover:bg-gray-700"
            title="Redo (Ctrl+Y)"
          >
            <Redo className="w-4 h-4" />
          </Button>

          <div className="w-px h-6 bg-gray-600 mx-2" />

          {/* Clipboard Operations */}
          <Button
            size="sm"
            variant="ghost"
            onClick={clipboard.copyClips}
            disabled={timelineStore.selectedClips.length === 0}
            className="text-white hover:bg-gray-700"
            title="Copy (Ctrl+C)"
          >
            <Copy className="w-4 h-4" />
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={clipboard.cutClips}
            disabled={timelineStore.selectedClips.length === 0}
            className="text-white hover:bg-gray-700"
            title="Cut (Ctrl+X)"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
            </svg>
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={() => clipboard.pasteClips()}
            disabled={!clipboard.hasClipboardData()}
            className="text-white hover:bg-gray-700"
            title="Paste (Ctrl+V)"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 011 1v1h2a2 2 0 012 2v11a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2h2V3z" />
            </svg>
          </Button>

          <div className="w-px h-6 bg-gray-600 mx-2" />

          {/* Editing Operations */}
          <Button
            size="sm"
            variant="ghost"
            onClick={editing.splitClipAtPlayhead}
            disabled={timelineStore.selectedClips.length !== 1}
            className="text-white hover:bg-gray-700"
            title="Split at Playhead (Ctrl+S)"
          >
            <Scissors className="w-4 h-4" />
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={editing.deleteSelectedClips}
            disabled={timelineStore.selectedClips.length === 0}
            className="text-white hover:bg-gray-700"
            title="Delete (Del)"
          >
            <Trash2 className="w-4 h-4" />
          </Button>

          <div className="flex items-center gap-1 ml-4">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleZoomOut}
              className="text-white hover:bg-gray-700"
            >
              <ZoomOut className="w-4 h-4" />
            </Button>

            <div className="w-20 mx-2">
              <Slider
                value={[timelineStore.zoomLevel]}
                min={0.1}
                max={10}
                step={0.1}
                onValueChange={(value) => timelineStore.setZoomLevel(value[0])}
                className="w-full"
              />
            </div>

            <Button
              size="sm"
              variant="ghost"
              onClick={handleZoomIn}
              className="text-white hover:bg-gray-700"
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Timeline Area */}
      <div className="relative overflow-x-auto overflow-y-hidden">
        <TimelineContextMenu>
          <div
            ref={timelineRef}
            className="relative min-h-[300px] cursor-crosshair"
            style={{
              width: Math.max(
                800,
                timeToPixels(timelineStore.duration) + TIMELINE_PADDING * 2
              ),
            }}
            onMouseDown={handleTimelineMouseDown}
          >
            {/* Time Ruler */}
            <div className="absolute top-0 left-0 right-0 h-8 bg-gray-800 border-b border-gray-700">
              {Array.from(
                { length: Math.ceil(timelineStore.duration) + 1 },
                (_, i) => (
                  <div
                    key={i}
                    className="absolute top-0 bottom-0 border-l border-gray-600"
                    style={{ left: timeToPixels(i) }}
                  >
                    <span className="absolute top-1 left-1 text-xs text-gray-400">
                      {i}s
                    </span>
                  </div>
                )
              )}
            </div>

            {/* Playhead */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-20 pointer-events-none"
              style={{ left: timeToPixels(timelineStore.playheadPosition) }}
            >
              <div className="absolute -top-1 -left-2 w-4 h-4 bg-red-500 rotate-45 transform origin-center"></div>
            </div>

            {/* Drop Zone Indicator */}
            {dropZone && (
              <div
                className={`absolute top-8 bottom-0 w-1 z-10 ${
                  dropZone.isValid ? "bg-green-500" : "bg-red-500"
                }`}
                style={{ left: timeToPixels(dropZone.time) }}
              />
            )}

            {/* Box Selection Overlay */}
            {editing.selectionState.isSelecting && (
              <div
                className="absolute border border-dashed border-blue-400 bg-blue-400 bg-opacity-10 pointer-events-none z-15"
                style={editing.getSelectionBoxStyle() || {}}
              />
            )}

            {/* Tracks */}
            <div className="mt-8">
              {timelineStore.tracks.map((track) => (
                <div
                  key={track.id}
                  className={`relative border-b border-gray-700 ${
                    dropZone?.trackId === track.id ? "bg-gray-700" : ""
                  }`}
                  style={{ height: TRACK_HEIGHT }}
                >
                  {/* Track Header */}
                  <TrackContextMenu track={track}>
                    <div className="absolute left-0 top-0 w-32 h-full bg-gray-800 border-r border-gray-700 flex items-center px-3 cursor-pointer hover:bg-gray-750">
                      <div>
                        <div className="text-sm font-medium text-white">
                          {track.name}
                        </div>
                        <div className="text-xs text-gray-400 capitalize">
                          {track.type}
                        </div>
                      </div>
                    </div>
                  </TrackContextMenu>

                  {/* Track Content Area */}
                  <div className="ml-32 h-full relative bg-gray-850">
                    {/* Clips */}
                    {track.clips.map((clip) => {
                      const asset = assetStore.getAssetById(clip.assetId);
                      const clipWidth =
                        (clip.endTime - clip.startTime) * PIXELS_PER_SECOND;
                      const isSelected = timelineStore.selectedClips.includes(
                        clip.id
                      );
                      const isDraggedClip =
                        dragState.draggedItem?.id === clip.id;

                      return (
                        <ClipContextMenu key={clip.id} clip={clip}>
                          <div
                            className={`timeline-clip absolute top-2 bottom-2 rounded cursor-move transition-all ${
                              isDraggedClip ? "opacity-50" : ""
                            } ${
                              isSelected
                                ? "bg-purple-600 border-2 border-purple-400"
                                : "bg-blue-600 hover:bg-blue-500 border border-blue-400"
                            }`}
                            style={{
                              left:
                                timeToPixels(clip.startTime) - TIMELINE_PADDING,
                              width: Math.max(clipWidth, 20),
                            }}
                            onMouseDown={(e) =>
                              startClipDrag(clip.id, e, track.id)
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                              timelineStore.selectClip(
                                clip.id,
                                e.ctrlKey || e.metaKey
                              );
                            }}
                          >
                            <div className="h-full flex items-center px-2 overflow-hidden">
                              <Move className="w-3 h-3 text-white mr-1 flex-shrink-0" />
                              <span className="text-xs text-white truncate">
                                {asset?.name || "Unknown"}
                              </span>
                            </div>

                            {/* Trim Handles */}
                            {isSelected && !dragState.isDragging && (
                              <>
                                <div
                                  className="trim-handle absolute left-0 top-0 bottom-0 w-2 bg-yellow-400 cursor-ew-resize hover:bg-yellow-300 transition-colors"
                                  onMouseDown={(e) => {
                                    e.stopPropagation();
                                    editing.startTrim(
                                      clip.id,
                                      "start",
                                      e.clientX
                                    );
                                  }}
                                  title="Trim start"
                                />
                                <div
                                  className="trim-handle absolute right-0 top-0 bottom-0 w-2 bg-yellow-400 cursor-ew-resize hover:bg-yellow-300 transition-colors"
                                  onMouseDown={(e) => {
                                    e.stopPropagation();
                                    editing.startTrim(
                                      clip.id,
                                      "end",
                                      e.clientX
                                    );
                                  }}
                                  title="Trim end"
                                />
                              </>
                            )}
                          </div>
                        </ClipContextMenu>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Add Track Button */}
            <div className="mt-4 p-4">
              <Button
                onClick={() => {
                  const trackCount = timelineStore.tracks.length;
                  timelineStore.addTrack({
                    type:
                      trackCount === 0
                        ? "video"
                        : trackCount === 1
                        ? "audio"
                        : "effects",
                    name: `Track ${trackCount + 1}`,
                    clips: [],
                    muted: false,
                    locked: false,
                    height: TRACK_HEIGHT,
                  });
                }}
                className="bg-gray-700 hover:bg-gray-600 text-white"
              >
                Add Track
              </Button>
            </div>
          </div>
        </TimelineContextMenu>
      </div>
    </div>
  );
}
