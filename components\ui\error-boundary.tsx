"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Home, Bug } from "lucide-react";
import {
  useError<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/lib/services/error-handling-service";
import { toast } from "@/lib/services/toast-service";

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
}

interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  errorInfo?: React.ErrorInfo;
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Report error to error handling service
    const { reportError } = useErrorHandling.getState();
    reportError(
      ErrorHandler.createError("unknown", error.message, {
        details: errorInfo.componentStack || undefined,
        severity: "critical",
        recoverable: true,
        retryable: true,
        stack: error.stack,
        context: {
          componentStack: errorInfo.componentStack || undefined,
          errorBoundary: true,
        },
      })
    );

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Show error toast
    toast.error(
      "Application Error",
      "An unexpected error occurred. Please try refreshing the page."
    );

    console.error("Error Boundary caught an error:", error, errorInfo);
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && prevProps.resetKeys !== resetKeys) {
      if (resetKeys?.some((key, idx) => prevProps.resetKeys?.[idx] !== key)) {
        this.resetError();
      }
    }
  }

  resetError = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.resetTimeoutId = window.setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
      });
    }, 100);
  };

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback: Fallback } = this.props;

    if (hasError && error) {
      if (Fallback) {
        return (
          <Fallback
            error={error}
            resetError={this.resetError}
            errorInfo={errorInfo || undefined}
          />
        );
      }

      return (
        <DefaultErrorFallback
          error={error}
          resetError={this.resetError}
          errorInfo={errorInfo || undefined}
        />
      );
    }

    return children;
  }
}

function DefaultErrorFallback({
  error,
  resetError,
  errorInfo,
}: ErrorFallbackProps) {
  const handleReportBug = () => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href,
    };

    // In a real app, you would send this to your error reporting service
    console.log("Error Report:", errorReport);

    toast.info("Error Report", "Error details have been logged for debugging.");
  };

  const handleGoHome = () => {
    window.location.href = "/dashboard";
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
        <div className="flex justify-center mb-4">
          <AlertTriangle className="w-16 h-16 text-red-500" />
        </div>

        <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Something went wrong
        </h1>

        <p className="text-gray-600 dark:text-gray-400 mb-6">
          An unexpected error occurred. This has been logged and we&apos;ll look
          into it.
        </p>

        <div className="space-y-3">
          <Button onClick={resetError} className="w-full">
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>

          <Button onClick={handleGoHome} variant="outline" className="w-full">
            <Home className="w-4 h-4 mr-2" />
            Go to Dashboard
          </Button>

          <Button
            onClick={handleReportBug}
            variant="ghost"
            size="sm"
            className="w-full"
          >
            <Bug className="w-4 h-4 mr-2" />
            Report Bug
          </Button>
        </div>

        {process.env.NODE_ENV === "development" && (
          <details className="mt-6 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              Error Details (Development)
            </summary>
            <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono overflow-auto max-h-40">
              <div className="text-red-600 dark:text-red-400 font-semibold">
                {error.name}: {error.message}
              </div>
              {error.stack && (
                <pre className="mt-2 whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                  {error.stack}
                </pre>
              )}
            </div>
          </details>
        )}
      </div>
    </div>
  );
}

// Hook for handling async errors
export function useAsyncError() {
  const [, setError] = React.useState();

  return React.useCallback((error: Error) => {
    setError(() => {
      throw error;
    });
  }, []);
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, "children">
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
}

// Error boundary for specific sections
interface SectionErrorBoundaryProps {
  children: React.ReactNode;
  sectionName: string;
  className?: string;
}

export function SectionErrorBoundary({
  children,
  sectionName,
  className,
}: SectionErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div
          className={`p-4 border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20 rounded-lg ${
            className || ""
          }`}
        >
          <div className="flex items-center space-x-2 text-red-700 dark:text-red-400 mb-2">
            <AlertTriangle className="w-5 h-5" />
            <h3 className="font-medium">Error in {sectionName}</h3>
          </div>
          <p className="text-sm text-red-600 dark:text-red-300 mb-3">
            {error.message}
          </p>
          <Button onClick={resetError} size="sm" variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
}
