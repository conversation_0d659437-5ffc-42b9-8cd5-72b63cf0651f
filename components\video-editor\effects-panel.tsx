"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>lider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Palette,
  Sparkles,
  Move3D,
  Shuffle,
  Eye,
  EyeOff,
  X,
  Play,
  RotateCcw,
} from "lucide-react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import type { Effect } from "@/types/video-editor";
import { cn } from "@/lib/utils";

interface EffectsPanelProps {
  className?: string;
}

// Predefined effects library
const EFFECT_PRESETS = {
  filters: [
    {
      id: "blur",
      name: "Blur",
      description: "Add gaussian blur effect",
      icon: Eye,
      parameters: { intensity: 5 },
    },
    {
      id: "sepia",
      name: "<PERSON><PERSON>",
      description: "Vintage sepia tone effect",
      icon: Palette,
      parameters: { intensity: 100 },
    },
    {
      id: "grayscale",
      name: "Grayscale",
      description: "Convert to black and white",
      icon: Palette,
      parameters: { intensity: 100 },
    },
    {
      id: "invert",
      name: "Invert",
      description: "Invert colors",
      icon: Palette,
      parameters: { intensity: 100 },
    },
  ],
  transitions: [
    {
      id: "fade",
      name: "Fade",
      description: "Smooth fade transition",
      icon: Sparkles,
      parameters: { duration: 1, type: "in" } as Record<
        string,
        number | string | boolean
      >,
    },
    {
      id: "slide",
      name: "Slide",
      description: "Slide transition effect",
      icon: Move3D,
      parameters: { duration: 1, direction: "left" } as Record<
        string,
        number | string | boolean
      >,
    },
    {
      id: "zoom",
      name: "Zoom",
      description: "Zoom in/out transition",
      icon: Move3D,
      parameters: { duration: 1, type: "in" } as Record<
        string,
        number | string | boolean
      >,
    },
    {
      id: "dissolve",
      name: "Dissolve",
      description: "Dissolve transition effect",
      icon: Sparkles,
      parameters: { duration: 1.5 } as Record<
        string,
        number | string | boolean
      >,
    },
  ],
};

export function EffectsPanel({ className }: EffectsPanelProps) {
  const timelineStore = useTimelineStore();
  const assetStore = useAssetStore();
  const [activeTab, setActiveTab] = useState("color");
  const [previewEffect, setPreviewEffect] = useState<string | null>(null);

  // Get selected clips and active clip
  const selectedClips = timelineStore.getSelectedClips();
  const activeClip = selectedClips.length > 0 ? selectedClips[0] : null;
  const activeAsset = activeClip
    ? assetStore.getAssetById(activeClip.assetId)
    : null;

  // Get current clip properties
  const brightness = activeClip?.properties.filters?.brightness || 100;
  const contrast = activeClip?.properties.filters?.contrast || 100;
  const saturation = activeClip?.properties.filters?.saturation || 100;
  const hue = activeClip?.properties.filters?.hue || 0;
  const scale = activeClip?.properties.scale || 1;
  const rotation = activeClip?.properties.rotation || 0;
  const opacity = activeClip?.properties.opacity || 1;
  const positionX = activeClip?.properties.position?.x || 0;
  const positionY = activeClip?.properties.position?.y || 0;

  // Color grading handlers
  const handleBrightnessChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        filters: {
          ...activeClip.properties.filters,
          brightness: value[0],
        },
      },
    });
  };

  const handleContrastChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        filters: {
          ...activeClip.properties.filters,
          contrast: value[0],
        },
      },
    });
  };

  const handleSaturationChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        filters: {
          ...activeClip.properties.filters,
          saturation: value[0],
        },
      },
    });
  };

  const handleHueChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        filters: {
          ...activeClip.properties.filters,
          hue: value[0],
        },
      },
    });
  };

  // Transform handlers
  const handleScaleChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        scale: value[0],
      },
    });
  };

  const handleRotationChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        rotation: value[0],
      },
    });
  };

  const handleOpacityChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        opacity: value[0] / 100,
      },
    });
  };

  const handlePositionXChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        position: {
          x: value[0],
          y: activeClip.properties.position?.y || 0,
        },
      },
    });
  };

  const handlePositionYChange = (value: number[]) => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        position: {
          x: activeClip.properties.position?.x || 0,
          y: value[0],
        },
      },
    });
  };

  // Effect management
  const applyEffect = (
    effectType: string,
    effectData: {
      name: string;
      parameters: Record<string, number | string | boolean>;
    }
  ) => {
    if (!activeClip) return;

    const newEffect: Omit<Effect, "id"> = {
      type: effectType,
      name: effectData.name,
      parameters: effectData.parameters,
      enabled: true,
    };

    timelineStore.addEffectToClip(activeClip.id, newEffect);
  };

  const removeEffect = (effectId: string) => {
    if (!activeClip) return;
    timelineStore.removeEffectFromClip(activeClip.id, effectId);
  };

  const toggleEffect = (effectId: string) => {
    if (!activeClip) return;
    const effect = activeClip.effects.find((e) => e.id === effectId);
    if (!effect) return;

    timelineStore.updateClipEffect(activeClip.id, effectId, {
      enabled: !effect.enabled,
    });
  };

  const resetColorGrading = () => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        filters: {
          ...activeClip.properties.filters,
          brightness: 100,
          contrast: 100,
          saturation: 100,
          hue: 0,
        },
      },
    });
  };

  const resetTransform = () => {
    if (!activeClip) return;
    timelineStore.updateClip(activeClip.id, {
      properties: {
        ...activeClip.properties,
        scale: 1,
        rotation: 0,
        opacity: 1,
        position: { x: 0, y: 0 },
      },
    });
  };

  if (!activeClip || !activeAsset) {
    return (
      <div className={cn("bg-gray-900 border-l border-gray-700", className)}>
        <div className="p-4">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Effects & Tools
          </h3>
          <div className="text-center py-8">
            <Sparkles className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400">Select a clip to apply effects</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "bg-gray-900 border-l border-gray-700 overflow-y-auto",
        className
      )}
    >
      <div className="p-4">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Sparkles className="w-5 h-5" />
          Effects & Tools
        </h3>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-4 w-full">
            <TabsTrigger value="color" className="text-xs">
              <Palette className="w-3 h-3 mr-1" />
              Color
            </TabsTrigger>
            <TabsTrigger value="effects" className="text-xs">
              <Sparkles className="w-3 h-3 mr-1" />
              Effects
            </TabsTrigger>
            <TabsTrigger value="transform" className="text-xs">
              <Move3D className="w-3 h-3 mr-1" />
              Transform
            </TabsTrigger>
            <TabsTrigger value="transitions" className="text-xs">
              <Shuffle className="w-3 h-3 mr-1" />
              Transitions
            </TabsTrigger>
          </TabsList>

          {/* Color Grading Tab */}
          <TabsContent value="color" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Color Grading</CardTitle>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={resetColorGrading}
                    className="h-6 px-2 text-xs"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    Reset
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Brightness</span>
                    <span className="text-xs text-gray-400">{brightness}%</span>
                  </div>
                  <Slider
                    value={[brightness]}
                    min={0}
                    max={200}
                    step={1}
                    onValueChange={handleBrightnessChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Contrast</span>
                    <span className="text-xs text-gray-400">{contrast}%</span>
                  </div>
                  <Slider
                    value={[contrast]}
                    min={0}
                    max={200}
                    step={1}
                    onValueChange={handleContrastChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Saturation</span>
                    <span className="text-xs text-gray-400">{saturation}%</span>
                  </div>
                  <Slider
                    value={[saturation]}
                    min={0}
                    max={200}
                    step={1}
                    onValueChange={handleSaturationChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Hue</span>
                    <span className="text-xs text-gray-400">{hue}°</span>
                  </div>
                  <Slider
                    value={[hue]}
                    min={-180}
                    max={180}
                    step={1}
                    onValueChange={handleHueChange}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Effects Tab */}
          <TabsContent value="effects" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Video Filters</CardTitle>
                <CardDescription className="text-xs">
                  Apply visual effects to your clip
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-2">
                  {EFFECT_PRESETS.filters.map((effect) => {
                    const Icon = effect.icon;
                    const isApplied = activeClip.effects.some(
                      (e) => e.type === effect.id
                    );

                    return (
                      <div
                        key={effect.id}
                        className={cn(
                          "bg-gray-800 rounded-lg p-3 hover:bg-gray-700 cursor-pointer transition-colors border",
                          isApplied
                            ? "border-purple-500 bg-purple-900/20"
                            : "border-transparent",
                          previewEffect === effect.id &&
                            "ring-2 ring-purple-400"
                        )}
                        onMouseEnter={() => setPreviewEffect(effect.id)}
                        onMouseLeave={() => setPreviewEffect(null)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Icon className="w-4 h-4 text-purple-400" />
                            <span className="text-white text-sm font-medium">
                              {effect.name}
                            </span>
                          </div>
                          {isApplied && (
                            <Badge variant="secondary" className="text-xs">
                              Applied
                            </Badge>
                          )}
                        </div>
                        <p className="text-gray-400 text-xs mb-2">
                          {effect.description}
                        </p>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => applyEffect(effect.id, effect)}
                            disabled={isApplied}
                            className="flex-1 bg-purple-600 hover:bg-purple-700 text-xs"
                          >
                            {isApplied ? "Applied" : "Apply"}
                          </Button>
                          {previewEffect === effect.id && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="px-2"
                            >
                              <Play className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Applied Effects */}
            {activeClip.effects.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Applied Effects</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {activeClip.effects.map((effect) => (
                      <div
                        key={effect.id}
                        className="flex items-center justify-between bg-gray-800 rounded-lg p-2"
                      >
                        <div className="flex items-center gap-2">
                          <span className="text-white text-sm">
                            {effect.name}
                          </span>
                          {!effect.enabled && (
                            <Badge variant="outline" className="text-xs">
                              Disabled
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleEffect(effect.id)}
                            className="h-6 w-6 p-0"
                          >
                            {effect.enabled ? (
                              <Eye className="w-3 h-3" />
                            ) : (
                              <EyeOff className="w-3 h-3" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeEffect(effect.id)}
                            className="h-6 w-6 p-0 text-red-400 hover:text-red-500"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Transform Tab */}
          <TabsContent value="transform" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Transform Controls</CardTitle>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={resetTransform}
                    className="h-6 px-2 text-xs"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    Reset
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Scale</span>
                    <span className="text-xs text-gray-400">
                      {scale.toFixed(2)}x
                    </span>
                  </div>
                  <Slider
                    value={[scale]}
                    min={0.1}
                    max={3}
                    step={0.01}
                    onValueChange={handleScaleChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Rotation</span>
                    <span className="text-xs text-gray-400">{rotation}°</span>
                  </div>
                  <Slider
                    value={[rotation]}
                    min={-180}
                    max={180}
                    step={1}
                    onValueChange={handleRotationChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Opacity</span>
                    <span className="text-xs text-gray-400">
                      {Math.round(opacity * 100)}%
                    </span>
                  </div>
                  <Slider
                    value={[opacity * 100]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={handleOpacityChange}
                    className="w-full"
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Position X</span>
                    <span className="text-xs text-gray-400">{positionX}px</span>
                  </div>
                  <Slider
                    value={[positionX]}
                    min={-500}
                    max={500}
                    step={1}
                    onValueChange={handlePositionXChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-400">Position Y</span>
                    <span className="text-xs text-gray-400">{positionY}px</span>
                  </div>
                  <Slider
                    value={[positionY]}
                    min={-500}
                    max={500}
                    step={1}
                    onValueChange={handlePositionYChange}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Transitions Tab */}
          <TabsContent value="transitions" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Transition Library</CardTitle>
                <CardDescription className="text-xs">
                  Add smooth transitions between clips
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-2">
                  {EFFECT_PRESETS.transitions.map((transition) => {
                    const Icon = transition.icon;

                    return (
                      <div
                        key={transition.id}
                        className={cn(
                          "bg-gray-800 rounded-lg p-3 hover:bg-gray-700 cursor-pointer transition-colors border border-transparent",
                          previewEffect === transition.id &&
                            "ring-2 ring-purple-400"
                        )}
                        onMouseEnter={() => setPreviewEffect(transition.id)}
                        onMouseLeave={() => setPreviewEffect(null)}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <Icon className="w-4 h-4 text-purple-400" />
                          <span className="text-white text-sm font-medium">
                            {transition.name}
                          </span>
                        </div>
                        <p className="text-gray-400 text-xs mb-2">
                          {transition.description}
                        </p>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() =>
                              applyEffect(transition.id, transition)
                            }
                            className="flex-1 bg-purple-600 hover:bg-purple-700 text-xs"
                          >
                            Apply
                          </Button>
                          {previewEffect === transition.id && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="px-2"
                            >
                              <Play className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
