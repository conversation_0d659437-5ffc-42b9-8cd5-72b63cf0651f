import { useEffect, useRef, useCallback } from "react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import type { PlayerRef } from "@remotion/player";

interface UseTimelineSyncOptions {
  playerRef?: React.RefObject<PlayerRef | null>;
  onTimeUpdate?: (time: number) => void;
  onPlayStateChange?: (isPlaying: boolean) => void;
}

export function useTimelineSync({
  playerRef,
  onTimeUpdate,
  onPlayStateChange,
}: UseTimelineSyncOptions = {}) {
  const timelineStore = useTimelineStore();
  const animationFrameRef = useRef<number | undefined>(undefined);
  const lastUpdateTimeRef = useRef<number>(0);

  // Sync playhead position with player
  const syncPlayheadWithPlayer = useCallback(() => {
    if (playerRef?.current && timelineStore.isPlaying) {
      const currentFrame = playerRef.current.getCurrentFrame();
      const fps = 30; // Default FPS, should match project settings
      const currentTime = currentFrame / fps;

      // Only update if time has changed significantly to avoid infinite loops
      if (Math.abs(currentTime - lastUpdateTimeRef.current) > 0.01) {
        lastUpdateTimeRef.current = currentTime;
        timelineStore.setPlayheadPosition(currentTime);
        onTimeUpdate?.(currentTime);
      }
    }

    if (timelineStore.isPlaying) {
      animationFrameRef.current = requestAnimationFrame(syncPlayheadWithPlayer);
    }
  }, [playerRef, timelineStore, onTimeUpdate]);

  // Start/stop sync based on play state
  useEffect(() => {
    if (timelineStore.isPlaying) {
      syncPlayheadWithPlayer();
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }

    onPlayStateChange?.(timelineStore.isPlaying);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [timelineStore.isPlaying, syncPlayheadWithPlayer, onPlayStateChange]);

  // Sync player position when playhead changes externally
  useEffect(() => {
    if (playerRef?.current && !timelineStore.isPlaying) {
      const fps = 30;
      const targetFrame = Math.floor(timelineStore.playheadPosition * fps);
      playerRef.current.seekTo(targetFrame);
    }
  }, [timelineStore.playheadPosition, playerRef, timelineStore.isPlaying]);

  // Control player playback state
  const play = useCallback(() => {
    timelineStore.setIsPlaying(true);
    if (playerRef?.current) {
      playerRef.current.play();
    }
  }, [timelineStore, playerRef]);

  const pause = useCallback(() => {
    timelineStore.setIsPlaying(false);
    if (playerRef?.current) {
      playerRef.current.pause();
    }
  }, [timelineStore, playerRef]);

  const seekTo = useCallback(
    (time: number) => {
      const clampedTime = Math.max(0, Math.min(time, timelineStore.duration));
      timelineStore.setPlayheadPosition(clampedTime);

      if (playerRef?.current) {
        const fps = 30;
        const targetFrame = Math.floor(clampedTime * fps);
        playerRef.current.seekTo(targetFrame);
      }
    },
    [timelineStore, playerRef]
  );

  const toggle = useCallback(() => {
    if (timelineStore.isPlaying) {
      pause();
    } else {
      play();
    }
  }, [timelineStore.isPlaying, play, pause]);

  return {
    isPlaying: timelineStore.isPlaying,
    playheadPosition: timelineStore.playheadPosition,
    duration: timelineStore.duration,
    play,
    pause,
    seekTo,
    toggle,
  };
}
