import { db, eq } from "@/lib/db";
import {
  videoProjects,
  videoProjectVersions,
  type VideoProject,
  type NewVideoProject,
  type VideoProjectVersion,
  type NewVideoProjectVersion,
  type VideoAsset,
  type VideoFolder,
} from "@/lib/schema";
import type {
  Project,
  ProjectSettings,
  TimelineData,
} from "@/types/video-editor";
import { v4 as uuidv4 } from "uuid";

type DbProjectWithRelations = VideoProject & {
  assets?: VideoAsset[];
  folders?: VideoFolder[];
};

export class ProjectService {
  /**
   * Create a new video project
   */
  static async createProject(
    name: string,
    userId: string,
    description?: string,
    settings?: Partial<ProjectSettings>
  ): Promise<Project> {
    const projectId = uuidv4();

    const defaultSettings: ProjectSettings = {
      width: 1920,
      height: 1080,
      fps: 30,
      duration: 60,
      backgroundColor: "#000000",
      ...settings,
    };

    const defaultTimeline: TimelineData = {
      tracks: [],
      duration: defaultSettings.duration,
      markers: [],
    };

    const newProject: NewVideoProject = {
      id: projectId,
      name,
      description: description || null,
      userId,
      settings: defaultSettings,
      timeline: defaultTimeline,
    };

    const [createdProject] = await db
      .insert(videoProjects)
      .values(newProject)
      .returning();

    return this.mapDbProjectToProject(createdProject);
  }

  /**
   * Get project by ID
   */
  static async getProject(
    projectId: string,
    userId: string
  ): Promise<Project | null> {
    const project = await db.query.videoProjects.findFirst({
      where: eq(videoProjects.id, projectId),
      with: {
        assets: true,
        folders: true,
      },
    });

    if (!project || project.userId !== userId) {
      return null;
    }

    return this.mapDbProjectToProject(project);
  }

  /**
   * Get all projects for a user
   */
  static async getUserProjects(userId: string): Promise<Project[]> {
    const projects = await db.query.videoProjects.findMany({
      where: eq(videoProjects.userId, userId),
      with: {
        assets: true,
        folders: true,
      },
      orderBy: (projects, { desc }) => [desc(projects.updatedAt)],
    });

    return projects.map(this.mapDbProjectToProject);
  }

  /**
   * Update project
   */
  static async updateProject(
    projectId: string,
    userId: string,
    updates: Partial<
      Pick<Project, "name" | "description" | "settings" | "timeline">
    >
  ): Promise<Project | null> {
    // Verify ownership
    const existingProject = await this.getProject(projectId, userId);
    if (!existingProject) {
      return null;
    }

    const updateData: Partial<NewVideoProject> = {};

    if (updates.name !== undefined) updateData.name = updates.name;
    if (updates.description !== undefined)
      updateData.description = updates.description;
    if (updates.settings !== undefined) updateData.settings = updates.settings;
    if (updates.timeline !== undefined) updateData.timeline = updates.timeline;

    const [updatedProject] = await db
      .update(videoProjects)
      .set(updateData)
      .where(eq(videoProjects.id, projectId))
      .returning();

    return this.mapDbProjectToProject(updatedProject);
  }

  /**
   * Delete project and all associated data
   */
  static async deleteProject(
    projectId: string,
    userId: string
  ): Promise<boolean> {
    // Verify ownership
    const project = await this.getProject(projectId, userId);
    if (!project) {
      return false;
    }

    // Delete project (cascade will handle related data)
    await db.delete(videoProjects).where(eq(videoProjects.id, projectId));

    return true;
  }

  /**
   * Create a project version/backup
   */
  static async createProjectVersion(
    projectId: string,
    userId: string,
    name?: string
  ): Promise<VideoProjectVersion | null> {
    const project = await this.getProject(projectId, userId);
    if (!project) {
      return null;
    }

    // Get current version count
    const existingVersions = await db.query.videoProjectVersions.findMany({
      where: eq(videoProjectVersions.projectId, projectId),
      orderBy: (versions, { desc }) => [desc(versions.versionNumber)],
    });

    const nextVersionNumber =
      existingVersions.length > 0 ? existingVersions[0].versionNumber + 1 : 1;

    const newVersion: NewVideoProjectVersion = {
      id: uuidv4(),
      projectId,
      versionNumber: nextVersionNumber,
      name: name || `Version ${nextVersionNumber}`,
      timeline: project.timeline,
      settings: project.settings,
    };

    const [createdVersion] = await db
      .insert(videoProjectVersions)
      .values(newVersion)
      .returning();

    return createdVersion;
  }

  /**
   * Get project versions
   */
  static async getProjectVersions(
    projectId: string,
    userId: string
  ): Promise<VideoProjectVersion[]> {
    // Verify ownership
    const project = await this.getProject(projectId, userId);
    if (!project) {
      return [];
    }

    return await db.query.videoProjectVersions.findMany({
      where: eq(videoProjectVersions.projectId, projectId),
      orderBy: (versions, { desc }) => [desc(versions.createdAt)],
    });
  }

  /**
   * Load a specific version
   */
  static async loadProjectVersion(
    versionId: string,
    userId: string
  ): Promise<{ timeline: TimelineData; settings: ProjectSettings } | null> {
    const version = await db.query.videoProjectVersions.findFirst({
      where: eq(videoProjectVersions.id, versionId),
      with: {
        project: true,
      },
    });

    if (!version || version.project.userId !== userId) {
      return null;
    }

    return {
      timeline: version.timeline as TimelineData,
      settings: version.settings as ProjectSettings,
    };
  }

  /**
   * Export project data for backup
   */
  static async exportProject(
    projectId: string,
    userId: string
  ): Promise<{ project: Project; versions: VideoProjectVersion[] } | null> {
    const project = await this.getProject(projectId, userId);
    if (!project) {
      return null;
    }

    const versions = await this.getProjectVersions(projectId, userId);

    return { project, versions };
  }

  /**
   * Import project from backup data
   */
  static async importProject(
    projectData: Project,
    userId: string,
    newName?: string
  ): Promise<Project> {
    const newProjectId = uuidv4();

    const importedProject: NewVideoProject = {
      id: newProjectId,
      name: newName || `${projectData.name} (Imported)`,
      description: projectData.description || null,
      userId,
      settings: projectData.settings,
      timeline: projectData.timeline,
    };

    const [createdProject] = await db
      .insert(videoProjects)
      .values(importedProject)
      .returning();

    return this.mapDbProjectToProject(createdProject);
  }

  /**
   * Map database project to application project type
   */
  private static mapDbProjectToProject(
    dbProject: DbProjectWithRelations
  ): Project {
    return {
      id: dbProject.id,
      name: dbProject.name,
      description: dbProject.description || undefined,
      settings: dbProject.settings as ProjectSettings,
      timeline: dbProject.timeline as TimelineData,
      assets: (dbProject.assets || []).map((asset) => ({
        id: asset.id,
        name: asset.name,
        type: asset.type as "video" | "audio" | "image" | "ai-generated",
        url: asset.url,
        thumbnailUrl: asset.thumbnailUrl || undefined,
        duration: asset.duration || undefined,
        metadata: {
          fileSize: 0,
          mimeType: "application/octet-stream",
          ...(asset.metadata as Record<string, unknown>),
        },
        userId: asset.userId,
        projectId: asset.projectId || undefined,
        folderId: asset.folderId || undefined,
        createdAt: asset.createdAt,
      })),
      createdAt: dbProject.createdAt,
      updatedAt: dbProject.updatedAt,
      userId: dbProject.userId,
    };
  }
}
