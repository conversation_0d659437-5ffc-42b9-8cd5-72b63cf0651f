"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON>,
  Volume2,
  Volume<PERSON>,
  <PERSON>,
  <PERSON>,
  Unlock,
  Settings,
  Move,
  ArrowUp,
  ArrowDown,
  Clock,
  Zap,
} from "lucide-react";
import {
  ContextMenu,
  useContextMenuItems,
  ContextMenuItem,
} from "@/components/ui/context-menu-enhanced";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useTimelineClipboard } from "@/lib/hooks/use-timeline-clipboard";
import { useTimelineEditing } from "@/lib/hooks/use-timeline-editing";
import type { Clip, Track } from "@/types/video-editor";

interface ClipContextMenuProps {
  clip: Clip;
  children: React.ReactNode;
}

export function ClipContextMenu({ clip, children }: ClipContextMenuProps) {
  const timelineStore = useTimelineStore();
  const clipboard = useTimelineClipboard();
  const editing = useTimelineEditing();
  const { createItem, createSeparator } = useContextMenuItems();

  const isSelected = timelineStore.selectedClips.includes(clip.id);
  const selectedCount = timelineStore.selectedClips.length;

  const handleSelectClip = () => {
    if (!isSelected) {
      timelineStore.selectClip(clip.id);
    }
  };

  const handleCopy = () => {
    if (!isSelected) {
      timelineStore.selectClip(clip.id);
    }
    clipboard.copyClips();
  };

  const handleCut = () => {
    if (!isSelected) {
      timelineStore.selectClip(clip.id);
    }
    clipboard.cutClips();
  };

  const handleDelete = () => {
    if (!isSelected) {
      timelineStore.selectClip(clip.id);
    }
    editing.deleteSelectedClips();
  };

  const handleDuplicate = () => {
    if (!isSelected) {
      timelineStore.selectClip(clip.id);
    }
    clipboard.duplicateClips();
  };

  const handleSplit = () => {
    const splitTime = timelineStore.playheadPosition;
    if (splitTime >= clip.startTime && splitTime <= clip.endTime) {
      timelineStore.splitClip(clip.id, splitTime);
    }
  };

  const handleMoveToTrack = (direction: "up" | "down") => {
    const currentTrack = timelineStore.tracks.find((track) =>
      track.clips.some((c) => c.id === clip.id)
    );

    if (!currentTrack) return;

    const currentIndex = timelineStore.tracks.findIndex(
      (t) => t.id === currentTrack.id
    );
    const targetIndex =
      direction === "up" ? currentIndex - 1 : currentIndex + 1;

    if (targetIndex >= 0 && targetIndex < timelineStore.tracks.length) {
      const targetTrack = timelineStore.tracks[targetIndex];
      timelineStore.moveClip(clip.id, targetTrack.id, clip.startTime);
    }
  };

  const handleSetInPoint = () => {
    const currentTime = timelineStore.playheadPosition;
    if (currentTime >= clip.startTime && currentTime < clip.endTime) {
      timelineStore.updateClip(clip.id, {
        trimStart: clip.trimStart + (currentTime - clip.startTime),
        startTime: currentTime,
      });
    }
  };

  const handleSetOutPoint = () => {
    const currentTime = timelineStore.playheadPosition;
    if (currentTime > clip.startTime && currentTime <= clip.endTime) {
      timelineStore.updateClip(clip.id, {
        trimEnd: clip.trimEnd - (clip.endTime - currentTime),
        endTime: currentTime,
      });
    }
  };

  const items: ContextMenuItem[] = [
    createItem(
      "select",
      isSelected ? `${selectedCount} clip(s) selected` : "Select Clip",
      handleSelectClip,
      {
        disabled: isSelected,
        icon: <Move className="w-4 h-4" />,
      }
    ),

    createSeparator("sep1"),

    createItem("copy", "Copy", handleCopy, {
      icon: <Copy className="w-4 h-4" />,
      shortcut: "Ctrl+C",
    }),

    createItem("cut", "Cut", handleCut, {
      icon: <Scissors className="w-4 h-4" />,
      shortcut: "Ctrl+X",
    }),

    createItem("duplicate", "Duplicate", handleDuplicate, {
      icon: <Copy className="w-4 h-4" />,
      shortcut: "Ctrl+D",
    }),

    createSeparator("sep2"),

    createItem("split", "Split at Playhead", handleSplit, {
      icon: <Scissors className="w-4 h-4" />,
      shortcut: "S",
      disabled: !(
        timelineStore.playheadPosition >= clip.startTime &&
        timelineStore.playheadPosition <= clip.endTime
      ),
    }),

    createItem("delete", "Delete", handleDelete, {
      icon: <Trash2 className="w-4 h-4" />,
      shortcut: "Del",
      danger: true,
    }),

    createSeparator("sep3"),

    createItem("setIn", "Set In Point", handleSetInPoint, {
      icon: <Clock className="w-4 h-4" />,
      shortcut: "I",
      disabled: !(
        timelineStore.playheadPosition >= clip.startTime &&
        timelineStore.playheadPosition < clip.endTime
      ),
    }),

    createItem("setOut", "Set Out Point", handleSetOutPoint, {
      icon: <Clock className="w-4 h-4" />,
      shortcut: "O",
      disabled: !(
        timelineStore.playheadPosition > clip.startTime &&
        timelineStore.playheadPosition <= clip.endTime
      ),
    }),

    createSeparator("sep4"),

    createItem("moveUp", "Move to Track Above", () => handleMoveToTrack("up"), {
      icon: <ArrowUp className="w-4 h-4" />,
      shortcut: "Shift+↑",
      disabled:
        timelineStore.tracks.findIndex((track) =>
          track.clips.some((c) => c.id === clip.id)
        ) === 0,
    }),

    createItem(
      "moveDown",
      "Move to Track Below",
      () => handleMoveToTrack("down"),
      {
        icon: <ArrowDown className="w-4 h-4" />,
        shortcut: "Shift+↓",
        disabled:
          timelineStore.tracks.findIndex((track) =>
            track.clips.some((c) => c.id === clip.id)
          ) ===
          timelineStore.tracks.length - 1,
      }
    ),

    createSeparator("sep5"),

    createItem(
      "properties",
      "Properties",
      () => {
        // Open clip properties panel
      },
      {
        icon: <Settings className="w-4 h-4" />,
      }
    ),
  ];

  return <ContextMenu items={items}>{children}</ContextMenu>;
}

interface TrackContextMenuProps {
  track: Track;
  children: React.ReactNode;
}

export function TrackContextMenu({ track, children }: TrackContextMenuProps) {
  const timelineStore = useTimelineStore();
  const { createItem, createSeparator } = useContextMenuItems();

  const handleToggleMute = () => {
    timelineStore.updateTrack(track.id, { muted: !track.muted });
  };

  const handleToggleLock = () => {
    timelineStore.updateTrack(track.id, { locked: !track.locked });
  };

  const handleToggleVisibility = () => {
    // Implement track visibility toggle
  };

  const handleDuplicateTrack = () => {
    timelineStore.addTrack({
      type: track.type,
      name: `${track.name} Copy`,
      clips: [],
      muted: track.muted,
      locked: false,
      height: track.height,
    });
  };

  const handleDeleteTrack = () => {
    if (timelineStore.tracks.length > 1) {
      timelineStore.removeTrack(track.id);
    }
  };

  const handleSelectAllClips = () => {
    const clipIds = track.clips.map((clip) => clip.id);
    timelineStore.selectClips(clipIds);
  };

  const items: ContextMenuItem[] = [
    createItem("selectAll", "Select All Clips", handleSelectAllClips, {
      icon: <Move className="w-4 h-4" />,
      disabled: track.clips.length === 0,
    }),

    createSeparator("sep1"),

    createItem(
      "mute",
      track.muted ? "Unmute Track" : "Mute Track",
      handleToggleMute,
      {
        icon: track.muted ? (
          <Volume2 className="w-4 h-4" />
        ) : (
          <VolumeX className="w-4 h-4" />
        ),
        shortcut: "M",
      }
    ),

    createItem(
      "lock",
      track.locked ? "Unlock Track" : "Lock Track",
      handleToggleLock,
      {
        icon: track.locked ? (
          <Unlock className="w-4 h-4" />
        ) : (
          <Lock className="w-4 h-4" />
        ),
        shortcut: "L",
      }
    ),

    createItem("visibility", "Toggle Visibility", handleToggleVisibility, {
      icon: <Eye className="w-4 h-4" />,
      shortcut: "V",
    }),

    createSeparator("sep2"),

    createItem("duplicate", "Duplicate Track", handleDuplicateTrack, {
      icon: <Copy className="w-4 h-4" />,
    }),

    createItem("delete", "Delete Track", handleDeleteTrack, {
      icon: <Trash2 className="w-4 h-4" />,
      danger: true,
      disabled: timelineStore.tracks.length <= 1,
    }),

    createSeparator("sep3"),

    createItem(
      "properties",
      "Track Properties",
      () => {
        // Open track properties panel
      },
      {
        icon: <Settings className="w-4 h-4" />,
      }
    ),
  ];

  return <ContextMenu items={items}>{children}</ContextMenu>;
}

interface TimelineContextMenuProps {
  children: React.ReactNode;
}

export function TimelineContextMenu({ children }: TimelineContextMenuProps) {
  const timelineStore = useTimelineStore();
  const clipboard = useTimelineClipboard();
  const { createItem, createSeparator } = useContextMenuItems();

  const handlePaste = () => {
    clipboard.pasteClips();
  };

  const handleAddMarker = () => {
    timelineStore.addMarker({
      time: timelineStore.playheadPosition,
      label: `Marker ${timelineStore.markers.length + 1}`,
      color: "#3b82f6",
    });
  };

  const handleAddVideoTrack = () => {
    timelineStore.addTrack({
      type: "video",
      name: `Video Track ${
        timelineStore.tracks.filter((t) => t.type === "video").length + 1
      }`,
      clips: [],
      muted: false,
      locked: false,
      height: 100,
    });
  };

  const handleAddAudioTrack = () => {
    timelineStore.addTrack({
      type: "audio",
      name: `Audio Track ${
        timelineStore.tracks.filter((t) => t.type === "audio").length + 1
      }`,
      clips: [],
      muted: false,
      locked: false,
      height: 80,
    });
  };

  const handleSelectAll = () => {
    const allClipIds: string[] = [];
    timelineStore.tracks.forEach((track) => {
      track.clips.forEach((clip) => {
        allClipIds.push(clip.id);
      });
    });
    timelineStore.selectClips(allClipIds);
  };

  const items: ContextMenuItem[] = [
    createItem("paste", "Paste", handlePaste, {
      icon: <Copy className="w-4 h-4" />,
      shortcut: "Ctrl+V",
    }),

    createSeparator("sep1"),

    createItem("addMarker", "Add Marker", handleAddMarker, {
      icon: <Zap className="w-4 h-4" />,
      shortcut: "M",
    }),

    createSeparator("sep2"),

    createItem("addVideoTrack", "Add Video Track", handleAddVideoTrack, {
      icon: <ArrowUp className="w-4 h-4" />,
    }),

    createItem("addAudioTrack", "Add Audio Track", handleAddAudioTrack, {
      icon: <Volume2 className="w-4 h-4" />,
    }),

    createSeparator("sep3"),

    createItem("selectAll", "Select All", handleSelectAll, {
      icon: <Move className="w-4 h-4" />,
      shortcut: "Ctrl+A",
    }),
  ];

  return <ContextMenu items={items}>{children}</ContextMenu>;
}
