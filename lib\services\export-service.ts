import type {
  ExportSettings,
  Project,
  Track,
  Clip,
  Asset,
} from "@/types/video-editor";
import { v4 as uuidv4 } from "uuid";

export interface ExportJob {
  id: string;
  projectId: string;
  userId: string;
  settings: ExportSettings;
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  progress: number;
  outputUrl?: string;
  errorMessage?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface RemotionComposition {
  id: string;
  width: number;
  height: number;
  fps: number;
  durationInFrames: number;
  tracks: RemotionTrack[];
  backgroundColor: string;
}

export interface RemotionTrack {
  id: string;
  type: "video" | "audio" | "effects";
  sequences: RemotionSequence[];
}

export interface RemotionSequence {
  id: string;
  from: number;
  durationInFrames: number;
  component: string;
  props: Record<string, unknown>;
  assetUrl?: string;
  effects?: RemotionEffect[];
}

export interface RemotionEffect {
  type: string;
  parameters: Record<string, unknown>;
}

export class ExportService {
  private static exportJobs = new Map<string, ExportJob>();

  /**
   * Start a new export job
   */
  static async startExport(
    project: Project,
    settings: ExportSettings,
    userId: string
  ): Promise<string> {
    const exportId = uuidv4();

    const exportJob: ExportJob = {
      id: exportId,
      projectId: project.id,
      userId,
      settings,
      status: "pending",
      progress: 0,
      createdAt: new Date(),
    };

    this.exportJobs.set(exportId, exportJob);

    // Start the export process asynchronously
    this.processExport(exportId, project, settings).catch((error) => {
      console.error(`Export ${exportId} failed:`, error);
      this.updateExportJob(exportId, {
        status: "failed",
        errorMessage: error.message,
      });
    });

    return exportId;
  }

  /**
   * Get export job status
   */
  static getExportJob(exportId: string): ExportJob | null {
    return this.exportJobs.get(exportId) || null;
  }

  /**
   * Cancel an export job
   */
  static async cancelExport(exportId: string): Promise<boolean> {
    const job = this.exportJobs.get(exportId);
    if (!job) return false;

    if (job.status === "processing" || job.status === "pending") {
      this.updateExportJob(exportId, {
        status: "cancelled",
      });
      return true;
    }

    return false;
  }

  /**
   * Process the export job
   */
  private static async processExport(
    exportId: string,
    project: Project,
    settings: ExportSettings
  ): Promise<void> {
    this.updateExportJob(exportId, { status: "processing", progress: 10 });

    try {
      // Step 1: Convert project to Remotion composition
      const composition = this.createRemotionComposition(project, settings);
      this.updateExportJob(exportId, { progress: 30 });

      // Step 2: Validate composition
      this.validateComposition(composition);
      this.updateExportJob(exportId, { progress: 40 });

      // Step 3: Render with Remotion
      const outputUrl = await this.renderComposition(
        exportId,
        composition,
        settings
      );
      this.updateExportJob(exportId, { progress: 90 });

      // Step 4: Post-process if needed
      await this.postProcessVideo();
      this.updateExportJob(exportId, {
        status: "completed",
        progress: 100,
        outputUrl,
        completedAt: new Date(),
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Convert project timeline to Remotion composition
   */
  private static createRemotionComposition(
    project: Project,
    settings: ExportSettings
  ): RemotionComposition {
    const projectSettings = project.settings;
    const timeline = project.timeline;

    // Calculate duration in frames
    const durationInFrames = Math.ceil(timeline.duration * settings.fps);

    const composition: RemotionComposition = {
      id: `composition-${project.id}`,
      width: settings.resolution.width,
      height: settings.resolution.height,
      fps: settings.fps,
      durationInFrames,
      backgroundColor: projectSettings.backgroundColor,
      tracks: [],
    };

    // Convert each track
    timeline.tracks.forEach((track) => {
      const remotionTrack = this.convertTrackToRemotion(
        track,
        project.assets,
        settings
      );
      if (remotionTrack.sequences.length > 0) {
        composition.tracks.push(remotionTrack);
      }
    });

    return composition;
  }

  /**
   * Convert a timeline track to Remotion track
   */
  private static convertTrackToRemotion(
    track: Track,
    assets: Asset[],
    settings: ExportSettings
  ): RemotionTrack {
    const remotionTrack: RemotionTrack = {
      id: track.id,
      type: track.type,
      sequences: [],
    };

    track.clips.forEach((clip) => {
      const asset = assets.find((a) => a.id === clip.assetId);
      if (!asset) return;

      const sequence = this.convertClipToSequence(clip, asset, settings);
      if (sequence) {
        remotionTrack.sequences.push(sequence);
      }
    });

    return remotionTrack;
  }

  /**
   * Convert a clip to Remotion sequence
   */
  private static convertClipToSequence(
    clip: Clip,
    asset: Asset,
    settings: ExportSettings
  ): RemotionSequence | null {
    const fromFrame = Math.floor(clip.startTime * settings.fps);
    const durationInFrames = Math.floor(
      (clip.endTime - clip.startTime) * settings.fps
    );

    if (durationInFrames <= 0) return null;

    const sequence: RemotionSequence = {
      id: clip.id,
      from: fromFrame,
      durationInFrames,
      component: this.getComponentForAssetType(asset.type),
      assetUrl: asset.url,
      props: {
        src: asset.url,
        trimStart: clip.trimStart,
        trimEnd: clip.trimEnd,
        volume: clip.properties.volume || 1,
        opacity: clip.properties.opacity || 1,
        scale: clip.properties.scale || 1,
        rotation: clip.properties.rotation || 0,
        position: clip.properties.position || { x: 0, y: 0 },
        filters: clip.properties.filters || {},
      },
    };

    // Convert effects
    if (clip.effects && clip.effects.length > 0) {
      sequence.effects = clip.effects.map((effect) => ({
        type: effect.type,
        parameters: effect.parameters,
      }));
    }

    return sequence;
  }

  /**
   * Get the appropriate Remotion component for asset type
   */
  private static getComponentForAssetType(assetType: string): string {
    switch (assetType) {
      case "video":
        return "VideoSequence";
      case "audio":
        return "AudioSequence";
      case "image":
        return "ImageSequence";
      case "ai-generated":
        return "VideoSequence"; // AI-generated content is typically video
      default:
        return "VideoSequence";
    }
  }

  /**
   * Validate the composition before rendering
   */
  private static validateComposition(composition: RemotionComposition): void {
    if (composition.tracks.length === 0) {
      throw new Error("No tracks found in composition");
    }

    if (composition.durationInFrames <= 0) {
      throw new Error("Invalid composition duration");
    }

    // Validate each track
    composition.tracks.forEach((track, trackIndex) => {
      if (track.sequences.length === 0) {
        console.warn(`Track ${trackIndex} has no sequences`);
      }

      track.sequences.forEach((sequence, sequenceIndex) => {
        if (sequence.durationInFrames <= 0) {
          throw new Error(
            `Invalid sequence duration at track ${trackIndex}, sequence ${sequenceIndex}`
          );
        }

        if (sequence.from < 0) {
          throw new Error(
            `Invalid sequence start time at track ${trackIndex}, sequence ${sequenceIndex}`
          );
        }
      });
    });
  }

  /**
   * Render the composition using Remotion
   */
  private static async renderComposition(
    exportId: string,
    composition: RemotionComposition,
    settings: ExportSettings
  ): Promise<string> {
    // In a real implementation, this would call Remotion's server-side rendering
    // For now, we'll simulate the rendering process

    const renderStartTime = Date.now();
    const estimatedRenderTime = this.estimateRenderTime(composition, settings);

    return new Promise((resolve, reject) => {
      const updateProgress = () => {
        const elapsed = Date.now() - renderStartTime;
        const progress = Math.min(
          90,
          40 + (elapsed / estimatedRenderTime) * 50
        );

        const job = this.exportJobs.get(exportId);
        if (job?.status === "cancelled") {
          reject(new Error("Export was cancelled"));
          return;
        }

        this.updateExportJob(exportId, { progress });

        if (elapsed < estimatedRenderTime) {
          setTimeout(updateProgress, 1000);
        } else {
          // Simulate successful render
          const outputUrl = `/api/video-editor/export/download/${exportId}`;
          resolve(outputUrl);
        }
      };

      updateProgress();
    });
  }

  /**
   * Estimate render time based on composition complexity
   */
  private static estimateRenderTime(
    composition: RemotionComposition,
    settings: ExportSettings
  ): number {
    const baseTime = 5000; // 5 seconds base
    const frameTime = composition.durationInFrames * 50; // 50ms per frame
    const trackTime = composition.tracks.length * 2000; // 2 seconds per track
    const sequenceTime = composition.tracks.reduce(
      (sum, track) => sum + track.sequences.length * 500, // 500ms per sequence
      0
    );

    const qualityMultiplier = {
      low: 0.5,
      medium: 1,
      high: 1.5,
      ultra: 2.5,
    }[settings.quality];

    const resolutionMultiplier =
      (settings.resolution.width * settings.resolution.height) / (1920 * 1080);

    return (
      (baseTime + frameTime + trackTime + sequenceTime) *
      qualityMultiplier *
      Math.sqrt(resolutionMultiplier)
    );
  }

  /**
   * Post-process the rendered video
   */
  private static async postProcessVideo(): Promise<void> {
    // In a real implementation, this might:
    // - Apply additional compression
    // - Add watermarks
    // - Generate thumbnails
    // - Upload to cloud storage

    // For now, just simulate post-processing
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  /**
   * Update export job status
   */
  private static updateExportJob(
    exportId: string,
    updates: Partial<ExportJob>
  ): void {
    const job = this.exportJobs.get(exportId);
    if (job) {
      Object.assign(job, updates);
      this.exportJobs.set(exportId, job);
    }
  }

  /**
   * Clean up old export jobs
   */
  static cleanupOldJobs(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();

    for (const [exportId, job] of this.exportJobs.entries()) {
      const jobAge = now - job.createdAt.getTime();
      if (jobAge > maxAge) {
        this.exportJobs.delete(exportId);
      }
    }
  }

  /**
   * Get all export jobs for a user
   */
  static getUserExportJobs(userId: string): ExportJob[] {
    return Array.from(this.exportJobs.values())
      .filter((job) => job.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
}
