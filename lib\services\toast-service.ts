import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
  persistent?: boolean;
  action?: ToastAction;
  timestamp: Date;
}

export type ToastType = "success" | "error" | "warning" | "info" | "loading";

export interface ToastAction {
  label: string;
  action: () => void;
}

interface ToastState {
  toasts: Toast[];
  maxToasts: number;
  defaultDuration: number;
  
  // Actions
  addToast: (toast: Omit<Toast, "id" | "timestamp">) => string;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
  updateToast: (id: string, updates: Partial<Toast>) => void;
}

export const useToast = create<ToastState>()(
  devtools((set, get) => ({
    toasts: [],
    maxToasts: 5,
    defaultDuration: 5000,

    addToast: (toastData) => {
      const id = crypto.randomUUID();
      const toast: Toast = {
        ...toastData,
        id,
        timestamp: new Date(),
        duration: toastData.duration ?? get().defaultDuration,
      };

      set((state) => {
        const newToasts = [...state.toasts, toast];
        
        // Limit the number of toasts
        if (newToasts.length > state.maxToasts) {
          newToasts.shift();
        }

        return { toasts: newToasts };
      });

      // Auto-remove toast after duration (unless persistent or loading)
      if (!toast.persistent && toast.type !== "loading" && toast.duration && toast.duration > 0) {
        setTimeout(() => {
          get().removeToast(id);
        }, toast.duration);
      }

      return id;
    },

    removeToast: (id) => {
      set((state) => ({
        toasts: state.toasts.filter(toast => toast.id !== id),
      }));
    },

    clearAllToasts: () => {
      set({ toasts: [] });
    },

    updateToast: (id, updates) => {
      set((state) => ({
        toasts: state.toasts.map(toast =>
          toast.id === id ? { ...toast, ...updates } : toast
        ),
      }));
    },
  }))
);

// Toast utility functions
export const toast = {
  success: (title: string, description?: string, action?: ToastAction) => {
    return useToast.getState().addToast({
      type: "success",
      title,
      description,
      action,
    });
  },

  error: (title: string, description?: string, action?: ToastAction) => {
    return useToast.getState().addToast({
      type: "error",
      title,
      description,
      action,
      duration: 8000, // Longer duration for errors
    });
  },

  warning: (title: string, description?: string, action?: ToastAction) => {
    return useToast.getState().addToast({
      type: "warning",
      title,
      description,
      action,
    });
  },

  info: (title: string, description?: string, action?: ToastAction) => {
    return useToast.getState().addToast({
      type: "info",
      title,
      description,
      action,
    });
  },

  loading: (title: string, description?: string) => {
    return useToast.getState().addToast({
      type: "loading",
      title,
      description,
      persistent: true,
    });
  },

  dismiss: (id: string) => {
    useToast.getState().removeToast(id);
  },

  update: (id: string, updates: Partial<Toast>) => {
    useToast.getState().updateToast(id, updates);
  },

  promise: <T>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: Error) => string);
    }
  ): Promise<T> => {
    const loadingToastId = toast.loading(loading);

    return promise
      .then((data) => {
        toast.dismiss(loadingToastId);
        const successMessage = typeof success === "function" ? success(data) : success;
        toast.success(successMessage);
        return data;
      })
      .catch((err) => {
        toast.dismiss(loadingToastId);
        const errorMessage = typeof error === "function" ? error(err) : error;
        toast.error(errorMessage);
        throw err;
      });
  },
};

// Progress toast for long-running operations
export const createProgressToast = (title: string, description?: string) => {
  const toastId = useToast.getState().addToast({
    type: "loading",
    title,
    description,
    persistent: true,
  });

  return {
    id: toastId,
    update: (progress: number, newDescription?: string) => {
      const progressDescription = `${newDescription || description} (${Math.round(progress)}%)`;
      toast.update(toastId, {
        description: progressDescription,
      });
    },
    success: (successTitle?: string, successDescription?: string) => {
      toast.update(toastId, {
        type: "success",
        title: successTitle || title,
        description: successDescription,
        persistent: false,
        duration: 3000,
      });
    },
    error: (errorTitle?: string, errorDescription?: string) => {
      toast.update(toastId, {
        type: "error",
        title: errorTitle || title,
        description: errorDescription,
        persistent: false,
        duration: 8000,
      });
    },
    dismiss: () => {
      toast.dismiss(toastId);
    },
  };
};
