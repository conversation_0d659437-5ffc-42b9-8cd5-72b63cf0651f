{"name": "visual-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "tsx lib/migrate.ts", "db:push": "drizzle-kit push", "reset-credits": "tsx scripts/reset-credits.ts", "reset-credits-api": "node --experimental-modules scripts/reset-credits.js"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.1", "@fal-ai/client": "^1.5.0", "@fal-ai/server-proxy": "^1.1.1", "@ffmpeg/ffmpeg": "^0.12.15", "@google/genai": "^1.5.1", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@remotion/cli": "^4.0.314", "@remotion/media-utils": "^4.0.314", "@remotion/player": "^4.0.314", "@types/fluent-ffmpeg": "^2.1.27", "@vercel/analytics": "^1.5.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.23.12", "lucide-react": "^0.511.0", "next": "15.3.2", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "remotion": "^4.0.314", "sonner": "^2.0.7", "tailwind-merge": "^3.3.0", "together-ai": "^0.16.0", "uuid": "^11.1.0", "visual-app": "file:", "zod": "^3.25.30", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/node": "^20.19.0", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.3.2", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.0", "typescript": "^5"}}