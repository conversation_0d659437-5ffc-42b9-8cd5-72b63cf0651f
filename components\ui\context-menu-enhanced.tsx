"use client";

import React, { useState, useEffect, useRef, ReactNode } from "react";
import { createPortal } from "react-dom";
import { cn } from "@/lib/utils";

export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: ReactNode;
  shortcut?: string;
  disabled?: boolean;
  danger?: boolean;
  separator?: boolean;
  submenu?: ContextMenuItem[];
  action?: () => void;
}

interface ContextMenuProps {
  items: ContextMenuItem[];
  children: ReactNode;
  className?: string;
}

interface ContextMenuState {
  isOpen: boolean;
  position: { x: number; y: number };
  submenuPosition?: { x: number; y: number };
  activeSubmenu?: string;
}

export function ContextMenu({ items, children, className }: ContextMenuProps) {
  const [state, setState] = useState<ContextMenuState>({
    isOpen: false,
    position: { x: 0, y: 0 },
  });

  const menuRef = useRef<HTMLDivElement>(null);
  const submenuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    const x = event.clientX;
    const y = event.clientY;

    setState({
      isOpen: true,
      position: { x, y },
      activeSubmenu: undefined,
      submenuPosition: undefined,
    });
  };

  const handleItemClick = (item: ContextMenuItem) => {
    if (item.disabled || item.separator) return;

    if (item.submenu) {
      // Handle submenu
      return;
    }

    if (item.action) {
      item.action();
    }

    setState((prev) => ({ ...prev, isOpen: false }));
  };

  const handleSubmenuHover = (
    item: ContextMenuItem,
    event: React.MouseEvent
  ) => {
    if (!item.submenu || item.disabled) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const submenuX = rect.right + 5;
    const submenuY = rect.top;

    setState((prev) => ({
      ...prev,
      activeSubmenu: item.id,
      submenuPosition: { x: submenuX, y: submenuY },
    }));
  };

  const handleMouseLeave = () => {
    setState((prev) => ({
      ...prev,
      activeSubmenu: undefined,
      submenuPosition: undefined,
    }));
  };

  const closeMenu = () => {
    setState((prev) => ({ ...prev, isOpen: false }));
  };

  // Close menu on outside click
  useEffect(() => {
    if (!state.isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        submenuRef.current &&
        !submenuRef.current.contains(event.target as Node)
      ) {
        closeMenu();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        closeMenu();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [state.isOpen]);

  // Adjust menu position to stay within viewport
  const getAdjustedPosition = (x: number, y: number) => {
    const menuWidth = 200; // Approximate menu width
    const menuHeight = items.length * 32; // Approximate item height
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let adjustedX = x;
    let adjustedY = y;

    if (x + menuWidth > viewportWidth) {
      adjustedX = x - menuWidth;
    }

    if (y + menuHeight > viewportHeight) {
      adjustedY = y - menuHeight;
    }

    return { x: Math.max(0, adjustedX), y: Math.max(0, adjustedY) };
  };

  const renderMenuItem = (item: ContextMenuItem) => {
    if (item.separator) {
      return (
        <div key={item.id} className="h-px bg-gray-200 dark:bg-gray-700 my-1" />
      );
    }

    return (
      <div
        key={item.id}
        className={cn(
          "flex items-center justify-between px-3 py-2 text-sm cursor-pointer transition-colors",
          item.disabled
            ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
            : item.danger
            ? "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800",
          item.submenu && "relative"
        )}
        onClick={() => handleItemClick(item)}
        onMouseEnter={(e) => handleSubmenuHover(item, e)}
      >
        <div className="flex items-center space-x-2">
          {item.icon && (
            <span className="w-4 h-4 flex items-center justify-center">
              {item.icon}
            </span>
          )}
          <span>{item.label}</span>
        </div>

        <div className="flex items-center space-x-2">
          {item.shortcut && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {item.shortcut}
            </span>
          )}
          {item.submenu && <span className="text-gray-400">▶</span>}
        </div>
      </div>
    );
  };

  const renderSubmenu = () => {
    if (!state.activeSubmenu || !state.submenuPosition) return null;

    const activeItem = items.find((item) => item.id === state.activeSubmenu);
    if (!activeItem?.submenu) return null;

    const adjustedPosition = getAdjustedPosition(
      state.submenuPosition.x,
      state.submenuPosition.y
    );

    return createPortal(
      <div
        ref={submenuRef}
        className="fixed z-[60] min-w-[180px] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1"
        style={{
          left: adjustedPosition.x,
          top: adjustedPosition.y,
        }}
        onMouseLeave={handleMouseLeave}
      >
        {activeItem.submenu.map(renderMenuItem)}
      </div>,
      document.body
    );
  };

  const adjustedPosition = getAdjustedPosition(
    state.position.x,
    state.position.y
  );

  return (
    <>
      <div
        ref={triggerRef}
        className={className}
        onContextMenu={handleContextMenu}
      >
        {children}
      </div>

      {state.isOpen &&
        createPortal(
          <div
            ref={menuRef}
            className="fixed z-50 min-w-[180px] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1"
            style={{
              left: adjustedPosition.x,
              top: adjustedPosition.y,
            }}
          >
            {items.map(renderMenuItem)}
          </div>,
          document.body
        )}

      {renderSubmenu()}
    </>
  );
}

// Hook for creating context menu items with shortcuts
export function useContextMenuItems() {
  const createItem = (
    id: string,
    label: string,
    action?: () => void,
    options?: {
      icon?: ReactNode;
      shortcut?: string;
      disabled?: boolean;
      danger?: boolean;
      submenu?: ContextMenuItem[];
    }
  ): ContextMenuItem => ({
    id,
    label,
    action,
    ...options,
  });

  const createSeparator = (id: string): ContextMenuItem => ({
    id,
    label: "",
    separator: true,
  });

  return { createItem, createSeparator };
}
