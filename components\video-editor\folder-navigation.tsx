"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useAssetStore } from "@/lib/stores/asset-store";
import type { Folder as FolderType } from "@/types/video-editor";
import {
  ChevronRight,
  Edit,
  Folder,
  FolderOpen,
  Home,
  Trash2,
} from "lucide-react";
import { useState } from "react";

interface FolderNavigationProps {
  className?: string;
}

export function FolderNavigation({ className }: FolderNavigationProps) {
  const assetStore = useAssetStore();
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set()
  );

  const currentFolder = assetStore.currentFolderId
    ? assetStore.getFolderById(assetStore.currentFolderId)
    : null;

  const breadcrumbs = currentFolder
    ? assetStore.getFolderHierarchy(currentFolder.id)
    : [];

  const rootFolders = assetStore.folders.filter((folder) => !folder.parentId);

  const getSubfolders = (parentId: string) => {
    return assetStore.folders.filter((folder) => folder.parentId === parentId);
  };

  const toggleFolderExpansion = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleFolderClick = (folderId: string | null) => {
    assetStore.setCurrentFolder(folderId);
  };

  const handleDeleteFolder = async (folder: FolderType) => {
    const assetsInFolder = assetStore.getAssetsByFolder(folder.id);
    const subfolders = getSubfolders(folder.id);

    let confirmMessage = `Are you sure you want to delete "${folder.name}"?`;

    if (assetsInFolder.length > 0 || subfolders.length > 0) {
      confirmMessage += `\n\nThis will also delete:`;
      if (assetsInFolder.length > 0) {
        confirmMessage += `\n- ${assetsInFolder.length} asset(s)`;
      }
      if (subfolders.length > 0) {
        confirmMessage += `\n- ${subfolders.length} subfolder(s)`;
      }
    }

    if (confirm(confirmMessage)) {
      try {
        await assetStore.deleteFolder(folder.id);
      } catch (error) {
        console.error("Failed to delete folder:", error);
        alert("Failed to delete folder");
      }
    }
  };

  const FolderItem = ({
    folder,
    level = 0,
  }: {
    folder: FolderType;
    level?: number;
  }) => {
    const subfolders = getSubfolders(folder.id);
    const hasSubfolders = subfolders.length > 0;
    const isExpanded = expandedFolders.has(folder.id);
    const isCurrentFolder = assetStore.currentFolderId === folder.id;
    const assetsCount = assetStore.getAssetsByFolder(folder.id).length;

    return (
      <div>
        <ContextMenu>
          <ContextMenuTrigger>
            <div
              className={`
                flex items-center gap-2 px-2 py-1.5 rounded cursor-pointer transition-colors
                hover:bg-gray-700
                ${
                  isCurrentFolder
                    ? "bg-purple-600/20 text-purple-300"
                    : "text-gray-300"
                }
              `}
              style={{ paddingLeft: `${8 + level * 16}px` }}
              onClick={() => handleFolderClick(folder.id)}
            >
              {hasSubfolders && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFolderExpansion(folder.id);
                  }}
                  className="p-0.5 hover:bg-gray-600 rounded"
                >
                  <ChevronRight
                    className={`w-3 h-3 transition-transform ${
                      isExpanded ? "rotate-90" : ""
                    }`}
                  />
                </button>
              )}

              {!hasSubfolders && <div className="w-4" />}

              {isCurrentFolder ? (
                <FolderOpen className="w-4 h-4" />
              ) : (
                <Folder className="w-4 h-4" />
              )}

              <span className="text-sm flex-1 truncate">{folder.name}</span>

              {assetsCount > 0 && (
                <span className="text-xs text-gray-500 bg-gray-700 px-1.5 py-0.5 rounded">
                  {assetsCount}
                </span>
              )}
            </div>
          </ContextMenuTrigger>

          <ContextMenuContent className="bg-gray-800 border-gray-600">
            <ContextMenuItem
              onClick={() => handleFolderClick(folder.id)}
              className="text-gray-300 hover:bg-gray-700"
            >
              <FolderOpen className="w-4 h-4 mr-2" />
              Open
            </ContextMenuItem>
            <ContextMenuItem
              onClick={() => {
                /* TODO: Implement rename */
              }}
              className="text-gray-300 hover:bg-gray-700"
            >
              <Edit className="w-4 h-4 mr-2" />
              Rename
            </ContextMenuItem>
            <ContextMenuItem
              onClick={() => handleDeleteFolder(folder)}
              className="text-red-400 hover:bg-gray-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </ContextMenuItem>
          </ContextMenuContent>
        </ContextMenu>

        {/* Subfolders */}
        {hasSubfolders && isExpanded && (
          <div>
            {subfolders.map((subfolder) => (
              <FolderItem
                key={subfolder.id}
                folder={subfolder}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`bg-gray-800 rounded-lg p-3 ${className}`}>
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <div className="flex items-center gap-1 mb-3 pb-2 border-b border-gray-700">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleFolderClick(null)}
            className="text-gray-400 hover:text-white p-1 h-auto"
          >
            <Home className="w-4 h-4" />
          </Button>

          {breadcrumbs.map((folder) => (
            <div key={folder.id} className="flex items-center gap-1">
              <ChevronRight className="w-3 h-3 text-gray-500" />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFolderClick(folder.id)}
                className="text-gray-400 hover:text-white p-1 h-auto text-sm"
              >
                {folder.name}
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Folder Tree */}
      <div className="space-y-1">
        {/* Root/Home folder */}
        <div
          className={`
            flex items-center gap-2 px-2 py-1.5 rounded cursor-pointer transition-colors
            hover:bg-gray-700
            ${
              !assetStore.currentFolderId
                ? "bg-purple-600/20 text-purple-300"
                : "text-gray-300"
            }
          `}
          onClick={() => handleFolderClick(null)}
        >
          <Home className="w-4 h-4" />
          <span className="text-sm">All Assets</span>
          <span className="text-xs text-gray-500 bg-gray-700 px-1.5 py-0.5 rounded ml-auto">
            {assetStore.getAssetsByFolder(null).length}
          </span>
        </div>

        {/* Root folders */}
        {rootFolders.map((folder) => (
          <FolderItem key={folder.id} folder={folder} />
        ))}

        {/* Empty state */}
        {rootFolders.length === 0 && (
          <div className="text-center py-4">
            <Folder className="w-8 h-8 text-gray-500 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No folders created yet</p>
            <p className="text-xs text-gray-600 mt-1">
              Create folders to organize your assets
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
