"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useTimelineDragDrop } from "@/lib/hooks/use-timeline-drag-drop";
import { formatDuration, formatFileSize } from "@/lib/utils/format";
import type { Asset } from "@/types/video-editor";
import {
  Clock,
  Copy,
  Eye,
  FileAudio,
  FileImage,
  FileVideo,
  FolderOpen,
  HardDrive,
  Play,
  Sparkles,
  Trash2,
} from "lucide-react";
import { useEffect, useState, useMemo, useRef } from "react";

interface AssetLibraryProps {
  viewMode: "grid" | "list";
  className?: string;
}

export function AssetLibrary({ viewMode, className }: AssetLibraryProps) {
  const assetStore = useAssetStore();
  const timelineStore = useTimelineStore();
  const { startAssetDrag } = useTimelineDragDrop();

  const filteredAssets = useMemo(
    () => assetStore.getFilteredAssets(),
    [assetStore]
  );

  const hasLoadedRef = useRef(false);

  useEffect(() => {
    // Only load assets once when component mounts
    if (!hasLoadedRef.current) {
      hasLoadedRef.current = true;
      assetStore.loadAssets();
    }
  }, [assetStore]); // Empty dependency array to prevent infinite loop

  const handleAssetSelect = (asset: Asset, multiSelect = false) => {
    assetStore.selectAsset(asset.id, multiSelect);
  };

  const handleAssetDoubleClick = (asset: Asset) => {
    // Add asset to timeline when double-clicked
    addAssetToTimeline(asset);
  };

  const addAssetToTimeline = (asset: Asset) => {
    // Find or create appropriate track
    let targetTrack = timelineStore.tracks.find(
      (track) =>
        track.type === asset.type ||
        (asset.type === "ai-generated" && track.type === "video") ||
        (asset.type === "image" && track.type === "video")
    );

    if (!targetTrack) {
      // Create new track
      let trackType: "video" | "audio" | "effects";
      if (
        asset.type === "ai-generated" ||
        asset.type === "video" ||
        asset.type === "image"
      ) {
        trackType = "video";
      } else if (asset.type === "audio") {
        trackType = "audio";
      } else {
        trackType = "effects";
      }

      timelineStore.addTrack({
        type: trackType,
        name: `${trackType.charAt(0).toUpperCase() + trackType.slice(1)} Track`,
        clips: [],
        muted: false,
        locked: false,
        height: trackType === "audio" ? 80 : 100,
      });

      targetTrack = timelineStore.tracks.find(
        (track) => track.type === trackType
      );
    }

    if (targetTrack) {
      const newClip = {
        assetId: asset.id,
        startTime: timelineStore.duration,
        endTime: timelineStore.duration + (asset.duration || 5),
        trimStart: 0,
        trimEnd: asset.duration || 5,
        effects: [],
        properties: {
          volume: 1,
          opacity: 1,
          scale: 1,
          rotation: 0,
          filters: {
            brightness: 100,
            contrast: 100,
            saturation: 100,
          },
        },
      };

      timelineStore.addClip(targetTrack.id, newClip);
      timelineStore.setDuration(
        Math.max(timelineStore.duration, newClip.endTime)
      );
    }
  };

  const handleDeleteAsset = async (asset: Asset) => {
    if (confirm(`Are you sure you want to delete "${asset.name}"?`)) {
      try {
        await assetStore.deleteAsset(asset.id);
      } catch (error) {
        console.error("Failed to delete asset:", error);
        alert("Failed to delete asset");
      }
    }
  };

  const handleDuplicateAsset = async (asset: Asset) => {
    try {
      await assetStore.duplicateAsset(asset.id);
    } catch (error) {
      console.error("Failed to duplicate asset:", error);
      alert("Failed to duplicate asset");
    }
  };

  const handlePreviewAsset = (asset: Asset) => {
    // Preview functionality will be implemented later
    console.log("Preview asset:", asset.name);
  };

  const getAssetIcon = (asset: Asset) => {
    switch (asset.type) {
      case "video":
        return <FileVideo className="w-5 h-5 text-blue-400" />;
      case "audio":
        return <FileAudio className="w-5 h-5 text-green-400" />;
      case "image":
        return <FileImage className="w-5 h-5 text-purple-400" />;
      case "ai-generated":
        return <Sparkles className="w-5 h-5 text-yellow-400" />;
      default:
        return <FileVideo className="w-5 h-5 text-gray-400" />;
    }
  };

  const AssetThumbnail = ({ asset }: { asset: Asset }) => {
    const [imageError, setImageError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    if (asset.type === "audio") {
      return (
        <div className="w-full h-full bg-gradient-to-br from-green-500/20 to-green-600/20 flex items-center justify-center">
          <FileAudio className="w-8 h-8 text-green-400" />
        </div>
      );
    }

    if (asset.thumbnailUrl && !imageError) {
      return (
        <Image
          src={asset.thumbnailUrl}
          alt={asset.name}
          fill
          className="object-cover"
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setImageError(true);
            setIsLoading(false);
          }}
          style={{ display: isLoading ? "none" : "block" }}
        />
      );
    }

    // Fallback for assets without thumbnails
    return (
      <div className="w-full h-full bg-gradient-to-br from-gray-600/20 to-gray-700/20 flex items-center justify-center">
        {getAssetIcon(asset)}
      </div>
    );
  };

  if (assetStore.isLoading) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading assets...</p>
        </div>
      </div>
    );
  }

  if (filteredAssets.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <FolderOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h4 className="text-lg font-medium text-white mb-2">No assets found</h4>
        <p className="text-gray-400 text-sm">
          {assetStore.searchQuery || assetStore.filterType !== "all"
            ? "Try adjusting your search or filter criteria"
            : "Upload some files to get started"}
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      {viewMode === "grid" ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {filteredAssets.map((asset) => (
            <ContextMenu key={asset.id}>
              <ContextMenuTrigger>
                <div
                  className={`
                    bg-gray-800 rounded-lg overflow-hidden cursor-pointer transition-all duration-200
                    hover:bg-gray-700 hover:scale-105
                    ${
                      assetStore.selectedAssets.includes(asset.id)
                        ? "ring-2 ring-purple-500"
                        : ""
                    }
                  `}
                  onClick={(e) =>
                    handleAssetSelect(asset, e.ctrlKey || e.metaKey)
                  }
                  onDoubleClick={() => handleAssetDoubleClick(asset)}
                  onMouseDown={(e) => startAssetDrag(asset.id, e)}
                  draggable
                >
                  {/* Thumbnail */}
                  <div className="aspect-video bg-gray-900 relative">
                    <AssetThumbnail asset={asset} />

                    {/* Duration overlay for video/audio */}
                    {asset.duration && (
                      <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
                        {formatDuration(asset.duration)}
                      </div>
                    )}

                    {/* Type indicator */}
                    <div className="absolute top-1 left-1">
                      {getAssetIcon(asset)}
                    </div>
                  </div>

                  {/* Asset info */}
                  <div className="p-2">
                    <h4
                      className="text-sm font-medium text-white truncate"
                      title={asset.name}
                    >
                      {asset.name}
                    </h4>
                    <p className="text-xs text-gray-400 mt-1">
                      {formatFileSize(asset.metadata.fileSize)}
                    </p>
                  </div>
                </div>
              </ContextMenuTrigger>

              <ContextMenuContent className="bg-gray-800 border-gray-600">
                <ContextMenuItem
                  onClick={() => addAssetToTimeline(asset)}
                  className="text-gray-300 hover:bg-gray-700"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Add to Timeline
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => handlePreviewAsset(asset)}
                  className="text-gray-300 hover:bg-gray-700"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => handleDuplicateAsset(asset)}
                  className="text-gray-300 hover:bg-gray-700"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Duplicate
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => handleDeleteAsset(asset)}
                  className="text-red-400 hover:bg-gray-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </ContextMenuItem>
              </ContextMenuContent>
            </ContextMenu>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {filteredAssets.map((asset) => (
            <ContextMenu key={asset.id}>
              <ContextMenuTrigger>
                <div
                  className={`
                    bg-gray-800 rounded-lg p-3 cursor-pointer transition-all duration-200
                    hover:bg-gray-700 flex items-center gap-3
                    ${
                      assetStore.selectedAssets.includes(asset.id)
                        ? "ring-2 ring-purple-500"
                        : ""
                    }
                  `}
                  onClick={(e) =>
                    handleAssetSelect(asset, e.ctrlKey || e.metaKey)
                  }
                  onDoubleClick={() => handleAssetDoubleClick(asset)}
                  onMouseDown={(e) => startAssetDrag(asset.id, e)}
                  draggable
                >
                  {/* Thumbnail */}
                  <div className="w-16 h-12 bg-gray-900 rounded overflow-hidden flex-shrink-0">
                    <AssetThumbnail asset={asset} />
                  </div>

                  {/* Asset info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      {getAssetIcon(asset)}
                      <h4 className="text-sm font-medium text-white truncate">
                        {asset.name}
                      </h4>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-400">
                      <span className="flex items-center gap-1">
                        <HardDrive className="w-3 h-3" />
                        {formatFileSize(asset.metadata.fileSize)}
                      </span>
                      {asset.duration && (
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatDuration(asset.duration)}
                        </span>
                      )}
                      {asset.metadata.width && asset.metadata.height && (
                        <span>
                          {asset.metadata.width}×{asset.metadata.height}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        addAssetToTimeline(asset);
                      }}
                      className="text-gray-400 hover:text-white"
                    >
                      <Play className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </ContextMenuTrigger>

              <ContextMenuContent className="bg-gray-800 border-gray-600">
                <ContextMenuItem
                  onClick={() => addAssetToTimeline(asset)}
                  className="text-gray-300 hover:bg-gray-700"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Add to Timeline
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => handlePreviewAsset(asset)}
                  className="text-gray-300 hover:bg-gray-700"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => handleDuplicateAsset(asset)}
                  className="text-gray-300 hover:bg-gray-700"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Duplicate
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => handleDeleteAsset(asset)}
                  className="text-red-400 hover:bg-gray-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </ContextMenuItem>
              </ContextMenuContent>
            </ContextMenu>
          ))}
        </div>
      )}
    </div>
  );
}
