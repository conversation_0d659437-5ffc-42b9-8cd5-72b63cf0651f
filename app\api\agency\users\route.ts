import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { hash } from "bcrypt";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { auth } from "@/lib/auth";
import { desc, eq, and, count } from "drizzle-orm";

// Schema for creating a new basic user
const createBasicUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// Helper function to check if user is an agency user
// async function isAgencyUser() {
//   const session = await auth();
//   if (!session || (session.user.userType !== "agency-basic" && session.user.userType !== "agency-deluxe")) {
//     return false;
//   }
//   return true;
// }

// GET - Get all basic users created by this agency user
export async function GET() {
  try {
    const session = await auth();
    
    // Verify the requestor is an agency user
    if (!session || (session.user.userType !== "agency-basic" && session.user.userType !== "agency-deluxe")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Get all basic users created by this agency user, ordered by most recently created first
    const agencyUsers = await db.query.users.findMany({
      where: (users, { eq, and }) => and(
        eq(users.createdBy, session.user.id),
        eq(users.userType, "basic")
      ),
      orderBy: [desc(users.createdAt)],
    });

    // Return the users with sensitive information removed
    return NextResponse.json({
      users: agencyUsers.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        userType: user.userType,
        credits: user.credits,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 });
  }
}

// POST - Create a new basic user
export async function POST(request: Request) {
  try {
    const session = await auth();
    
    // Verify the requestor is an agency user
    if (!session || (session.user.userType !== "agency-basic" && session.user.userType !== "agency-deluxe")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // For agency-basic users, check if they have reached their limit of 50 users
    if (session.user.userType === "agency-basic") {
      const userCount = await db
        .select({ count: count() })
        .from(users)
        .where(
          and(
            eq(users.createdBy, session.user.id),
            eq(users.userType, "basic")
          )
        );
      
      if (userCount[0].count >= 50) {
        return NextResponse.json(
          { error: "You have reached the maximum limit of 50 basic users" },
          { status: 403 }
        );
      }
    }

    // Parse and validate the request body
    const body = await request.json();
    const result = createBasicUserSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.flatten() },
        { status: 400 }
      );
    }
    
    const { name, email, password } = result.data;
    
    // Check if email already exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.email, email)
    });
    
    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }
    
    // Hash the password
    const hashedPassword = await hash(password, 12);
    
    // Create the new basic user
    const userId = uuidv4();
    await db.insert(users).values({
      id: userId,
      name,
      email,
      password: hashedPassword,
      userType: "basic",
      credits: 5,
      lastCreditReset: new Date(),
      createdBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    // Return the newly created user (without password)
    const newUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, userId)
    });
    
    return NextResponse.json({
      user: {
        id: newUser?.id,
        name: newUser?.name,
        email: newUser?.email,
        userType: newUser?.userType,
        credits: newUser?.credits,
        createdAt: newUser?.createdAt,
        updatedAt: newUser?.updatedAt,
      }
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json({ error: "Failed to create user" }, { status: 500 });
  }
} 