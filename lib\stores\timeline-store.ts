import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { v4 as uuidv4 } from "uuid";
import type {
  Track,
  Clip,
  TimelineData,
  Marker,
  Effect,
} from "@/types/video-editor";

interface TimelineState {
  // Timeline data
  tracks: Track[];
  selectedClips: string[];
  playheadPosition: number;
  zoomLevel: number;
  duration: number;
  markers: Marker[];

  // UI state
  isPlaying: boolean;
  selectedTrackId: string | null;

  // Actions - Timeline management
  setTimeline: (timeline: TimelineData) => void;
  clearTimeline: () => void;
  setDuration: (duration: number) => void;

  // Actions - Track management
  addTrack: (track: Omit<Track, "id">) => void;
  removeTrack: (trackId: string) => void;
  updateTrack: (trackId: string, updates: Partial<Track>) => void;
  moveTrack: (trackId: string, newIndex: number) => void;
  duplicateTrack: (trackId: string) => void;

  // Actions - Clip management
  addClip: (trackId: string, clip: Omit<Clip, "id">) => void;
  removeClip: (clipId: string) => void;
  updateClip: (clipId: string, updates: Partial<Clip>) => void;
  moveClip: (clipId: string, trackId: string, position: number) => void;
  splitClip: (clipId: string, position: number) => void;
  trimClip: (clipId: string, start: number, end: number) => void;
  duplicateClip: (clipId: string) => void;

  // Actions - Selection management
  selectClip: (clipId: string, multiSelect?: boolean) => void;
  selectClips: (clipIds: string[]) => void;
  clearSelection: () => void;
  selectTrack: (trackId: string) => void;

  // Actions - Playback control
  setPlayheadPosition: (position: number) => void;
  setIsPlaying: (playing: boolean) => void;
  setZoomLevel: (level: number) => void;

  // Actions - Markers
  addMarker: (marker: Omit<Marker, "id">) => void;
  removeMarker: (markerId: string) => void;
  updateMarker: (markerId: string, updates: Partial<Marker>) => void;

  // Actions - Effects
  addEffectToClip: (clipId: string, effect: Omit<Effect, "id">) => void;
  removeEffectFromClip: (clipId: string, effectId: string) => void;
  updateClipEffect: (
    clipId: string,
    effectId: string,
    updates: Partial<Effect>
  ) => void;

  // Actions - Video merging
  mergeSelectedClips: () => Clip[];
  mergeTrackClips: (trackId: string) => Clip[];
  getClipsForMerging: (clipIds: string[]) => Clip[];
  validateClipsForMerging: (clipIds: string[]) => {
    isValid: boolean;
    errors: string[];
  };

  // Utility functions
  getClipById: (clipId: string) => Clip | undefined;
  getTrackById: (trackId: string) => Track | undefined;
  getClipsByTrack: (trackId: string) => Clip[];
  getSelectedClips: () => Clip[];
  getTotalDuration: () => number;
}

export const useTimelineStore = create<TimelineState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        tracks: [],
        selectedClips: [],
        playheadPosition: 0,
        zoomLevel: 1,
        duration: 0,
        markers: [],
        isPlaying: false,
        selectedTrackId: null,

        // Timeline management
        setTimeline: (timeline) =>
          set({
            tracks: timeline.tracks,
            duration: timeline.duration,
            markers: timeline.markers,
            selectedClips: [],
            playheadPosition: 0,
          }),

        clearTimeline: () =>
          set({
            tracks: [],
            selectedClips: [],
            playheadPosition: 0,
            duration: 0,
            markers: [],
            isPlaying: false,
            selectedTrackId: null,
          }),

        setDuration: (duration) => set({ duration }),

        // Track management
        addTrack: (trackData) =>
          set((state) => ({
            tracks: [...state.tracks, { ...trackData, id: uuidv4() }],
          })),

        removeTrack: (trackId) =>
          set((state) => ({
            tracks: state.tracks.filter((track) => track.id !== trackId),
            selectedClips: state.selectedClips.filter(
              (clipId) =>
                !state.tracks
                  .find((track) => track.id === trackId)
                  ?.clips.some((clip) => clip.id === clipId)
            ),
            selectedTrackId:
              state.selectedTrackId === trackId ? null : state.selectedTrackId,
          })),

        updateTrack: (trackId, updates) =>
          set((state) => ({
            tracks: state.tracks.map((track) =>
              track.id === trackId ? { ...track, ...updates } : track
            ),
          })),

        moveTrack: (trackId, newIndex) =>
          set((state) => {
            const tracks = [...state.tracks];
            const trackIndex = tracks.findIndex(
              (track) => track.id === trackId
            );
            if (trackIndex === -1) return state;

            const [track] = tracks.splice(trackIndex, 1);
            tracks.splice(newIndex, 0, track);
            return { tracks };
          }),

        duplicateTrack: (trackId) =>
          set((state) => {
            const track = state.tracks.find((t) => t.id === trackId);
            if (!track) return state;

            const newTrack: Track = {
              ...track,
              id: uuidv4(),
              name: `${track.name} Copy`,
              clips: track.clips.map((clip) => ({
                ...clip,
                id: uuidv4(),
              })),
            };

            return { tracks: [...state.tracks, newTrack] };
          }),

        // Clip management
        addClip: (trackId, clipData) =>
          set((state) => ({
            tracks: state.tracks.map((track) =>
              track.id === trackId
                ? {
                    ...track,
                    clips: [...track.clips, { ...clipData, id: uuidv4() }],
                  }
                : track
            ),
          })),

        removeClip: (clipId) =>
          set((state) => ({
            tracks: state.tracks.map((track) => ({
              ...track,
              clips: track.clips.filter((clip) => clip.id !== clipId),
            })),
            selectedClips: state.selectedClips.filter((id) => id !== clipId),
          })),

        updateClip: (clipId, updates) =>
          set((state) => ({
            tracks: state.tracks.map((track) => ({
              ...track,
              clips: track.clips.map((clip) =>
                clip.id === clipId ? { ...clip, ...updates } : clip
              ),
            })),
          })),

        moveClip: (clipId, trackId, position) =>
          set((state) => {
            // Find and remove clip from current track
            let clipToMove: Clip | undefined;
            const tracksWithoutClip = state.tracks.map((track) => ({
              ...track,
              clips: track.clips.filter((clip) => {
                if (clip.id === clipId) {
                  clipToMove = clip;
                  return false;
                }
                return true;
              }),
            }));

            if (!clipToMove) return state;

            // Add clip to new track at position
            const updatedClip = { ...clipToMove, startTime: position };
            return {
              tracks: tracksWithoutClip.map((track) =>
                track.id === trackId
                  ? { ...track, clips: [...track.clips, updatedClip] }
                  : track
              ),
            };
          }),

        splitClip: (clipId, position) =>
          set((state) => {
            const tracks = state.tracks.map((track) => ({
              ...track,
              clips: track.clips.flatMap((clip) => {
                if (clip.id !== clipId) return [clip];

                if (position <= clip.startTime || position >= clip.endTime) {
                  return [clip]; // Can't split outside clip bounds
                }

                const firstClip: Clip = {
                  ...clip,
                  id: uuidv4(),
                  endTime: position,
                  trimEnd: clip.trimStart + (position - clip.startTime),
                };

                const secondClip: Clip = {
                  ...clip,
                  id: uuidv4(),
                  startTime: position,
                  trimStart: clip.trimStart + (position - clip.startTime),
                };

                return [firstClip, secondClip];
              }),
            }));

            return { tracks };
          }),

        trimClip: (clipId, start, end) =>
          set((state) => ({
            tracks: state.tracks.map((track) => ({
              ...track,
              clips: track.clips.map((clip) =>
                clip.id === clipId
                  ? { ...clip, trimStart: start, trimEnd: end }
                  : clip
              ),
            })),
          })),

        duplicateClip: (clipId) =>
          set((state) => {
            const tracks = state.tracks.map((track) => {
              const clipIndex = track.clips.findIndex(
                (clip) => clip.id === clipId
              );
              if (clipIndex === -1) return track;

              const originalClip = track.clips[clipIndex];
              const newClip: Clip = {
                ...originalClip,
                id: uuidv4(),
                startTime: originalClip.endTime,
                endTime:
                  originalClip.endTime +
                  (originalClip.endTime - originalClip.startTime),
              };

              return {
                ...track,
                clips: [...track.clips, newClip],
              };
            });

            return { tracks };
          }),

        // Selection management
        selectClip: (clipId, multiSelect = false) =>
          set((state) => ({
            selectedClips: multiSelect
              ? state.selectedClips.includes(clipId)
                ? state.selectedClips.filter((id) => id !== clipId)
                : [...state.selectedClips, clipId]
              : [clipId],
          })),

        selectClips: (clipIds) => set({ selectedClips: clipIds }),

        clearSelection: () => set({ selectedClips: [] }),

        selectTrack: (trackId) => set({ selectedTrackId: trackId }),

        // Playback control
        setPlayheadPosition: (position) => set({ playheadPosition: position }),

        setIsPlaying: (playing) => set({ isPlaying: playing }),

        setZoomLevel: (level) =>
          set({ zoomLevel: Math.max(0.1, Math.min(10, level)) }),

        // Markers
        addMarker: (markerData) =>
          set((state) => ({
            markers: [...state.markers, { ...markerData, id: uuidv4() }],
          })),

        removeMarker: (markerId) =>
          set((state) => ({
            markers: state.markers.filter((marker) => marker.id !== markerId),
          })),

        updateMarker: (markerId, updates) =>
          set((state) => ({
            markers: state.markers.map((marker) =>
              marker.id === markerId ? { ...marker, ...updates } : marker
            ),
          })),

        // Effects
        addEffectToClip: (clipId, effectData) =>
          set((state) => ({
            tracks: state.tracks.map((track) => ({
              ...track,
              clips: track.clips.map((clip) =>
                clip.id === clipId
                  ? {
                      ...clip,
                      effects: [
                        ...clip.effects,
                        { ...effectData, id: uuidv4() },
                      ],
                    }
                  : clip
              ),
            })),
          })),

        removeEffectFromClip: (clipId, effectId) =>
          set((state) => ({
            tracks: state.tracks.map((track) => ({
              ...track,
              clips: track.clips.map((clip) =>
                clip.id === clipId
                  ? {
                      ...clip,
                      effects: clip.effects.filter(
                        (effect) => effect.id !== effectId
                      ),
                    }
                  : clip
              ),
            })),
          })),

        updateClipEffect: (clipId, effectId, updates) =>
          set((state) => ({
            tracks: state.tracks.map((track) => ({
              ...track,
              clips: track.clips.map((clip) =>
                clip.id === clipId
                  ? {
                      ...clip,
                      effects: clip.effects.map((effect) =>
                        effect.id === effectId
                          ? { ...effect, ...updates }
                          : effect
                      ),
                    }
                  : clip
              ),
            })),
          })),

        // Utility functions
        getClipById: (clipId) => {
          const state = get();
          for (const track of state.tracks) {
            const clip = track.clips.find((c) => c.id === clipId);
            if (clip) return clip;
          }
          return undefined;
        },

        getTrackById: (trackId) => {
          const state = get();
          return state.tracks.find((track) => track.id === trackId);
        },

        getClipsByTrack: (trackId) => {
          const state = get();
          const track = state.tracks.find((t) => t.id === trackId);
          return track ? track.clips : [];
        },

        getSelectedClips: () => {
          const state = get();
          const clips: Clip[] = [];
          for (const track of state.tracks) {
            for (const clip of track.clips) {
              if (state.selectedClips.includes(clip.id)) {
                clips.push(clip);
              }
            }
          }
          return clips;
        },

        getTotalDuration: () => {
          const state = get();
          let maxDuration = 0;
          for (const track of state.tracks) {
            for (const clip of track.clips) {
              maxDuration = Math.max(maxDuration, clip.endTime);
            }
          }
          return Math.max(maxDuration, state.duration);
        },

        // Video merging functions
        mergeSelectedClips: () => {
          return get()
            .getSelectedClips()
            .sort((a, b) => a.startTime - b.startTime);
        },

        mergeTrackClips: (trackId) => {
          const state = get();
          const track = state.tracks.find((t) => t.id === trackId);
          return track
            ? track.clips.sort((a, b) => a.startTime - b.startTime)
            : [];
        },

        getClipsForMerging: (clipIds) => {
          const state = get();
          const clips: Clip[] = [];
          for (const track of state.tracks) {
            for (const clip of track.clips) {
              if (clipIds.includes(clip.id)) {
                clips.push(clip);
              }
            }
          }
          return clips.sort((a, b) => a.startTime - b.startTime);
        },

        validateClipsForMerging: (clipIds) => {
          const errors: string[] = [];

          if (clipIds.length < 2) {
            errors.push("At least 2 clips are required for merging");
          }

          const clips = get().getClipsForMerging(clipIds);

          // Check for overlapping clips
          for (let i = 0; i < clips.length - 1; i++) {
            const currentClip = clips[i];
            const nextClip = clips[i + 1];

            if (currentClip.endTime > nextClip.startTime) {
              errors.push(
                `Clips "${currentClip.id}" and "${nextClip.id}" overlap and cannot be merged`
              );
            }
          }

          // Check for clips with zero duration
          const zeroDurationClips = clips.filter(
            (clip) => clip.startTime >= clip.endTime
          );
          if (zeroDurationClips.length > 0) {
            errors.push(
              `${zeroDurationClips.length} clips have zero or negative duration`
            );
          }

          // Check for clips with invalid trim settings
          const invalidTrimClips = clips.filter(
            (clip) => clip.trimStart >= clip.trimEnd
          );
          if (invalidTrimClips.length > 0) {
            errors.push(
              `${invalidTrimClips.length} clips have invalid trim settings`
            );
          }

          return {
            isValid: errors.length === 0,
            errors,
          };
        },
      }),
      {
        name: "timeline-store",
        partialize: (state) => ({
          tracks: state.tracks,
          duration: state.duration,
          markers: state.markers,
          zoomLevel: state.zoomLevel,
        }),
      }
    )
  )
);
