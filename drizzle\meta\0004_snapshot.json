{"id": "78e4d1af-fa14-43a6-928c-7ff73b09b1b4", "prevId": "2c8a23d5-e1cb-485c-b180-d662ea178cfb", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_provider_provider_account_id_pk": {"name": "accounts_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"session_token": {"name": "session_token", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "user_type": {"name": "user_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'basic'"}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "last_credit_reset": {"name": "last_credit_reset", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reset_token": {"name": "reset_token", "type": "text", "primaryKey": false, "notNull": false}, "reset_token_expiry": {"name": "reset_token_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_created_by_users_id_fk": {"name": "users_created_by_users_id_fk", "tableFrom": "users", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"name": "verification_tokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.video_assets": {"name": "video_assets", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "real", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "folder_id": {"name": "folder_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"video_assets_user_id_users_id_fk": {"name": "video_assets_user_id_users_id_fk", "tableFrom": "video_assets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "video_assets_project_id_video_projects_id_fk": {"name": "video_assets_project_id_video_projects_id_fk", "tableFrom": "video_assets", "tableTo": "video_projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "video_assets_folder_id_video_folders_id_fk": {"name": "video_assets_folder_id_video_folders_id_fk", "tableFrom": "video_assets", "tableTo": "video_folders", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.video_exports": {"name": "video_exports", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "progress": {"name": "progress", "type": "real", "primaryKey": false, "notNull": false, "default": 0}, "output_url": {"name": "output_url", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"video_exports_project_id_video_projects_id_fk": {"name": "video_exports_project_id_video_projects_id_fk", "tableFrom": "video_exports", "tableTo": "video_projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "video_exports_user_id_users_id_fk": {"name": "video_exports_user_id_users_id_fk", "tableFrom": "video_exports", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.video_folders": {"name": "video_folders", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"video_folders_parent_id_video_folders_id_fk": {"name": "video_folders_parent_id_video_folders_id_fk", "tableFrom": "video_folders", "tableTo": "video_folders", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "video_folders_user_id_users_id_fk": {"name": "video_folders_user_id_users_id_fk", "tableFrom": "video_folders", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "video_folders_project_id_video_projects_id_fk": {"name": "video_folders_project_id_video_projects_id_fk", "tableFrom": "video_folders", "tableTo": "video_projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.video_generations": {"name": "video_generations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "negative_prompt": {"name": "negative_prompt", "type": "text", "primaryKey": false, "notNull": false}, "resolution": {"name": "resolution", "type": "text", "primaryKey": false, "notNull": false, "default": "'720p'"}, "aspect_ratio": {"name": "aspect_ratio", "type": "text", "primaryKey": false, "notNull": false, "default": "'16:9'"}, "seed": {"name": "seed", "type": "integer", "primaryKey": false, "notNull": false}, "video_url": {"name": "video_url", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "request_id": {"name": "request_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"video_generations_user_id_users_id_fk": {"name": "video_generations_user_id_users_id_fk", "tableFrom": "video_generations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.video_project_versions": {"name": "video_project_versions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "version_number": {"name": "version_number", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "timeline": {"name": "timeline", "type": "jsonb", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"video_project_versions_project_id_video_projects_id_fk": {"name": "video_project_versions_project_id_video_projects_id_fk", "tableFrom": "video_project_versions", "tableTo": "video_projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.video_projects": {"name": "video_projects", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "timeline": {"name": "timeline", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"video_projects_user_id_users_id_fk": {"name": "video_projects_user_id_users_id_fk", "tableFrom": "video_projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}