import { db } from "./db";
import { users } from "./schema";
import { eq } from "drizzle-orm";
import crypto from "crypto";
import type { User } from "./schema";

// Token expiration time (1 hour)
const TOKEN_EXPIRATION = 60 * 60 * 1000; // 1 hour in milliseconds

// Generate a secure random token
export function generateResetToken(): string {
  return crypto.randomBytes(32).toString("hex");
}

// Store the reset token in the user's record
export async function storeResetToken(email: string): Promise<{ token: string; user: User } | null> {
  try {
    // Find the user by email
    const user = await db.query.users.findFirst({
      where: (users) => eq(users.email, email)
    });

    if (!user) {
      return null;
    }

    // Generate a new reset token
    const resetToken = generateResetToken();
    const resetTokenExpiry = new Date(Date.now() + TOKEN_EXPIRATION);

    // Store the token and expiry in the user record
    await db.update(users)
      .set({
        resetToken,
        resetTokenExpiry,
        updatedAt: new Date(),
      })
      .where(eq(users.id, user.id));

    return { token: resetToken, user };
  } catch (error) {
    console.error("Error storing reset token:", error);
    return null;
  }
}

// Verify a reset token
export async function verifyResetToken(token: string): Promise<User | null> {
  try {
    // Find the user with this token
    const user = await db.query.users.findFirst({
      where: (users) => eq(users.resetToken, token)
    });

    if (!user || !user.resetTokenExpiry) {
      return null;
    }

    // Check if token has expired
    const now = new Date();
    if (now > user.resetTokenExpiry) {
      return null;
    }

    return user;
  } catch (error) {
    console.error("Error verifying reset token:", error);
    return null;
  }
}

// Clear the reset token after it's been used
export async function clearResetToken(userId: string): Promise<boolean> {
  try {
    await db.update(users)
      .set({
        resetToken: null,
        resetTokenExpiry: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));
    
    return true;
  } catch (error) {
    console.error("Error clearing reset token:", error);
    return false;
  }
} 