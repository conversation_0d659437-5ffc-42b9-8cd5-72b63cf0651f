"use client";

import React, {
  useRef,
  useEffect,
  useMemo,
  useState,
  useCallback,
} from "react";
import Image from "next/image";
import { Player, PlayerRef } from "@remotion/player";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Settings,
  Maximize,
  RotateCcw,
} from "lucide-react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useTimelineSync } from "@/lib/hooks/use-timeline-sync";
import { ColorGrading, Transform } from "./remotion-effects";
import type { Clip, Track, Asset } from "@/types/video-editor";

interface RemotionPreviewProps {
  className?: string;
  width?: number;
  height?: number;
  fps?: number;
}

// Dynamic composition component that renders based on timeline state
const TimelineComposition: React.FC<{
  tracks: Track[];
  playheadPosition: number;
  assets: Asset[];
  width: number;
  height: number;
  volume?: number;
}> = ({ tracks, playheadPosition, assets, width, height, volume = 1 }) => {
  // Get assets lookup for quick access
  const assetsMap = useMemo(() => {
    return assets.reduce((map, asset) => {
      map[asset.id] = asset;
      return map;
    }, {} as Record<string, Asset>);
  }, [assets]);

  // Get active clips at current playhead position
  const activeClips = useMemo(() => {
    const clips: Array<{ clip: Clip; asset: Asset; track: Track }> = [];

    tracks.forEach((track) => {
      track.clips.forEach((clip: Clip) => {
        if (
          playheadPosition >= clip.startTime &&
          playheadPosition < clip.endTime
        ) {
          const asset = assetsMap[clip.assetId];
          if (asset) {
            clips.push({ clip, asset, track });
          }
        }
      });
    });

    return clips;
  }, [tracks, playheadPosition, assetsMap]);

  return (
    <div
      style={{
        width,
        height,
        backgroundColor: "#000",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {activeClips.map(({ clip, asset, track }, index) => {
        // Calculate clip-relative time
        const clipTime = playheadPosition - clip.startTime;

        if (asset.type === "video") {
          return (
            <div
              key={`${clip.id}-${index}`}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                zIndex: track.type === "video" ? 1 : 0,
              }}
            >
              <Transform
                scale={clip.properties.scale}
                rotation={clip.properties.rotation}
                position={clip.properties.position}
                opacity={clip.properties.opacity}
              >
                <ColorGrading
                  brightness={clip.properties.filters?.brightness}
                  contrast={clip.properties.filters?.contrast}
                  saturation={clip.properties.filters?.saturation}
                  hue={clip.properties.filters?.hue}
                >
                  <video
                    src={asset.url}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "contain",
                    }}
                    muted={track.muted || volume === 0}
                    ref={(videoEl) => {
                      if (videoEl) {
                        videoEl.currentTime = clipTime + (clip.trimStart || 0);
                        videoEl.volume = Math.min(
                          1,
                          Math.max(0, (clip.properties.volume || 1) * volume)
                        );
                      }
                    }}
                  />
                </ColorGrading>
              </Transform>
            </div>
          );
        } else if (asset.type === "image") {
          return (
            <div
              key={`${clip.id}-${index}`}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                zIndex: track.type === "video" ? 1 : 0,
              }}
            >
              <Transform
                scale={clip.properties.scale}
                rotation={clip.properties.rotation}
                position={clip.properties.position}
                opacity={clip.properties.opacity}
              >
                <ColorGrading
                  brightness={clip.properties.filters?.brightness}
                  contrast={clip.properties.filters?.contrast}
                  saturation={clip.properties.filters?.saturation}
                  hue={clip.properties.filters?.hue}
                >
                  <Image
                    src={asset.url}
                    alt={asset.name}
                    fill
                    style={{
                      objectFit: "contain",
                    }}
                  />
                </ColorGrading>
              </Transform>
            </div>
          );
        }

        return null;
      })}
    </div>
  );
};

export function RemotionPreview({
  className = "",
  width = 1920,
  height = 1080,
  fps = 30,
}: RemotionPreviewProps) {
  const timelineStore = useTimelineStore();
  const assetStore = useAssetStore();
  const playerRef = useRef<PlayerRef>(null);

  // Local state for preview controls
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [previewQuality, setPreviewQuality] = useState<
    "low" | "medium" | "high"
  >("medium");
  const [showSettings, setShowSettings] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Sync timeline with player
  const { isPlaying, playheadPosition, seekTo, toggle } = useTimelineSync({
    playerRef,
    onTimeUpdate: (time) => {
      // Update timeline position when player time changes
      if (Math.abs(time - timelineStore.playheadPosition) > 0.1) {
        timelineStore.setPlayheadPosition(time);
      }
    },
  });

  // Calculate composition duration in frames
  const durationInFrames = useMemo(() => {
    return Math.max(1, Math.ceil(timelineStore.duration * fps));
  }, [timelineStore.duration, fps]);

  // Current frame based on playhead position
  const currentFrame = useMemo(() => {
    return Math.floor(playheadPosition * fps);
  }, [playheadPosition, fps]);

  // Calculate preview dimensions based on quality
  const previewDimensions = useMemo(() => {
    const qualityMultipliers = {
      low: 0.5,
      medium: 0.75,
      high: 1.0,
    };
    const multiplier = qualityMultipliers[previewQuality];
    return {
      width: Math.floor(width * multiplier),
      height: Math.floor(height * multiplier),
    };
  }, [width, height, previewQuality]);

  // Sync player when playhead changes externally (from timeline)
  useEffect(() => {
    if (playerRef.current && !isPlaying) {
      playerRef.current.seekTo(currentFrame);
    }
  }, [currentFrame, isPlaying]);

  // Control player playback state
  useEffect(() => {
    if (playerRef.current) {
      if (isPlaying) {
        playerRef.current.play();
      } else {
        playerRef.current.pause();
      }
    }
  }, [isPlaying]);

  // Playback control handlers
  const handlePlayPause = useCallback(() => {
    toggle();
  }, [toggle]);

  const handleFrameStep = useCallback(
    (direction: "forward" | "backward") => {
      const frameTime = 1 / fps;
      const newTime =
        direction === "forward"
          ? Math.min(playheadPosition + frameTime, timelineStore.duration)
          : Math.max(playheadPosition - frameTime, 0);
      seekTo(newTime);
    },
    [fps, playheadPosition, timelineStore.duration, seekTo]
  );

  const handleScrub = useCallback(
    (values: number[]) => {
      const newTime = values[0];
      seekTo(newTime);
    },
    [seekTo]
  );

  const handleVolumeChange = useCallback((values: number[]) => {
    const newVolume = values[0] / 100;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  }, []);

  const handleMuteToggle = useCallback(() => {
    setIsMuted(!isMuted);
  }, [isMuted]);

  const handleQualityChange = useCallback(
    (quality: "low" | "medium" | "high") => {
      setPreviewQuality(quality);
    },
    []
  );

  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleReset = useCallback(() => {
    seekTo(0);
  }, [seekTo]);

  // Format time display
  const formatTime = useCallback(
    (timeInSeconds: number) => {
      const minutes = Math.floor(timeInSeconds / 60);
      const seconds = Math.floor(timeInSeconds % 60);
      const frames = Math.floor((timeInSeconds % 1) * fps);
      return `${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}:${frames.toString().padStart(2, "0")}`;
    },
    [fps]
  );

  return (
    <div
      className={`bg-gray-900 rounded-lg overflow-hidden ${className} ${
        isFullscreen ? "fixed inset-0 z-50" : ""
      }`}
    >
      {/* Video Preview Area */}
      <div
        className={`${
          isFullscreen ? "h-full flex flex-col" : "aspect-video"
        } relative`}
      >
        <div className={`${isFullscreen ? "flex-1" : "h-full"} relative`}>
          <Player
            ref={playerRef}
            component={TimelineComposition}
            inputProps={{
              tracks: timelineStore.tracks,
              playheadPosition: playheadPosition,
              assets: assetStore.assets,
              width: previewDimensions.width,
              height: previewDimensions.height,
              volume: isMuted ? 0 : volume,
            }}
            durationInFrames={durationInFrames}
            compositionWidth={previewDimensions.width}
            compositionHeight={previewDimensions.height}
            fps={fps}
            controls={false}
            style={{
              width: "100%",
              height: "100%",
            }}
            clickToPlay={false}
            doubleClickToFullscreen={false}
          />

          {/* Overlay Controls */}
          <div className="absolute top-4 right-4 flex gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => setShowSettings(!showSettings)}
              className="bg-black/50 hover:bg-black/70"
            >
              <Settings className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={handleFullscreenToggle}
              className="bg-black/50 hover:bg-black/70"
            >
              <Maximize className="w-4 h-4" />
            </Button>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <div className="absolute top-16 right-4 bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-600 min-w-48">
              <h4 className="text-white font-medium mb-3">Preview Settings</h4>

              <div className="space-y-3">
                <div>
                  <label className="text-sm text-gray-300 block mb-1">
                    Quality
                  </label>
                  <Select
                    value={previewQuality}
                    onValueChange={handleQualityChange}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low (50%)</SelectItem>
                      <SelectItem value="medium">Medium (75%)</SelectItem>
                      <SelectItem value="high">High (100%)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-xs text-gray-400">
                  Resolution: {previewDimensions.width} ×{" "}
                  {previewDimensions.height}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Preview Controls */}
        <div className="bg-gray-800 border-t border-gray-700 p-4">
          {/* Timeline Scrubber */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xs text-gray-400 w-16">
                {formatTime(playheadPosition)}
              </span>
              <div className="flex-1">
                <Slider
                  value={[playheadPosition]}
                  min={0}
                  max={timelineStore.duration || 1}
                  step={1 / fps}
                  onValueChange={handleScrub}
                  className="w-full"
                />
              </div>
              <span className="text-xs text-gray-400 w-16 text-right">
                {formatTime(timelineStore.duration)}
              </span>
            </div>
          </div>

          {/* Playback Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Reset Button */}
              <Button
                size="sm"
                variant="ghost"
                onClick={handleReset}
                className="text-gray-400 hover:text-white"
              >
                <RotateCcw className="w-4 h-4" />
              </Button>

              {/* Frame Step Backward */}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleFrameStep("backward")}
                className="text-gray-400 hover:text-white"
              >
                <SkipBack className="w-4 h-4" />
              </Button>

              {/* Play/Pause */}
              <Button
                size="sm"
                onClick={handlePlayPause}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>

              {/* Frame Step Forward */}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleFrameStep("forward")}
                className="text-gray-400 hover:text-white"
              >
                <SkipForward className="w-4 h-4" />
              </Button>
            </div>

            {/* Volume Controls */}
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="ghost"
                onClick={handleMuteToggle}
                className="text-gray-400 hover:text-white"
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
              <div className="w-20">
                <Slider
                  value={[isMuted ? 0 : volume * 100]}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={handleVolumeChange}
                  className="w-full"
                />
              </div>
            </div>

            {/* Info Display */}
            <div className="text-xs text-gray-400 text-right">
              <div>
                Frame: {currentFrame} / {durationInFrames}
              </div>
              <div>
                {previewDimensions.width} × {previewDimensions.height} @ {fps}
                fps
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
