"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

export function DashboardClient() {
  const { data: session, status } = useSession();
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  if (status === "loading") {
    return <div className="p-4 text-center">Loading session...</div>;
  }

  if (status === "unauthenticated") {
    return <div className="p-4 text-center">Not authenticated. Please log in.</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold">Client Component</h2>
      <pre className="mt-4 rounded bg-gray-100 p-4 dark:bg-gray-800">
        {JSON.stringify({ session, status }, null, 2)}
      </pre>
    </div>
  );
} 