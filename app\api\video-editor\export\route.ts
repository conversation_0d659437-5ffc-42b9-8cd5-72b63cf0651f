import type { ExportSettings } from "@/types/video-editor";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ProjectService } from "@/lib/services/project-service";
import { ExportService } from "@/lib/services/export-service";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const {
      projectId,
      settings,
    }: { projectId: string; settings: ExportSettings } = body;

    // Get the project
    const project = await ProjectService.getProject(projectId, session.user.id);
    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Start the export
    const exportId = await ExportService.startExport(
      project,
      settings,
      session.user.id
    );

    return NextResponse.json({ exportId });
  } catch (error) {
    console.error("Error starting export:", error);
    return NextResponse.json(
      { error: "Failed to start export" },
      { status: 500 }
    );
  }
}
