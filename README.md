# Next.js Authentication with NextAuth.js and Dr<PERSON>zle ORM

This project demonstrates how to implement authentication in a Next.js application using NextAuth.js with Drizzle ORM and PostgreSQL database.

## Features

- User authentication with email and password
- User registration
- Protected routes with middleware
- Dashboard for authenticated users
- Text-to-video generation using fal.ai API
- Text-to-image generation using Together AI
- Image-to-video generation using fal.ai API
- Google Veo API for advanced text-to-video generation
- User access control for agency users
- PostgreSQL database with Drizzle ORM
- Modern UI with Tailwind CSS

## Prerequisites

- Node.js 18+ and npm
- PostgreSQL database

## Getting Started

1. Clone the repository

```bash
git clone <repository-url>
cd visual-app
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

Create a `.env.local` file in the root directory with the following variables:

```
DATABASE_URL="postgresql://username:password@localhost:5432/nextauth_db"
AUTH_SECRET="your-secret-key"
FAL_KEY="your-fal-ai-api-key"
TOGETHER_API_KEY="your-together-ai-api-key"
GEMINI_API_KEY="your-google-gemini-api-key"
```

Replace `username`, `password`, and the database name with your PostgreSQL credentials. 
- Get your FAL_KEY from [fal.ai](https://fal.ai/) by signing up for an account.
- Get your TOGETHER_API_KEY from [Together AI](https://together.ai/) by signing up for an account.
- Get your GEMINI_API_KEY from [Google AI Studio](https://aistudio.google.com/) by creating a project and generating an API key.

4. Set up the database

```bash
npm run db:generate
npm run db:migrate
```

5. Run the development server

```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

- `app/` - Next.js App Router pages and API routes
- `components/` - React components
- `lib/` - Utility functions and configurations
  - `db.ts` - Drizzle ORM configuration
  - `schema.ts` - Database schema definition
  - `migrate.ts` - Database migration script
- `drizzle/` - Generated SQL migrations
- `auth.ts` - NextAuth.js configuration
- `middleware.ts` - Next.js middleware for protected routes
- `drizzle.config.ts` - Drizzle configuration

## Authentication Flow

1. User registers via `/register` page
2. User logs in via `/login` page
3. NextAuth.js creates a session and JWT
4. Protected routes check for valid session
5. User can access the dashboard when authenticated
6. User can sign out to end the session

## AI Generation Features

### Text-to-Video
Uses fal.ai API to generate videos from text prompts.

### Text-to-Image
Uses Together AI's FLUX.1-schnell-Free model to generate high-quality images from text descriptions.

### Image-to-Video
Uses fal.ai API to animate static images into videos.

### Google Veo Text-to-Video
Uses Google's Veo API to generate high-quality videos from text prompts. This feature requires a valid Google Gemini API key with access to the Veo model.

#### Setting up Google Veo API
1. Visit [Google AI Studio](https://aistudio.google.com/) and create an account
2. Create a new API key
3. Add the API key to your `.env.local` file as `GEMINI_API_KEY`
4. Ensure you have access to the Veo model (you may need to request access)

#### Troubleshooting Google Veo API
- If you see an "API Key not found" error, make sure your API key is correctly set in the `.env.local` file
- If your API key is valid but you're still seeing errors, verify that your Google account has access to the Veo model
- For production use, consider implementing rate limiting and error handling

## Technologies Used

- [Next.js](https://nextjs.org/)
- [NextAuth.js](https://next-auth.js.org/)
- [Drizzle ORM](https://orm.drizzle.team/)
- [PostgreSQL](https://www.postgresql.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [TypeScript](https://www.typescriptlang.org/)
- [React Hook Form](https://react-hook-form.com/)
- [Zod](https://zod.dev/)
- [fal.ai](https://fal.ai/) - AI text-to-video generation
- [Together AI](https://together.ai/) - AI text-to-image generation
- [Google Gemini](https://aistudio.google.com/) - Veo text-to-video generation

## License

MIT

# Video Editing Application

This is a video editing application built with Next.js that provides a user interface for editing videos. Currently, the application provides a UI for video editing but uses sample videos for the output.

## Implementing Real Video Processing with FFmpeg

For a production-ready application, you should implement server-side video processing using FFmpeg. Here's how you can do it:

### 1. Install FFmpeg on your server

```bash
# For Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# For macOS
brew install ffmpeg

# For Windows
# Download from https://ffmpeg.org/download.html
```

### 2. Install Node.js FFmpeg wrapper

```bash
npm install fluent-ffmpeg
```

### 3. Implement video processing in your API routes

Here's an example of how to implement video trimming with FFmpeg:

```typescript
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";
import path from "path";
import os from "os";
import ffmpeg from "fluent-ffmpeg";

// Temporary directory for processing videos
const TMP_DIR = path.join(os.tmpdir(), 'veo-video-processing');

// Ensure temp directory exists
if (!fs.existsSync(TMP_DIR)) {
  fs.mkdirSync(TMP_DIR, { recursive: true });
}

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const formData = await req.formData();
    const videoFile = formData.get('video') as File;
    const trimStart = parseFloat(formData.get('trimStart') as string);
    const trimEnd = parseFloat(formData.get('trimEnd') as string);
    
    if (!videoFile) {
      return NextResponse.json({ error: "No video file provided" }, { status: 400 });
    }
    
    // Generate a unique ID for this render
    const renderId = uuidv4();
    
    // Save the uploaded file to the temp directory
    const videoBuffer = await videoFile.arrayBuffer();
    const inputPath = path.join(TMP_DIR, `input-${renderId}.mp4`);
    const outputPath = path.join(TMP_DIR, `output-${renderId}.mp4`);
    
    fs.writeFileSync(inputPath, Buffer.from(videoBuffer));
    
    // Process the video with FFmpeg
    await new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .setStartTime(trimStart)
        .setDuration(trimEnd - trimStart)
        .output(outputPath)
        .on('end', resolve)
        .on('error', reject)
        .run();
    });
    
    // Return the processed video
    const processedVideo = fs.readFileSync(outputPath);
    
    // Clean up temp files
    fs.unlinkSync(inputPath);
    fs.unlinkSync(outputPath);
    
    return new NextResponse(processedVideo, {
      status: 200,
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Disposition': `attachment; filename="edited-video-${renderId}.mp4"`
      }
    });
    
  } catch (error) {
    console.error("Error processing video:", error);
    return NextResponse.json({ error: "Failed to process video" }, { status: 500 });
  }
}
```

### 4. Implement other video editing features

FFmpeg supports a wide range of video editing operations:

- **Trimming**: Cut sections of a video
- **Combining**: Join multiple video clips together
- **Filters**: Apply visual filters (brightness, contrast, saturation)
- **Audio**: Adjust volume, add background music
- **Text**: Add text overlays
- **Transitions**: Add transitions between clips

### 5. Handle file uploads properly

For production use, you should:

1. Use a storage service like AWS S3 or Google Cloud Storage for video files
2. Implement proper authentication and authorization
3. Set up file size limits and validation
4. Consider using a queue system for processing long videos

## Libraries for Video Editing

Here are some libraries you can use for video editing in a Next.js application:

1. **FFmpeg** (via fluent-ffmpeg): The most powerful option, supports almost any video editing operation
2. **Remotion**: React framework for programmatic video creation
3. **MoviePy**: Python library for video editing (requires a Python backend)
4. **ShortcutJS**: JavaScript video editing library

## Cloud Services for Video Processing

If you don't want to implement your own video processing:

1. **AWS Elemental MediaConvert**: Comprehensive video processing service
2. **Google Cloud Video Intelligence API**: AI-powered video analysis and editing
3. **Cloudinary**: Cloud-based image and video management
4. **Mux**: API-first platform for video

## Getting Started with Development

1. Clone this repository
2. Install dependencies: `npm install`
3. Run the development server: `npm run dev`
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## License

This project is licensed under the MIT License.

## User Types

The application supports different user types with specific permissions:

### Basic User
- Access to text-to-video and text-to-image generation
- Limited credits (5 by default)

### Unlimited User
- Access to all generation features
- More credits (10 by default)

### Agency-Basic User
- All features of Unlimited users
- Can create and manage up to 50 Basic users
- Access to User Access Control panel

### Agency-Deluxe User
- All features of Unlimited users
- Can create and manage unlimited Basic users
- Access to User Access Control panel

### Admin User
- Full access to all features
- User management for all user types
- Access to Google Veo3 features
- **Reset All Credits:** Admins can reset the credits for all users at once by clicking the "Reset All Credits" button on the user management page. This action requires confirmation.

## User Access Control

Agency users (agency-basic and agency-deluxe) have access to a User Access Control panel where they can:

1. Create new basic users
2. View all basic users they've created
3. Edit basic user details and credits
4. Delete basic users

Agency-basic users are limited to creating 50 basic users, while agency-deluxe users can create unlimited basic users.
