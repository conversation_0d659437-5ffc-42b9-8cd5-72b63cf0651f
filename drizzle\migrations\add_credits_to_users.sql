-- Add credits and lastCreditReset fields to users table

ALTER TABLE "users" ADD COLUMN "credits" integer NOT NULL DEFAULT 5;
ALTER TABLE "users" ADD COLUMN "last_credit_reset" timestamp DEFAULT now() NOT NULL;

-- Update existing users with appropriate credits based on their user_type
UPDATE "users" SET "credits" = 5 WHERE "user_type" = 'basic';
UPDATE "users" SET "credits" = 5 WHERE "user_type" = 'admin';
UPDATE "users" SET "credits" = 10 WHERE "user_type" = 'unlimited';
UPDATE "users" SET "credits" = 5 WHERE "user_type" = 'agency-basic';
UPDATE "users" SET "credits" = 5 WHERE "user_type" = 'agency-deluxe'; 