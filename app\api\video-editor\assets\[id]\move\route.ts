import { NextRequest, NextResponse } from "next/server";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { folderId } = body;

    // In a real implementation, you would update the asset's folderId in the database
    const updatedAsset = {
      id,
      folderId: folderId,
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json(updatedAsset);
  } catch (error) {
    console.error("Error moving asset:", error);
    return NextResponse.json(
      { error: "Failed to move asset" },
      { status: 500 }
    );
  }
}
