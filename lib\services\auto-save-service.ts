import type { Project } from "@/types/video-editor";

interface AutoSaveConfig {
  interval: number; // milliseconds
  maxRetries: number;
  retryDelay: number; // milliseconds
}

interface AutoSaveState {
  projectId: string;
  userId: string;
  lastSaved: Date;
  isDirty: boolean;
  isSaving: boolean;
  retryCount: number;
}

export class AutoSaveService {
  private static instances = new Map<string, AutoSaveService>();
  private intervalId: NodeJS.Timeout | null = null;
  private state: AutoSaveState;
  private config: AutoSaveConfig;
  private onSaveCallback?: (success: boolean, error?: Error) => void;
  private onStateChangeCallback?: (state: AutoSaveState) => void;

  private constructor(
    projectId: string,
    userId: string,
    config: Partial<AutoSaveConfig> = {}
  ) {
    this.config = {
      interval: 30000, // 30 seconds default
      maxRetries: 3,
      retryDelay: 5000, // 5 seconds
      ...config,
    };

    this.state = {
      projectId,
      userId,
      lastSaved: new Date(),
      isDirty: false,
      isSaving: false,
      retryCount: 0,
    };
  }

  /**
   * Get or create auto-save instance for a project
   */
  static getInstance(
    projectId: string,
    userId: string,
    config?: Partial<AutoSaveConfig>
  ): AutoSaveService {
    const key = `${projectId}-${userId}`;

    if (!this.instances.has(key)) {
      this.instances.set(key, new AutoSaveService(projectId, userId, config));
    }

    return this.instances.get(key)!;
  }

  /**
   * Start auto-save for the project
   */
  start(): void {
    if (this.intervalId) {
      return; // Already started
    }

    this.intervalId = setInterval(() => {
      this.performAutoSave();
    }, this.config.interval);
  }

  /**
   * Stop auto-save for the project
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Mark project as dirty (needs saving)
   */
  markDirty(): void {
    this.state.isDirty = true;
    this.notifyStateChange();
  }

  /**
   * Force save immediately
   */
  async forceSave(projectData: Partial<Project>): Promise<boolean> {
    return this.saveProject(projectData);
  }

  /**
   * Update auto-save configuration
   */
  updateConfig(config: Partial<AutoSaveConfig>): void {
    this.config = { ...this.config, ...config };

    // Restart with new interval if running
    if (this.intervalId) {
      this.stop();
      this.start();
    }
  }

  /**
   * Set callback for save events
   */
  onSave(callback: (success: boolean, error?: Error) => void): void {
    this.onSaveCallback = callback;
  }

  /**
   * Set callback for state changes
   */
  onStateChange(callback: (state: AutoSaveState) => void): void {
    this.onStateChangeCallback = callback;
  }

  /**
   * Get current auto-save state
   */
  getState(): AutoSaveState {
    return { ...this.state };
  }

  /**
   * Destroy auto-save instance
   */
  destroy(): void {
    this.stop();
    const key = `${this.state.projectId}-${this.state.userId}`;
    AutoSaveService.instances.delete(key);
  }

  /**
   * Perform auto-save if needed
   */
  private async performAutoSave(): Promise<void> {
    if (!this.state.isDirty || this.state.isSaving) {
      return;
    }

    // Get current project data from store or cache
    // In a real implementation, this would get the current project state
    // For now, we'll just mark as saved since the actual saving logic
    // would need to be integrated with the project store

    try {
      this.state.isSaving = true;
      this.notifyStateChange();

      // This would typically get the current project state from the store
      // and save it using ProjectService.updateProject()
      // For now, we'll simulate a successful save
      await new Promise((resolve) => setTimeout(resolve, 100));

      this.state.isDirty = false;
      this.state.lastSaved = new Date();
      this.state.retryCount = 0;
      this.state.isSaving = false;

      this.notifyStateChange();
      this.onSaveCallback?.(true);
    } catch (error) {
      this.state.isSaving = false;
      this.state.retryCount++;

      if (this.state.retryCount < this.config.maxRetries) {
        // Schedule retry
        setTimeout(() => {
          this.performAutoSave();
        }, this.config.retryDelay);
      } else {
        // Max retries reached, reset counter and notify error
        this.state.retryCount = 0;
        this.onSaveCallback?.(false, error as Error);
      }

      this.notifyStateChange();
    }
  }

  /**
   * Save project data
   */
  private async saveProject(projectData: Partial<Project>): Promise<boolean> {
    try {
      this.state.isSaving = true;
      this.notifyStateChange();

      const response = await fetch(
        `/api/video-editor/projects/${this.state.projectId}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(projectData),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to save project");
      }

      this.state.isDirty = false;
      this.state.lastSaved = new Date();
      this.state.retryCount = 0;
      this.state.isSaving = false;

      this.notifyStateChange();
      this.onSaveCallback?.(true);

      return true;
    } catch (error) {
      this.state.isSaving = false;
      this.state.retryCount++;
      this.notifyStateChange();
      this.onSaveCallback?.(false, error as Error);
      return false;
    }
  }

  /**
   * Notify state change callback
   */
  private notifyStateChange(): void {
    this.onStateChangeCallback?.(this.getState());
  }

  /**
   * Clean up all instances (useful for testing or app shutdown)
   */
  static destroyAll(): void {
    for (const instance of this.instances.values()) {
      instance.destroy();
    }
    this.instances.clear();
  }
}

/**
 * Hook for React components to use auto-save
 */
export function useAutoSave(
  projectId: string,
  userId: string,
  config?: Partial<AutoSaveConfig>
) {
  const autoSave = AutoSaveService.getInstance(projectId, userId, config);

  return {
    start: () => autoSave.start(),
    stop: () => autoSave.stop(),
    markDirty: () => autoSave.markDirty(),
    forceSave: (data: Partial<Project>) => autoSave.forceSave(data),
    updateConfig: (newConfig: Partial<AutoSaveConfig>) =>
      autoSave.updateConfig(newConfig),
    onSave: (callback: (success: boolean, error?: Error) => void) =>
      autoSave.onSave(callback),
    onStateChange: (callback: (state: AutoSaveState) => void) =>
      autoSave.onStateChange(callback),
    getState: () => autoSave.getState(),
    destroy: () => autoSave.destroy(),
  };
}
