import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { hash } from "bcrypt";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { auth } from "@/lib/auth";
import { desc } from "drizzle-orm";

// Schema for creating a new user
const createUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  userType: z.enum(["basic", "unlimited", "agency-basic", "agency-deluxe", "admin"]).default("basic"),
});

// Function to get initial credits based on user type
function getInitialCredits(userType: string): number {
  switch (userType) {
    case "basic":
      return 5;
    case "unlimited":
      return 10;
    case "agency-basic":
    case "agency-deluxe":
      return 5;
    case "admin":
      return 5;
    default:
      return 5;
  }
}

// Helper function to check if user is admin
async function isAdmin() {
  const session = await auth();
  if (!session || session.user.userType !== "admin") {
    return false;
  }
  return true;
}

// GET - Get all users
export async function GET() {
  try {
    // Verify the requestor is an admin
    if (!await isAdmin()) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Get all users, ordered by most recently created first
    const allUsers = await db.query.users.findMany({
      orderBy: [desc(users.createdAt)],
    });

    // Return the users with sensitive information removed
    return NextResponse.json({
      users: allUsers.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        userType: user.userType,
        credits: user.credits,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 });
  }
}

// POST - Create a new user
export async function POST(request: Request) {
  try {
    // Verify the requestor is an admin
    if (!await isAdmin()) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const result = createUserSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.flatten() },
        { status: 400 }
      );
    }
    
    const { name, email, password, userType } = result.data;
    
    // Check if email already exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.email, email)
    });
    
    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }
    
    // Hash the password
    const hashedPassword = await hash(password, 12);
    
    // Get initial credits based on user type
    const initialCredits = getInitialCredits(userType);
    
    // Create the new user
    const userId = uuidv4();
    await db.insert(users).values({
      id: userId,
      name,
      email,
      password: hashedPassword,
      userType: userType,
      credits: initialCredits,
      lastCreditReset: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    // Return the newly created user (without password)
    const newUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, userId)
    });
    
    return NextResponse.json({
      user: {
        id: newUser?.id,
        name: newUser?.name,
        email: newUser?.email,
        userType: newUser?.userType,
        credits: newUser?.credits,
        createdAt: newUser?.createdAt,
        updatedAt: newUser?.updatedAt,
      }
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json({ error: "Failed to create user" }, { status: 500 });
  }
} 