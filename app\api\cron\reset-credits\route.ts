import { db, eq, schema } from "@/lib/db";
import { NextResponse } from 'next/server';

export async function POST() {
  try {
    // Optional: Add some basic security with a secret key
    // Uncomment and use this if you want API key protection
    // const { searchParams } = new URL(request.url);
    // const apiKey = searchParams.get('apiKey');
    // if (apiKey !== process.env.CRON_API_KEY) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }
    
    const creditsByUserType = {
      'basic': 15,
      'unlimited': 25,
      'agency-deluxe': 15,
      'admin': 15,
      'agency-basic': 15
    };
    
    const now = new Date();
    const results: Record<string, { count: number; credits: number }> = {};
    
    for (const [userType, creditAmount] of Object.entries(creditsByUserType)) {
      const updatedUsers = await db
        .update(schema.users)
        .set({ 
          credits: creditAmount,
          lastCreditReset: now
        })
        .where(eq(schema.users.userType, userType))
        .returning({ id: schema.users.id, email: schema.users.email });
      
      results[userType] = {
        count: updatedUsers.length,
        credits: creditAmount
      };
    }
    
    return NextResponse.json({
      success: true,
      message: 'Credits reset successfully',
      timestamp: now.toISOString(),
      results
    });
  } catch (error) {
    console.error('Error resetting credits:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to reset credits',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 