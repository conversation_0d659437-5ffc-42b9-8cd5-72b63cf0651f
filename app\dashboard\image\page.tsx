"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Sparkles, ImageIcon, Loader2, Upload, AlertCircle, Lock } from "lucide-react"
import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRouter } from "next/navigation"
import { useCreditStore } from "@/lib/stores/creditStore"

export default function ImageToVideoPage() {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>("");
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("worst quality, inconsistent motion, blurry, jittery, distorted");
  const [resolution, setResolution] = useState("720p");
  const [aspectRatio, setAspectRatio] = useState("auto");
  const [error, setError] = useState<string | null>(null);
  const [generationStatus, setGenerationStatus] = useState<string | null>(null);
  
  // Get credits from global store
  const { credits, fetchCredits, decrementCredit } = useCreditStore();
  const hasCredits = credits > 0;
  
  // Fetch credits when component mounts
  useEffect(() => {
    fetchCredits();
  }, [fetchCredits]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      
      // Upload the file immediately
      await uploadFile(file);
    }
  };
  
  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append("file", file);
      
      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload image");
      }
      
      const data = await response.json();
      setUploadedImageUrl(data.url);
    } catch (err) {
      console.error("Error uploading image:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred during upload");
      setSelectedFile(null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError("Please enter a prompt");
      return;
    }
    
    if (!uploadedImageUrl) {
      setError("Please upload an image");
      return;
    }
    
    if (!hasCredits) {
      setError("You don't have enough credits to generate a video. Credits reset daily.");
      return;
    }
    
    try {
      setIsGenerating(true);
      setError(null);
      setGenerationStatus("Submitting request...");
      
      const response = await fetch("/api/image-to-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          negative_prompt: negativePrompt,
          resolution,
          aspect_ratio: aspectRatio,
          image_url: uploadedImageUrl,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate video");
      }
      
      // Immediately decrement credit in the UI
      decrementCredit();
      setGenerationStatus("Processing your image...");
      
      // Redirect to the videos page
      router.push("/dashboard/myvideos");
      router.refresh();
    } catch (err) {
      console.error("Error generating video:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
      setGenerationStatus(null);
      
      // Refresh credits to get current state
      fetchCredits();
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="w-full">
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <ImageIcon className="w-5 h-5 text-purple-400" /> Image to Video
          </CardTitle>
          <CardDescription className="text-gray-400">
            Upload an image and generate a video sequence.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label className="text-gray-200 font-medium mb-1">Upload Image</Label>
            <div className="border-2 border-dashed border-gray-700 rounded-lg p-6 text-center bg-gray-800 cursor-pointer hover:border-purple-500 transition-colors">
              <Input 
                type="file" 
                className="hidden" 
                id="file-upload"
                onChange={handleFileChange}
                accept="image/jpeg,image/png,image/gif"
                disabled={isUploading || isGenerating}
              />
              <Label htmlFor="file-upload" className="cursor-pointer">
                {isUploading ? (
                  <Loader2 className="mx-auto h-12 w-12 text-purple-400 animate-spin" />
                ) : (
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                )}
                <p className="mt-2 text-sm text-gray-400">
                  <span className="font-semibold text-purple-400">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
              </Label>
              
              {selectedFile && (
                <div className="mt-4 text-left">
                  <p className="text-sm text-gray-300 mb-2">Selected image:</p>
                  <div className="flex items-center gap-2 px-2 py-1 bg-gray-700 rounded text-sm text-gray-300">
                    {selectedFile.name}
                    {uploadedImageUrl && (
                      <span className="text-green-400 text-xs ml-auto">✓ Uploaded</span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div>
            <Label htmlFor="prompt" className="text-gray-200 font-medium mb-1">
              Video Prompt
            </Label>
            <Textarea
              id="prompt"
              placeholder="Describe how the image should animate..."
              className="min-h-24 bg-gray-800 border-gray-700 text-white"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="negativePrompt" className="text-gray-200 font-medium mb-1">
              Negative Prompt
            </Label>
            <Textarea
              id="negativePrompt"
              placeholder="What to avoid in the generated video..."
              className="min-h-20 bg-gray-800 border-gray-700 text-white"
              value={negativePrompt}
              onChange={(e) => setNegativePrompt(e.target.value)}
            />
          </div>
          
          <div className="flex gap-4">
            <div className="flex-1">
              <Label className="text-gray-200 font-medium mb-1">Resolution</Label>
              <Select value={resolution} onValueChange={setResolution}>
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select resolution" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="480p">480p</SelectItem>
                  <SelectItem value="720p">720p</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Label className="text-gray-200 font-medium mb-1">Aspect Ratio</Label>
              <Select value={aspectRatio} onValueChange={setAspectRatio}>
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select aspect ratio" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="auto">Auto (from image)</SelectItem>
                  <SelectItem value="16:9">16:9 (Landscape)</SelectItem>
                  <SelectItem value="1:1">1:1 (Square)</SelectItem>
                  <SelectItem value="9:16">9:16 (Portrait)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {error && (
            <div className="flex items-center gap-2 text-red-500 text-sm bg-red-950/30 p-3 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
          
          <Button 
            className="w-full bg-purple-600 hover:bg-purple-700 text-white text-base h-12"
            onClick={handleGenerate}
            disabled={isGenerating || isUploading || !uploadedImageUrl || !prompt.trim() || !hasCredits}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" /> Generating...
              </>
            ) : !hasCredits ? (
              <>
                <Lock className="w-5 h-5 mr-2" /> Insufficient Credits
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" /> Generate Video
              </>
            )}
          </Button>
          
          {isGenerating && (
            <div className="mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin text-purple-400" />
                <span className="text-gray-300">{generationStatus || "Processing your image..."}</span>
              </div>
              <div className="mt-2 h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                <div className="h-full bg-purple-600 rounded-full animate-pulse" style={{ width: "40%" }}></div>
              </div>
              <p className="text-gray-400 text-sm mt-2">
                Video generation may take several minutes. You&apos;ll be redirected to your videos page when complete.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 