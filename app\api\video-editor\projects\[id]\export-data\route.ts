import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ProjectService } from "@/lib/services/project-service";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const exportData = await ProjectService.exportProject(id, session.user.id);

    if (!exportData) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Set headers for file download
    const headers = new Headers();
    headers.set("Content-Type", "application/json");
    headers.set(
      "Content-Disposition",
      `attachment; filename="project-${id}-backup.json"`
    );

    return new NextResponse(JSON.stringify(exportData, null, 2), {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error("Error exporting project data:", error);
    return NextResponse.json(
      { error: "Failed to export project data" },
      { status: 500 }
    );
  }
}
