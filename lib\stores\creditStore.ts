import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface CreditState {
  // Credit information
  credits: number;
  maxCredits: number;
  hoursUntilReset: number | null;
  lastUpdated: number;
  
  // Loading state
  isLoading: boolean;
  
  // Actions
  setCredits: (credits: number) => void;
  setMaxCredits: (maxCredits: number) => void;
  setHoursUntilReset: (hours: number | null) => void;
  fetchCredits: () => Promise<void>;
  decrementCredit: () => void;
  resetCredits: () => void;
  syncWithSession: () => Promise<void>;
}

export const useCreditStore = create<CreditState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        credits: 0,
        maxCredits: 5, // Default for basic users
        hoursUntilReset: null,
        lastUpdated: Date.now(),
        isLoading: false,
        
        // Actions
        setCredits: (credits) => set({ credits, lastUpdated: Date.now() }),
        
        setMaxCredits: (maxCredits) => set({ maxCredits }),
        
        setHoursUntilReset: (hours) => set({ hoursUntilReset: hours }),
        
        fetchCredits: async () => {
          const state = get();
          
          // Don't fetch if we've fetched recently (within 1 minute)
          if (!state.isLoading && (Date.now() - state.lastUpdated > 60000)) {
            try {
              set({ isLoading: true });
              const response = await fetch('/api/user/stats');
              
              if (response.ok) {
                const data = await response.json();
                
                if (data.stats?.credits !== undefined) {
                  set({ 
                    credits: data.stats.credits,
                    hoursUntilReset: data.stats.hoursUntilReset || null,
                    lastUpdated: Date.now()
                  });
                }
              }
            } catch (error) {
              console.error('Error fetching credits:', error);
            } finally {
              set({ isLoading: false });
            }
          }
        },
        
        decrementCredit: () => set((state) => ({ 
          credits: Math.max(0, state.credits - 1),
          lastUpdated: Date.now()
        })),
        
        resetCredits: () => {
          // This would typically be called from the server
          // when daily reset happens
          const state = get();
          set({ 
            credits: state.maxCredits,
            hoursUntilReset: 24,
            lastUpdated: Date.now()
          });
        },
        
        // New function to sync with session
        syncWithSession: async () => {
          try {
            // Force fetch the latest credits from the API regardless of last update time
            set({ isLoading: true });
            const response = await fetch('/api/user/stats');
            
            if (response.ok) {
              const data = await response.json();
              
              if (data.stats?.credits !== undefined) {
                set({ 
                  credits: data.stats.credits,
                  hoursUntilReset: data.stats.hoursUntilReset || null,
                  lastUpdated: Date.now()
                });
              }
            }
          } catch (error) {
            console.error('Error syncing credits with session:', error);
          } finally {
            set({ isLoading: false });
          }
        }
      }),
      {
        name: 'credit-store',
        // Only persist certain parts of the state
        partialize: (state) => ({ 
          credits: state.credits,
          maxCredits: state.maxCredits,
          hoursUntilReset: state.hoursUntilReset,
          lastUpdated: state.lastUpdated
        })
      }
    )
  )
); 