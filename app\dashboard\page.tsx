"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { VideoGeneration } from "@/lib/schema";
import {
  Clock,
  Loader2,
  Lock,
  Sparkles,
  TrendingUp,
  User,
  Users,
  Video,
  Image,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useCreditStore } from "@/lib/stores/creditStore";
import { ProtectedRoute } from "@/components/auth/protected-route";

interface UserStats {
  totalVideos: number;
  completedVideos: number;
  totalAppVideos: number;
}

export default function DashboardHomePage() {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [recentVideos, setRecentVideos] = useState<VideoGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const { credits, hoursUntilReset, syncWithSession } = useCreditStore();

  // Sync credits with session when dashboard loads
  useEffect(() => {
    syncWithSession();
  }, [syncWithSession]);

  useEffect(() => {
    async function fetchUserStats() {
      try {
        setLoading(true);

        // Fetch videos separately
        const videosResponse = await fetch("/api/videos?limit=5");

        if (!videosResponse.ok) {
          throw new Error("Failed to fetch recent videos");
        }

        const videosData = await videosResponse.json();
        setRecentVideos(videosData.videos || []);

        // Fetch total app videos count
        const adminStatsResponse = await fetch("/api/admin/stats");
        let totalAppVideos = 0;

        if (adminStatsResponse.ok) {
          const adminStatsData = await adminStatsResponse.json();
          totalAppVideos = adminStatsData.stats?.totalVideos || 0;
        }

        // Set basic stats
        setStats({
          totalVideos: videosData.totalCount || 0,
          completedVideos:
            videosData.videos?.filter(
              (v: VideoGeneration) => v.status === "completed"
            ).length || 0,
          totalAppVideos: totalAppVideos,
        });
      } catch (err) {
        console.error("Error fetching user stats:", err);
        setError("Failed to load user statistics. Please refresh the page.");
      } finally {
        setLoading(false);
      }
    }

    fetchUserStats();
  }, [credits]);

  // Quick action links
  const quickActions = [
    {
      title: "New Video",
      icon: Video,
      path: "/dashboard/video",
      bgColor: "bg-purple-900/30",
      textColor: "text-purple-400",
    },
    {
      title: "Text to Image",
      icon: Image,
      path: "/dashboard/text-to-image",
      bgColor: "bg-indigo-900/30",
      textColor: "text-indigo-400",
    },
    {
      title: "My Videos",
      icon: Video,
      path: "/dashboard/myvideos",
      bgColor: "bg-blue-900/30",
      textColor: "text-blue-400",
    },
    {
      title: "Profile",
      icon: User,
      path: "/dashboard/settings",
      bgColor: "bg-green-900/30",
      textColor: "text-green-400",
    },
    {
      title: "Change Password",
      icon: Lock,
      path: "/dashboard/settings",
      bgColor: "bg-amber-900/30",
      textColor: "text-amber-400",
    },
  ];

  return (
    <ProtectedRoute>
      <div className="w-full">
        {/* Welcome Card */}
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-400" /> Welcome to FLOWY
              AI
            </CardTitle>
            <CardDescription className="text-gray-400">
              Your AI-powered video creation platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="p-4 sm:p-6 bg-gradient-to-r from-purple-900/50 to-indigo-900/50 rounded-lg border border-purple-800">
              <h3 className="text-xl font-semibold text-white mb-2">
                Ready to create your first video?
              </h3>
              <p className="text-gray-300 mb-4">
                Get started with our AI-powered tools to create professional
                videos in minutes.
              </p>
              <div className="flex flex-wrap gap-3">
                <Link href="/dashboard/video">
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                    Text to Video
                  </Button>
                </Link>
                <Link href="/dashboard/image">
                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                    Image to Video
                  </Button>
                </Link>
                <Link href="/dashboard/text-to-image">
                  <Button className="bg-indigo-600 hover:bg-indigo-700 text-white">
                    Text to Image
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {loading ? (
            // Loading state
            Array(3)
              .fill(0)
              .map((_, index) => (
                <Card key={index} className="bg-gray-900 border-gray-800">
                  <CardContent className="p-4 sm:p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center">
                        <Loader2 className="w-6 h-6 text-gray-600 animate-spin" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-6 bg-gray-800 rounded w-16"></div>
                      <div className="h-4 bg-gray-800 rounded w-24"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
          ) : (
            <>
              <Card className="bg-gray-900 border-gray-800">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-900/30 rounded-full flex items-center justify-center">
                      <Video className="w-6 h-6 text-blue-400" />
                    </div>
                    <span className="text-xs text-gray-500 font-medium">
                      TOTAL
                    </span>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-2xl font-bold text-white">
                      {stats?.totalVideos || 0}
                    </h3>
                    <p className="text-gray-400 text-sm">Videos Created</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-800">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-green-900/30 rounded-full flex items-center justify-center">
                      <Users className="w-6 h-6 text-green-400" />
                    </div>
                    <span className="text-xs text-gray-500 font-medium">
                      TOTAL
                    </span>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-2xl font-bold text-white">
                      {stats?.totalAppVideos?.toLocaleString() || 0}
                    </h3>
                    <p className="text-gray-400 text-sm">All Videos Created</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-800">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Clock className="w-6 h-6 text-purple-400" />
                    </div>
                    <span className="text-xs text-gray-500 font-medium">
                      AVAILABLE
                    </span>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-2xl font-bold text-white">{credits}</h3>
                    <p className="text-gray-400 text-sm">
                      Credits
                      {hoursUntilReset ? ` (Reset in ${hoursUntilReset}h)` : ""}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        {/* Recent Activity */}
        <Card className="bg-gray-900 border-gray-800 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-purple-400" /> Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              // Loading state for recent activity
              <div className="space-y-3">
                {Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-3 p-3 border border-gray-800 rounded-lg bg-gray-800/50"
                    >
                      <div className="w-10 h-10 bg-gray-700 rounded-lg"></div>
                      <div className="space-y-2 flex-1">
                        <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : error ? (
              <div className="text-center py-6">
                <p className="text-red-400">{error}</p>
              </div>
            ) : recentVideos.length > 0 ? (
              <div className="space-y-3">
                {recentVideos.map((video) => (
                  <div
                    key={video.id}
                    className="flex items-center justify-between p-3 border border-gray-800 rounded-lg bg-gray-800/50"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                        <Video className="w-5 h-5 text-gray-300" />
                      </div>
                      <div>
                        <h4 className="text-white font-medium">
                          {video.prompt.substring(0, 30)}...
                        </h4>
                        <p className="text-gray-400 text-xs">
                          {new Date(video.createdAt).toLocaleDateString()}{" "}
                          {new Date(video.createdAt).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                    </div>
                    <div
                      className={`px-2 py-1 rounded text-xs ${
                        video.status === "completed"
                          ? "bg-green-900/30 text-green-400"
                          : video.status === "pending"
                          ? "bg-amber-900/30 text-amber-400"
                          : "bg-red-900/30 text-red-400"
                      }`}
                    >
                      {video.status.charAt(0).toUpperCase() +
                        video.status.slice(1)}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-gray-500">No recent activity found</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="border-t border-gray-800 pt-4">
            <Link prefetch={true} href="/dashboard/myvideos" className="w-full">
              <Button
                variant="outline"
                className="w-full border-gray-700 bg-purple-600 hover:bg-purple-700"
              >
                View All Videos
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-400" /> Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
              {quickActions.map((action, index) => (
                <Link key={index} prefetch={true} href={action.path}>
                  <div className="p-3 sm:p-4 bg-gray-800 border border-gray-700 rounded-lg text-center hover:border-purple-500 transition-colors cursor-pointer">
                    <div
                      className={`w-10 h-10 sm:w-12 sm:h-12 ${action.bgColor} rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3`}
                    >
                      <action.icon
                        className={`w-5 h-5 sm:w-6 sm:h-6 ${action.textColor}`}
                      />
                    </div>
                    <h4 className="text-white font-medium text-sm sm:text-base">
                      {action.title}
                    </h4>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  );
}
