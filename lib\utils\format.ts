/**
 * Format file size in bytes to human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

/**
 * Format duration in seconds to human readable format
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.round(seconds % 60);

  if (minutes < 60) {
    return remainingSeconds > 0
      ? `${minutes}m ${remainingSeconds}s`
      : `${minutes}m`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

/**
 * Format timestamp to human readable format
 */
export function formatTimestamp(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return "Today";
  } else if (diffDays === 1) {
    return "Yesterday";
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return weeks === 1 ? "1 week ago" : `${weeks} weeks ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return months === 1 ? "1 month ago" : `${months} months ago`;
  } else {
    const years = Math.floor(diffDays / 365);
    return years === 1 ? "1 year ago" : `${years} years ago`;
  }
}

/**
 * Format resolution to human readable format
 */
export function formatResolution(width?: number, height?: number): string {
  if (!width || !height) return "Unknown";

  // Common resolution names
  const resolutions: Record<string, string> = {
    "1920x1080": "1080p (Full HD)",
    "1280x720": "720p (HD)",
    "3840x2160": "4K (Ultra HD)",
    "2560x1440": "1440p (2K)",
    "854x480": "480p",
    "640x360": "360p",
  };

  const key = `${width}x${height}`;
  return resolutions[key] || `${width}×${height}`;
}

/**
 * Format aspect ratio to human readable format
 */
export function formatAspectRatio(width?: number, height?: number): string {
  if (!width || !height) return "Unknown";

  const gcd = (a: number, b: number): number => {
    return b === 0 ? a : gcd(b, a % b);
  };

  const divisor = gcd(width, height);
  const ratioWidth = width / divisor;
  const ratioHeight = height / divisor;

  // Common aspect ratios
  const ratios: Record<string, string> = {
    "16:9": "16:9 (Widescreen)",
    "4:3": "4:3 (Standard)",
    "1:1": "1:1 (Square)",
    "21:9": "21:9 (Ultrawide)",
    "9:16": "9:16 (Vertical)",
  };

  const key = `${ratioWidth}:${ratioHeight}`;
  return ratios[key] || `${ratioWidth}:${ratioHeight}`;
}
