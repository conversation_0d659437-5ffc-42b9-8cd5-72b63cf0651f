import { route } from "@fal-ai/server-proxy/nextjs";
import { NextRequest, NextResponse } from "next/server";

// Add a debug route to check if environment variables are properly loaded
export async function GET(request: NextRequest) {
  // Check if the request is for debugging
  const url = new URL(request.url);
  if (url.searchParams.get("debug") === "true") {
    // Return a safe version of the environment variables for debugging
    // Don't include the actual key value, just whether it exists
    return NextResponse.json({
      hasFalKey: !!process.env.FAL_KEY,
      falKeyLength: process.env.FAL_KEY ? process.env.FAL_KEY.length : 0
    });
  }
  
  // Otherwise, use the standard route handler
  return route.GET(request);
}

export const { POST } = route; 