"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useVideoMergerStore } from "@/lib/stores/video-merger-store";
import { AlertCircle, CheckCircle, Merge, TestTube } from "lucide-react";
import { useState } from "react";

/**
 * Test component for video merging functionality
 * This component provides a simple interface to test the video merger features
 */
export function VideoMergerTest() {
  const timelineStore = useTimelineStore();
  const assetStore = useAssetStore();
  const mergerStore = useVideoMergerStore();

  const [testResults, setTestResults] = useState<{
    clipValidation: boolean;
    mergeOperation: boolean;
    compositeVideo: boolean;
    batchMerge: boolean;
  }>({
    clipValidation: false,
    mergeOperation: false,
    compositeVideo: false,
    batchMerge: false,
  });

  // Test clip validation
  const testClipValidation = () => {
    try {
      // Create test clips
      const testClipIds = ["clip1", "clip2", "clip3"];
      const validation = timelineStore.validateClipsForMerging(testClipIds);

      console.log("Clip validation test:", validation);
      setTestResults((prev) => ({ ...prev, clipValidation: true }));
    } catch (error) {
      console.error("Clip validation test failed:", error);
      setTestResults((prev) => ({ ...prev, clipValidation: false }));
    }
  };

  // Test merge operation creation
  const testMergeOperation = async () => {
    try {
      const selectedClips = timelineStore.getSelectedClips();
      const assets = assetStore.assets;

      if (selectedClips.length < 2) {
        console.log("Need at least 2 selected clips for merge test");
        return;
      }

      const outputSettings = {
        format: "mp4" as const,
        quality: "medium" as const,
        resolution: { width: 1920, height: 1080 },
        fps: 30,
        audioSync: true,
      };

      const operation = await mergerStore.mergeClips(
        selectedClips,
        assets,
        outputSettings
      );

      console.log("Merge operation created:", operation);
      setTestResults((prev) => ({ ...prev, mergeOperation: true }));
    } catch (error) {
      console.error("Merge operation test failed:", error);
      setTestResults((prev) => ({ ...prev, mergeOperation: false }));
    }
  };

  // Test composite video creation
  const testCompositeVideo = async () => {
    try {
      const tracks = timelineStore.tracks;
      const assets = assetStore.assets;

      if (tracks.length === 0) {
        console.log("Need at least 1 track for composite test");
        return;
      }

      const settings = {
        tracks,
        projectSettings: {
          width: 1920,
          height: 1080,
          fps: 30,
          duration: 60,
          backgroundColor: "#000000",
        },
        outputFormat: "mp4" as const,
        quality: "medium" as const,
        audioSync: true,
      };

      const operation = await mergerStore.createCompositeVideo(
        settings,
        assets
      );

      console.log("Composite video operation created:", operation);
      setTestResults((prev) => ({ ...prev, compositeVideo: true }));
    } catch (error) {
      console.error("Composite video test failed:", error);
      setTestResults((prev) => ({ ...prev, compositeVideo: false }));
    }
  };

  // Test batch merge
  const testBatchMerge = async () => {
    try {
      const tracks = timelineStore.tracks.filter(
        (track) => track.clips.length > 1
      );
      const assets = assetStore.assets;

      if (tracks.length === 0) {
        console.log("Need tracks with multiple clips for batch merge test");
        return;
      }

      const mergeRequests = tracks.map((track) => ({
        clips: track.clips,
        outputSettings: {
          format: "mp4" as const,
          quality: "medium" as const,
          resolution: { width: 1920, height: 1080 },
          fps: 30,
          audioSync: true,
        },
      }));

      const batchOperation = await mergerStore.batchMergeClips(
        mergeRequests,
        assets
      );

      console.log("Batch merge operation created:", batchOperation);
      setTestResults((prev) => ({ ...prev, batchMerge: true }));
    } catch (error) {
      console.error("Batch merge test failed:", error);
      setTestResults((prev) => ({ ...prev, batchMerge: false }));
    }
  };

  const runAllTests = async () => {
    console.log("Running all video merger tests...");

    testClipValidation();
    await new Promise((resolve) => setTimeout(resolve, 500));

    await testMergeOperation();
    await new Promise((resolve) => setTimeout(resolve, 500));

    await testCompositeVideo();
    await new Promise((resolve) => setTimeout(resolve, 500));

    await testBatchMerge();

    console.log("All tests completed");
  };

  const getTestIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <AlertCircle className="w-4 h-4 text-red-500" />
    );
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="w-5 h-5" />
          Video Merger Test Suite
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getTestIcon(testResults.clipValidation)}
              <span className="font-medium">Clip Validation</span>
            </div>
            <Button size="sm" onClick={testClipValidation}>
              Test
            </Button>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getTestIcon(testResults.mergeOperation)}
              <span className="font-medium">Merge Operation</span>
            </div>
            <Button size="sm" onClick={testMergeOperation}>
              Test
            </Button>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getTestIcon(testResults.compositeVideo)}
              <span className="font-medium">Composite Video</span>
            </div>
            <Button size="sm" onClick={testCompositeVideo}>
              Test
            </Button>
          </div>

          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {getTestIcon(testResults.batchMerge)}
              <span className="font-medium">Batch Merge</span>
            </div>
            <Button size="sm" onClick={testBatchMerge}>
              Test
            </Button>
          </div>
        </div>

        <div className="flex gap-2 pt-4">
          <Button onClick={runAllTests} className="flex-1">
            <Merge className="w-4 h-4 mr-2" />
            Run All Tests
          </Button>
        </div>

        <div className="text-sm text-gray-600">
          <p>
            <strong>Current State:</strong>
          </p>
          <ul className="list-disc list-inside space-y-1">
            <li>Selected clips: {timelineStore.selectedClips.length}</li>
            <li>Total tracks: {timelineStore.tracks.length}</li>
            <li>Total assets: {assetStore.assets.length}</li>
            <li>
              Active merge operations:{" "}
              {mergerStore.getActiveOperations().length}
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
