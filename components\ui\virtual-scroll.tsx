"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { cn } from "@/lib/utils";

export interface VirtualScrollItem {
  id: string;
  height: number;
  data: unknown;
}

interface VirtualScrollProps {
  items: VirtualScrollItem[];
  containerHeight: number;
  renderItem: (item: VirtualScrollItem, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
  scrollToIndex?: number;
  horizontal?: boolean;
  itemWidth?: number; // For horizontal scrolling
}

interface VirtualizedRange {
  startIndex: number;
  endIndex: number;
  visibleStartIndex: number;
  visibleEndIndex: number;
}

export function VirtualScroll({
  items,
  containerHeight,
  renderItem,
  overscan = 5,
  className,
  onScroll,
  scrollToIndex,
  horizontal = false,
  itemWidth = 100,
}: VirtualScrollProps) {
  const [scrollOffset, setScrollOffset] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // Calculate total size and item positions
  const { totalSize, itemPositions } = useMemo(() => {
    let offset = 0;
    const positions: number[] = [];

    for (let i = 0; i < items.length; i++) {
      positions[i] = offset;
      offset += horizontal ? itemWidth : items[i].height;
    }

    return {
      totalSize: offset,
      itemPositions: positions,
    };
  }, [items, horizontal, itemWidth]);

  // Calculate visible range
  const range = useMemo((): VirtualizedRange => {
    if (items.length === 0) {
      return {
        startIndex: 0,
        endIndex: 0,
        visibleStartIndex: 0,
        visibleEndIndex: 0,
      };
    }

    const containerSize = horizontal
      ? scrollElementRef.current?.clientWidth || 0
      : containerHeight;

    // Find first visible item
    let startIndex = 0;
    for (let i = 0; i < items.length; i++) {
      if (
        itemPositions[i] + (horizontal ? itemWidth : items[i].height) >
        scrollOffset
      ) {
        startIndex = i;
        break;
      }
    }

    // Find last visible item
    let endIndex = startIndex;
    for (let i = startIndex; i < items.length; i++) {
      if (itemPositions[i] > scrollOffset + containerSize) {
        break;
      }
      endIndex = i;
    }

    // Apply overscan
    const visibleStartIndex = Math.max(0, startIndex - overscan);
    const visibleEndIndex = Math.min(items.length - 1, endIndex + overscan);

    return {
      startIndex,
      endIndex,
      visibleStartIndex,
      visibleEndIndex,
    };
  }, [
    items,
    itemPositions,
    scrollOffset,
    containerHeight,
    overscan,
    horizontal,
    itemWidth,
  ]);

  // Handle scroll
  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const scrollTop = horizontal
        ? event.currentTarget.scrollLeft
        : event.currentTarget.scrollTop;

      setScrollOffset(scrollTop);
      onScroll?.(scrollTop);
    },
    [onScroll, horizontal]
  );

  // Scroll to specific index
  useEffect(() => {
    if (scrollToIndex !== undefined && scrollElementRef.current) {
      const targetOffset = itemPositions[scrollToIndex] || 0;

      if (horizontal) {
        scrollElementRef.current.scrollLeft = targetOffset;
      } else {
        scrollElementRef.current.scrollTop = targetOffset;
      }
    }
  }, [scrollToIndex, itemPositions, horizontal]);

  // Render visible items
  const visibleItems = useMemo(() => {
    const items_to_render = [];

    for (let i = range.visibleStartIndex; i <= range.visibleEndIndex; i++) {
      const item = items[i];
      if (!item) continue;

      const offset = itemPositions[i];
      const size = horizontal ? itemWidth : item.height;

      items_to_render.push(
        <div
          key={item.id}
          style={{
            position: "absolute",
            [horizontal ? "left" : "top"]: offset,
            [horizontal ? "width" : "height"]: size,
            [horizontal ? "height" : "width"]: "100%",
          }}
        >
          {renderItem(item, i)}
        </div>
      );
    }

    return items_to_render;
  }, [items, range, itemPositions, renderItem, horizontal, itemWidth]);

  return (
    <div
      ref={scrollElementRef}
      className={cn("overflow-auto", className)}
      style={{
        height: horizontal ? "100%" : containerHeight,
        width: horizontal ? "100%" : "100%",
      }}
      onScroll={handleScroll}
    >
      <div
        style={{
          position: "relative",
          [horizontal ? "width" : "height"]: totalSize,
          [horizontal ? "height" : "width"]: "100%",
        }}
      >
        {visibleItems}
      </div>
    </div>
  );
}

// Hook for virtual scrolling with dynamic heights
export function useVirtualScroll<T>({
  items,
  estimateSize,
  getItemKey,
}: {
  items: T[];
  estimateSize: (index: number) => number;
  getItemKey: (item: T, index: number) => string;
}) {
  const [measuredSizes, setMeasuredSizes] = useState<Map<string, number>>(
    new Map()
  );
  const measurementCache = useRef<Map<string, number>>(new Map());

  const virtualItems = useMemo((): VirtualScrollItem[] => {
    return items.map((item, index) => {
      const key = getItemKey(item, index);
      const measuredSize = measuredSizes.get(key);
      const height = measuredSize ?? estimateSize(index);

      return {
        id: key,
        height,
        data: item,
      };
    });
  }, [items, measuredSizes, estimateSize, getItemKey]);

  const measureElement = useCallback((key: string, element: HTMLElement) => {
    if (!element) return;

    const size = element.getBoundingClientRect().height;
    const currentSize = measurementCache.current.get(key);

    if (currentSize !== size) {
      measurementCache.current.set(key, size);
      setMeasuredSizes(new Map(measurementCache.current));
    }
  }, []);

  return {
    virtualItems,
    measureElement,
  };
}

// Timeline-specific virtual scroll component
interface VirtualTimelineProps {
  tracks: Array<{
    id: string;
    height: number;
    [key: string]: unknown;
  }>;
  containerHeight: number;
  renderTrack: (track: unknown, index: number) => React.ReactNode;
  className?: string;
}

export function VirtualTimeline({
  tracks,
  containerHeight,
  renderTrack,
  className,
}: VirtualTimelineProps) {
  const virtualItems = useMemo((): VirtualScrollItem[] => {
    return tracks.map((track) => ({
      id: track.id,
      height: track.height,
      data: track,
    }));
  }, [tracks]);

  return (
    <VirtualScroll
      items={virtualItems}
      containerHeight={containerHeight}
      renderItem={(item, index) => renderTrack(item.data, index)}
      overscan={2}
      className={className}
    />
  );
}
