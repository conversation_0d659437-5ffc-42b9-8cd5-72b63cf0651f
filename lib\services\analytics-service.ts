import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface AnalyticsEvent {
  id: string;
  name: string;
  category: AnalyticsCategory;
  properties: Record<string, unknown>;
  timestamp: number;
  sessionId: string;
  userId?: string;
}

export type AnalyticsCategory =
  | "performance"
  | "user_interaction"
  | "video_editing"
  | "export"
  | "error"
  | "feature_usage"
  | "system";

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  context?: Record<string, unknown>;
}

interface AnalyticsState {
  events: AnalyticsEvent[];
  sessionId: string;
  userId?: string;
  isEnabled: boolean;
  batchSize: number;
  flushInterval: number;

  // Actions
  track: (
    name: string,
    category: AnalyticsCategory,
    properties?: Record<string, unknown>
  ) => void;
  trackPerformance: (metric: Omit<PerformanceMetric, "timestamp">) => void;
  trackError: (error: Error, context?: Record<string, unknown>) => void;
  trackFeatureUsage: (
    feature: string,
    action: string,
    properties?: Record<string, unknown>
  ) => void;
  setUserId: (userId: string) => void;
  flush: () => Promise<void>;
  clear: () => void;
  getEventsByCategory: (category: AnalyticsCategory) => AnalyticsEvent[];
  getSessionStats: () => SessionStats;
}

export interface SessionStats {
  totalEvents: number;
  sessionDuration: number;
  featuresUsed: string[];
  performanceMetrics: PerformanceMetric[];
  errorCount: number;
}

export const useAnalytics = create<AnalyticsState>()(
  devtools((set, get) => ({
    events: [],
    sessionId: crypto.randomUUID(),
    userId: undefined,
    isEnabled: true,
    batchSize: 50,
    flushInterval: 30000, // 30 seconds

    track: (name, category, properties = {}) => {
      const state = get();
      if (!state.isEnabled) return;

      const event: AnalyticsEvent = {
        id: crypto.randomUUID(),
        name,
        category,
        properties,
        timestamp: Date.now(),
        sessionId: state.sessionId,
        userId: state.userId,
      };

      set((state) => ({
        events: [...state.events, event],
      }));

      // Auto-flush if batch size reached
      if (state.events.length >= state.batchSize) {
        state.flush();
      }
    },

    trackPerformance: (metric) => {
      const state = get();
      state.track("performance_metric", "performance", {
        metric_name: metric.name,
        value: metric.value,
        unit: metric.unit,
        context: metric.context,
      });
    },

    trackError: (error, context = {}) => {
      const state = get();
      state.track("error_occurred", "error", {
        error_name: error.name,
        error_message: error.message,
        error_stack: error.stack,
        ...context,
      });
    },

    trackFeatureUsage: (feature, action, properties = {}) => {
      const state = get();
      state.track("feature_used", "feature_usage", {
        feature,
        action,
        ...properties,
      });
    },

    setUserId: (userId) => {
      set({ userId });
    },

    flush: async () => {
      const state = get();
      if (state.events.length === 0) return;

      try {
        // Send events to analytics service
        await sendAnalyticsEvents(state.events);

        // Clear sent events
        set({ events: [] });
      } catch (error) {
        console.error("Failed to send analytics events:", error);
      }
    },

    clear: () => {
      set({ events: [] });
    },

    getEventsByCategory: (category) => {
      const state = get();
      return state.events.filter((event) => event.category === category);
    },

    getSessionStats: () => {
      const state = get();
      const events = state.events;

      const featuresUsed = Array.from(
        new Set(
          events
            .filter((e) => e.category === "feature_usage")
            .map((e) => e.properties.feature as string)
            .filter(Boolean)
        )
      );

      const performanceMetrics = events
        .filter((e) => e.category === "performance")
        .map((e) => ({
          name: e.properties.metric_name as string,
          value: e.properties.value as number,
          unit: e.properties.unit as string,
          timestamp: e.timestamp,
          context: e.properties.context as Record<string, unknown>,
        }));

      const errorCount = events.filter((e) => e.category === "error").length;

      const sessionStart = Math.min(...events.map((e) => e.timestamp));
      const sessionDuration = events.length > 0 ? Date.now() - sessionStart : 0;

      return {
        totalEvents: events.length,
        sessionDuration,
        featuresUsed,
        performanceMetrics,
        errorCount,
      };
    },
  }))
);

// Analytics utilities
export class AnalyticsManager {
  private static instance: AnalyticsManager;
  private flushTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.startAutoFlush();
    this.setupErrorTracking();
    this.setupPerformanceTracking();
  }

  static getInstance(): AnalyticsManager {
    if (!AnalyticsManager.instance) {
      AnalyticsManager.instance = new AnalyticsManager();
    }
    return AnalyticsManager.instance;
  }

  private startAutoFlush() {
    const { flushInterval, flush } = useAnalytics.getState();

    this.flushTimer = setInterval(() => {
      flush();
    }, flushInterval);
  }

  private setupErrorTracking() {
    // Global error handler
    window.addEventListener("error", (event) => {
      useAnalytics.getState().trackError(new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener("unhandledrejection", (event) => {
      useAnalytics
        .getState()
        .trackError(new Error(`Unhandled Promise Rejection: ${event.reason}`), {
          type: "unhandled_promise_rejection",
        });
    });
  }

  private setupPerformanceTracking() {
    // Track page load performance
    window.addEventListener("load", () => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;

      useAnalytics.getState().trackPerformance({
        name: "page_load_time",
        value: navigation.loadEventEnd - navigation.fetchStart,
        unit: "ms",
      });

      useAnalytics.getState().trackPerformance({
        name: "dom_content_loaded",
        value: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        unit: "ms",
      });
    });

    // Track memory usage periodically
    if ("memory" in performance) {
      setInterval(() => {
        const memory = (
          performance as unknown as {
            memory: {
              usedJSHeapSize: number;
              totalJSHeapSize: number;
              jsHeapSizeLimit: number;
            };
          }
        ).memory;

        useAnalytics.getState().trackPerformance({
          name: "memory_usage",
          value: memory.usedJSHeapSize,
          unit: "bytes",
          context: {
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
          },
        });
      }, 60000); // Every minute
    }
  }

  stopAutoFlush() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  // Video editor specific tracking
  trackVideoEdit(action: string, properties: Record<string, unknown> = {}) {
    useAnalytics
      .getState()
      .track(`video_edit_${action}`, "video_editing", properties);
  }

  trackExport(format: string, duration: number, success: boolean) {
    useAnalytics.getState().track("video_export", "export", {
      format,
      duration,
      success,
      timestamp: Date.now(),
    });
  }

  trackAssetImport(type: string, size: number, success: boolean) {
    useAnalytics.getState().track("asset_import", "user_interaction", {
      asset_type: type,
      file_size: size,
      success,
    });
  }

  trackTimelineInteraction(
    action: string,
    clipCount: number,
    duration: number
  ) {
    useAnalytics.getState().track("timeline_interaction", "user_interaction", {
      action,
      clip_count: clipCount,
      timeline_duration: duration,
    });
  }
}

// Send events to analytics service
async function sendAnalyticsEvents(events: AnalyticsEvent[]): Promise<void> {
  // In a real implementation, this would send to your analytics service
  // For now, we'll just log to console in development (reduced frequency)
  if (process.env.NODE_ENV === "development") {
    // Only log every 10th batch to reduce console noise
    if (Math.random() < 0.1) {
      console.log("Analytics Events (sample):", events.length, "events");
    }
    return;
  }

  try {
    const response = await fetch("/api/analytics", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ events }),
    });

    if (!response.ok) {
      throw new Error(`Analytics API error: ${response.statusText}`);
    }
  } catch (error) {
    // Store failed events in localStorage for retry
    const failedEvents = JSON.parse(
      localStorage.getItem("failed_analytics_events") || "[]"
    );
    failedEvents.push(...events);
    localStorage.setItem(
      "failed_analytics_events",
      JSON.stringify(failedEvents)
    );

    throw error;
  }
}

// Hook for analytics
export function useAnalyticsManager() {
  const analytics = useAnalytics();
  const manager = AnalyticsManager.getInstance();

  return {
    ...analytics,
    manager,
    trackVideoEdit: manager.trackVideoEdit.bind(manager),
    trackExport: manager.trackExport.bind(manager),
    trackAssetImport: manager.trackAssetImport.bind(manager),
    trackTimelineInteraction: manager.trackTimelineInteraction.bind(manager),
  };
}

// Performance observer for Core Web Vitals
export function setupWebVitalsTracking() {
  // Largest Contentful Paint
  try {
    if (
      PerformanceObserver.supportedEntryTypes.includes(
        "largest-contentful-paint"
      )
    ) {
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          useAnalytics.getState().trackPerformance({
            name: "largest_contentful_paint",
            value: entry.startTime,
            unit: "ms",
          });
        }
      }).observe({ entryTypes: ["largest-contentful-paint"] });
    }
  } catch (error) {
    console.warn("LCP tracking not supported:", error);
  }

  // First Input Delay
  try {
    if (PerformanceObserver.supportedEntryTypes.includes("first-input")) {
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          useAnalytics.getState().trackPerformance({
            name: "first_input_delay",
            value:
              (entry as unknown as { processingStart: number })
                .processingStart - entry.startTime,
            unit: "ms",
          });
        }
      }).observe({ entryTypes: ["first-input"] });
    }
  } catch (error) {
    console.warn("FID tracking not supported:", error);
  }

  // Cumulative Layout Shift (with error handling for unsupported browsers)
  try {
    const observer = new PerformanceObserver((list) => {
      let clsValue = 0;
      for (const entry of list.getEntries()) {
        const layoutShiftEntry = entry as unknown as {
          hadRecentInput: boolean;
          value: number;
        };
        if (!layoutShiftEntry.hadRecentInput) {
          clsValue += layoutShiftEntry.value;
        }
      }

      useAnalytics.getState().trackPerformance({
        name: "cumulative_layout_shift",
        value: clsValue,
        unit: "score",
      });
    });

    // Check if layout-shift is supported before observing
    if (PerformanceObserver.supportedEntryTypes.includes("layout-shift")) {
      observer.observe({ entryTypes: ["layout-shift"] });
    }
  } catch (error) {
    console.warn("Layout shift tracking not supported:", error);
  }
}

// Initialize analytics
export const analyticsManager = AnalyticsManager.getInstance();
