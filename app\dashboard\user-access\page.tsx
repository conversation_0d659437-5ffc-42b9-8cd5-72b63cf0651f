"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  Table, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableBody, 
  TableCell 
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, UserPlus } from "lucide-react";
import { format } from "date-fns";
import { z } from "zod";

type User = {
  id: string;
  name: string | null;
  email: string | null;
  userType: string | null;
  credits: number | null;
  createdAt: string;
};

export default function UserAccessPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userCount, setUserCount] = useState(0);
  const [userLimit, setUserLimit] = useState(Infinity);
  
  // Add user dialog state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    password: "",
  });
  const [isAddingUser, setIsAddingUser] = useState(false);
  
  // Edit user dialog state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editUserForm, setEditUserForm] = useState({
    name: "",
    email: "",
    credits: 0
  });
  const [isUpdatingUser, setIsUpdatingUser] = useState(false);
  
  // Delete confirmation dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [isDeletingUser, setIsDeletingUser] = useState(false);
  
  // Add state to track which user is being processed
  const [processingUserId, setProcessingUserId] = useState<string | null>(null);
  
  // Check if user is agency type
  useEffect(() => {
    if (!session?.user?.userType?.includes("agency")) {
      router.push("/dashboard");
    } else if (session?.user?.userType === "agency-basic") {
      setUserLimit(50);
    }
  }, [session, router]);
  
  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch("/api/agency/users");
        if (!response.ok) {
          throw new Error("Failed to fetch users");
        }
        const data = await response.json();
        setUsers(data.users);
        setUserCount(data.users.length);
        setLoading(false);
      } catch (error) {
        setError(error instanceof Error ? error.message : "An error occurred");
        setLoading(false);
      }
    };
    
    if (session?.user?.userType?.includes("agency")) {
      fetchUsers();
    }
  }, [session]);
  
  const handleAddUser = async () => {
    try {
      setIsAddingUser(true);
      // Client-side validation
      const schema = z.object({
        name: z.string().min(2, "Name must be at least 2 characters"),
        email: z.string().email("Please enter a valid email address"),
        password: z.string().min(8, "Password must be at least 8 characters"),
      });
      
      schema.parse(newUser);
      
      const response = await fetch("/api/agency/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newUser),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to create user");
      }
      
      const data = await response.json();
      setUsers([...users, data.user]);
      setUserCount(userCount + 1);
      setIsAddDialogOpen(false);
      setNewUser({
        name: "",
        email: "",
        password: "",
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to create user");
    } finally {
      setIsAddingUser(false);
    }
  };
  
  const handleEditUser = async () => {
    if (!editingUser) return;
    
    try {
      setIsUpdatingUser(true);
      setProcessingUserId(editingUser.id);
      
      const response = await fetch(`/api/agency/users/${editingUser.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(editUserForm),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to update user");
      }
      
      const data = await response.json();
      setUsers(users.map(user => user.id === editingUser.id ? data.user : user));
      setIsEditDialogOpen(false);
      setEditingUser(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to update user");
    } finally {
      setIsUpdatingUser(false);
      setProcessingUserId(null);
    }
  };
  
  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    
    try {
      setIsDeletingUser(true);
      setProcessingUserId(userToDelete);
      
      const response = await fetch(`/api/agency/users/${userToDelete}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to delete user");
      }
      
      setUsers(users.filter(user => user.id !== userToDelete));
      setUserCount(userCount - 1);
      setIsDeleteDialogOpen(false);
      setUserToDelete(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to delete user");
    } finally {
      setIsDeletingUser(false);
      setProcessingUserId(null);
    }
  };
  
  const openEditDialog = (user: User) => {
    setEditingUser(user);
    setEditUserForm({
      name: user.name || "",
      email: user.email || "",
      credits: user.credits || 0
    });
    setIsEditDialogOpen(true);
  };
  
  const openDeleteDialog = (userId: string) => {
    setUserToDelete(userId);
    setIsDeleteDialogOpen(true);
  };
  
  if (!session?.user?.userType?.includes("agency")) {
    return null;
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">User Access Control</h1>
          <p className="text-gray-400 mt-1">
            {session?.user?.userType === "agency-basic" 
              ? `Manage your basic users (${userCount}/${userLimit})`
              : `Manage your basic users (${userCount})`
            }
          </p>
        </div>
        <Button 
          onClick={() => setIsAddDialogOpen(true)}
          className="bg-purple-600 hover:bg-purple-700"
          disabled={session?.user?.userType === "agency-basic" && userCount >= userLimit}
        >
          <UserPlus className="w-4 h-4 mr-2" />
          Add User
        </Button>
      </div>
      
      {error && (
        <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-lg">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto"></div>
          <p className="mt-4 text-gray-400">Loading users...</p>
        </div>
      ) : users.length === 0 ? (
        <div className="text-center py-10 border border-dashed border-gray-700 rounded-lg">
          <p className="text-gray-400">No users found. Add your first user to get started.</p>
        </div>
      ) : (
        <div className="border border-gray-800 rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-900 border-b border-gray-800">
                <TableHead className="text-gray-300">Name</TableHead>
                <TableHead className="text-gray-300">Email</TableHead>
                <TableHead className="text-gray-300">Credits</TableHead>
                <TableHead className="text-gray-300">Created</TableHead>
                <TableHead className="text-gray-300 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow 
                  key={user.id} 
                  className="border-b border-gray-800 hover:bg-gray-900/50"
                >
                  <TableCell className="font-medium text-white">{user.name}</TableCell>
                  <TableCell className="text-gray-300">{user.email}</TableCell>
                  <TableCell>
                    <Badge className="bg-green-600 text-white">
                      {user.credits} credits
                    </Badge>
                  </TableCell>
                  <TableCell className="text-gray-400">
                    {format(new Date(user.createdAt), "MMM d, yyyy")}
                  </TableCell>
                  <TableCell className="text-right space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="border-gray-700 hover:bg-gray-800"
                      onClick={() => openEditDialog(user)}
                      disabled={processingUserId === user.id}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="border-gray-700 hover:bg-red-900/30 text-red-500"
                      onClick={() => openDeleteDialog(user.id)}
                      disabled={processingUserId === user.id}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
      
      {/* Add User Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="bg-gray-900 border border-gray-800 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Add New User</DialogTitle>
            <DialogDescription className="text-gray-400">
              Create a new basic user account.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div>
              <Label htmlFor="name" className="text-gray-300">Name</Label>
              <Input 
                id="name" 
                placeholder="Enter name" 
                className="bg-gray-800 border-gray-700 text-white"
                value={newUser.name}
                onChange={(e) => setNewUser({...newUser, name: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="email" className="text-gray-300">Email</Label>
              <Input 
                id="email" 
                placeholder="Enter email" 
                className="bg-gray-800 border-gray-700 text-white"
                value={newUser.email}
                onChange={(e) => setNewUser({...newUser, email: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="password" className="text-gray-300">Password</Label>
              <Input 
                id="password" 
                type="password" 
                placeholder="Enter password" 
                className="bg-gray-800 border-gray-700 text-white"
                value={newUser.password}
                onChange={(e) => setNewUser({...newUser, password: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddDialogOpen(false)}
              className="border-gray-700 text-gray-300"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleAddUser}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={isAddingUser}
            >
              {isAddingUser ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : "Create User"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-gray-900 border border-gray-800 text-white">
          <DialogHeader>
            <DialogTitle className="text-white">Edit User</DialogTitle>
            <DialogDescription className="text-gray-400">
              Update user information.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div>
              <Label htmlFor="edit-name" className="text-gray-300">Name</Label>
              <Input 
                id="edit-name" 
                placeholder="Enter name" 
                className="bg-gray-800 border-gray-700 text-white"
                value={editUserForm.name}
                onChange={(e) => setEditUserForm({...editUserForm, name: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="edit-email" className="text-gray-300">Email</Label>
              <Input 
                id="edit-email" 
                placeholder="Enter email" 
                className="bg-gray-800 border-gray-700 text-white"
                value={editUserForm.email}
                onChange={(e) => setEditUserForm({...editUserForm, email: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="edit-credits" className="text-gray-300">Credits</Label>
              <Input 
                id="edit-credits" 
                type="number" 
                min="0"
                placeholder="Enter credits" 
                className="bg-gray-800 border-gray-700 text-white"
                value={editUserForm.credits}
                onChange={(e) => setEditUserForm({...editUserForm, credits: parseInt(e.target.value) || 0})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsEditDialogOpen(false)}
              className="border-gray-700 text-gray-300"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleEditUser}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={isUpdatingUser}
            >
              {isUpdatingUser ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : "Update User"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="bg-gray-900 border border-gray-800 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">Delete User</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              Are you sure you want to delete this user? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteUser();
              }}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={isDeletingUser}
            >
              {isDeletingUser ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 