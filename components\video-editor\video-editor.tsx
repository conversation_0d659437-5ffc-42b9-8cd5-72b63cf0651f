"use client";

import React, { useRef } from "react";
import { PlayerRef } from "@remotion/player";
import { RemotionTimeline } from "./remotion-timeline";
import { RemotionPreview } from "./remotion-preview";
import { ClipManager } from "./clip-manager";
import { useTimelineSync } from "@/lib/hooks/use-timeline-sync";
import { useProjectStore } from "@/lib/stores/project-store";
import { Button } from "@/components/ui/button";
import { Save, Download } from "lucide-react";

interface VideoEditorProps {
  className?: string;
}

export function VideoEditor({ className = "" }: VideoEditorProps) {
  const playerRef = useRef<PlayerRef>(null);
  const projectStore = useProjectStore();

  // Sync timeline with player
  useTimelineSync({
    playerRef,
    onTimeUpdate: () => {
      // Handle time updates if needed
    },
    onPlayStateChange: () => {
      // Handle play state changes if needed
    },
  });

  const handleSaveProject = async () => {
    try {
      if (projectStore.currentProject) {
        await projectStore.saveProject();
        // Show success notification
        console.log("Project saved successfully");
      }
    } catch (error) {
      console.error("Failed to save project:", error);
      // Show error notification
    }
  };

  const handleExportProject = async () => {
    try {
      if (!projectStore.currentProject) {
        throw new Error("No project to export");
      }

      const exportSettings = {
        format: "mp4" as const,
        quality: "high" as const,
        resolution: {
          width: projectStore.currentProject.settings.width,
          height: projectStore.currentProject.settings.height,
        },
        fps: projectStore.currentProject.settings.fps,
      };

      const outputUrl = await projectStore.exportProject(exportSettings);

      // Create download link
      const link = document.createElement("a");
      link.href = outputUrl;
      link.download = `${projectStore.currentProject.name}-${Date.now()}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log("Video exported successfully");
    } catch (error) {
      console.error("Error exporting video:", error);
      // Show error notification
    }
  };

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900">
        <div>
          <h1 className="text-xl font-bold text-white">Video Editor</h1>
          {projectStore.currentProject && (
            <p className="text-sm text-gray-400">
              {projectStore.currentProject.name}
            </p>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            onClick={handleSaveProject}
            disabled={projectStore.isSaving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="w-4 h-4 mr-2" />
            {projectStore.isSaving ? "Saving..." : "Save"}
          </Button>

          <Button
            onClick={handleExportProject}
            disabled={projectStore.isExporting}
            className="bg-green-600 hover:bg-green-700"
          >
            <Download className="w-4 h-4 mr-2" />
            {projectStore.isExporting ? "Exporting..." : "Export"}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Asset Manager */}
        <div className="w-80 border-r border-gray-700 bg-gray-900 overflow-y-auto">
          <ClipManager />
        </div>

        {/* Center - Preview and Timeline */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Preview Area */}
          <div className="flex-1 p-4 bg-gray-800">
            <RemotionPreview
              className="h-full"
              width={1920}
              height={1080}
              fps={30}
            />
          </div>

          {/* Timeline Area */}
          <div className="h-80 border-t border-gray-700 bg-gray-900 overflow-hidden">
            <RemotionTimeline className="h-full" />
          </div>
        </div>
      </div>
    </div>
  );
}
