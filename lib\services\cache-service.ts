import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface CacheEntry<T = unknown> {
  key: string;
  data: T;
  timestamp: number;
  ttl?: number; // Time to live in milliseconds
  size: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  maxEntries: number;
}

interface CacheState {
  entries: Map<string, CacheEntry>;
  totalSize: number;
  config: CacheConfig;

  // Actions
  set: <T>(key: string, data: T, ttl?: number) => void;
  get: <T>(key: string) => T | undefined;
  has: (key: string) => boolean;
  delete: (key: string) => void;
  clear: () => void;
  cleanup: () => void;
  getStats: () => CacheStats;
  updateConfig: (config: Partial<CacheConfig>) => void;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  averageAccessCount: number;
}

const DEFAULT_CONFIG: CacheConfig = {
  maxSize: 100 * 1024 * 1024, // 100MB
  defaultTTL: 30 * 60 * 1000, // 30 minutes
  maxEntries: 1000,
};

export const useCacheService = create<CacheState>()(
  devtools((set, get) => ({
    entries: new Map(),
    totalSize: 0,
    config: DEFAULT_CONFIG,

    set: (key, data, ttl) => {
      const state = get();
      const size = estimateSize(data);
      const now = Date.now();

      const entry: CacheEntry = {
        key,
        data,
        timestamp: now,
        ttl: ttl || state.config.defaultTTL,
        size,
        accessCount: 0,
        lastAccessed: now,
      };

      set((state) => {
        const newEntries = new Map(state.entries);
        const existingEntry = newEntries.get(key);

        // Remove existing entry size
        let newTotalSize = state.totalSize;
        if (existingEntry) {
          newTotalSize -= existingEntry.size;
        }

        newEntries.set(key, entry);
        newTotalSize += size;

        // Check if we need to evict entries
        if (
          newTotalSize > state.config.maxSize ||
          newEntries.size > state.config.maxEntries
        ) {
          const { entries: cleanedEntries, totalSize: cleanedSize } =
            evictEntries(newEntries, newTotalSize, state.config);

          return {
            entries: cleanedEntries,
            totalSize: cleanedSize,
          };
        }

        return {
          entries: newEntries,
          totalSize: newTotalSize,
        };
      });
    },

    get: (key) => {
      const state = get();
      const entry = state.entries.get(key);

      if (!entry) {
        return undefined;
      }

      const now = Date.now();

      // Check if entry has expired
      if (entry.ttl && now - entry.timestamp > entry.ttl) {
        state.delete(key);
        return undefined;
      }

      // Update access statistics
      set((state) => {
        const newEntries = new Map(state.entries);
        const updatedEntry = {
          ...entry,
          accessCount: entry.accessCount + 1,
          lastAccessed: now,
        };
        newEntries.set(key, updatedEntry);

        return { entries: newEntries };
      });

      return entry.data;
    },

    has: (key) => {
      const state = get();
      const entry = state.entries.get(key);

      if (!entry) return false;

      // Check if expired
      if (entry.ttl && Date.now() - entry.timestamp > entry.ttl) {
        state.delete(key);
        return false;
      }

      return true;
    },

    delete: (key) => {
      set((state) => {
        const newEntries = new Map(state.entries);
        const entry = newEntries.get(key);

        if (entry) {
          newEntries.delete(key);
          return {
            entries: newEntries,
            totalSize: state.totalSize - entry.size,
          };
        }

        return state;
      });
    },

    clear: () => {
      set({
        entries: new Map(),
        totalSize: 0,
      });
    },

    cleanup: () => {
      const state = get();
      const now = Date.now();
      const newEntries = new Map(state.entries);
      let newTotalSize = state.totalSize;

      // Remove expired entries
      for (const [key, entry] of newEntries) {
        if (entry.ttl && now - entry.timestamp > entry.ttl) {
          newEntries.delete(key);
          newTotalSize -= entry.size;
        }
      }

      set({
        entries: newEntries,
        totalSize: newTotalSize,
      });
    },

    getStats: () => {
      const state = get();
      const entries = Array.from(state.entries.values());

      const totalAccesses = entries.reduce(
        (sum, entry) => sum + entry.accessCount,
        0
      );
      const averageAccessCount =
        entries.length > 0 ? totalAccesses / entries.length : 0;

      return {
        totalEntries: entries.length,
        totalSize: state.totalSize,
        hitRate: 0, // Would need to track hits/misses separately
        missRate: 0,
        averageAccessCount,
      };
    },

    updateConfig: (newConfig) => {
      set((state) => ({
        config: { ...state.config, ...newConfig },
      }));
    },
  }))
);

// Utility functions
function estimateSize(data: unknown): number {
  if (data === null || data === undefined) return 0;

  if (typeof data === "string") {
    return data.length * 2; // UTF-16
  }

  if (typeof data === "number") {
    return 8; // 64-bit number
  }

  if (typeof data === "boolean") {
    return 1;
  }

  if (data instanceof ArrayBuffer) {
    return data.byteLength;
  }

  if (data instanceof Blob) {
    return data.size;
  }

  if (data instanceof ImageData) {
    return data.data.length;
  }

  if (data instanceof HTMLCanvasElement) {
    return data.width * data.height * 4; // RGBA
  }

  // For objects, rough estimation
  try {
    return JSON.stringify(data).length * 2;
  } catch {
    return 1000; // Default estimate
  }
}

function evictEntries(
  entries: Map<string, CacheEntry>,
  totalSize: number,
  config: CacheConfig
): { entries: Map<string, CacheEntry>; totalSize: number } {
  const entriesArray = Array.from(entries.values());

  // Sort by LRU (Least Recently Used) with access count consideration
  entriesArray.sort((a, b) => {
    // Prioritize by access count first
    if (a.accessCount !== b.accessCount) {
      return a.accessCount - b.accessCount;
    }
    // Then by last accessed time
    return a.lastAccessed - b.lastAccessed;
  });

  const newEntries = new Map(entries);
  let newTotalSize = totalSize;

  // Remove entries until we're under limits
  for (const entry of entriesArray) {
    if (
      newTotalSize <= config.maxSize * 0.8 &&
      newEntries.size <= config.maxEntries * 0.8
    ) {
      break;
    }

    newEntries.delete(entry.key);
    newTotalSize -= entry.size;
  }

  return { entries: newEntries, totalSize: newTotalSize };
}

// Specialized caches
export class PreviewFrameCache {
  private static instance: PreviewFrameCache;
  private cache = useCacheService.getState();

  static getInstance(): PreviewFrameCache {
    if (!PreviewFrameCache.instance) {
      PreviewFrameCache.instance = new PreviewFrameCache();
    }
    return PreviewFrameCache.instance;
  }

  setFrame(
    videoId: string,
    time: number,
    frame: HTMLCanvasElement | ImageData
  ) {
    const key = `frame:${videoId}:${time}`;
    this.cache.set(key, frame, 10 * 60 * 1000); // 10 minutes TTL
  }

  getFrame(
    videoId: string,
    time: number
  ): HTMLCanvasElement | ImageData | undefined {
    const key = `frame:${videoId}:${time}`;
    return this.cache.get(key);
  }

  hasFrame(videoId: string, time: number): boolean {
    const key = `frame:${videoId}:${time}`;
    return this.cache.has(key);
  }

  clearVideo(videoId: string) {
    const entries = this.cache.entries;
    for (const key of entries.keys()) {
      if (key.startsWith(`frame:${videoId}:`)) {
        this.cache.delete(key);
      }
    }
  }
}

export class ThumbnailCache {
  private static instance: ThumbnailCache;
  private cache = useCacheService.getState();

  static getInstance(): ThumbnailCache {
    if (!ThumbnailCache.instance) {
      ThumbnailCache.instance = new ThumbnailCache();
    }
    return ThumbnailCache.instance;
  }

  setThumbnail(assetId: string, size: string, thumbnail: string | Blob) {
    const key = `thumb:${assetId}:${size}`;
    this.cache.set(key, thumbnail, 60 * 60 * 1000); // 1 hour TTL
  }

  getThumbnail(assetId: string, size: string): string | Blob | undefined {
    const key = `thumb:${assetId}:${size}`;
    return this.cache.get(key);
  }

  hasThumbnail(assetId: string, size: string): boolean {
    const key = `thumb:${assetId}:${size}`;
    return this.cache.has(key);
  }
}

export class CompositionCache {
  private static instance: CompositionCache;
  private cache = useCacheService.getState();

  static getInstance(): CompositionCache {
    if (!CompositionCache.instance) {
      CompositionCache.instance = new CompositionCache();
    }
    return CompositionCache.instance;
  }

  setComposition(projectId: string, timestamp: number, composition: unknown) {
    const key = `comp:${projectId}:${timestamp}`;
    this.cache.set(key, composition, 5 * 60 * 1000); // 5 minutes TTL
  }

  getComposition(projectId: string, timestamp: number): unknown {
    const key = `comp:${projectId}:${timestamp}`;
    return this.cache.get(key);
  }

  clearProject(projectId: string) {
    const entries = this.cache.entries;
    for (const key of entries.keys()) {
      if (key.startsWith(`comp:${projectId}:`)) {
        this.cache.delete(key);
      }
    }
  }
}

// Hook for cache management
export function useCacheManager() {
  const cacheService = useCacheService();

  return {
    ...cacheService,
    previewFrames: PreviewFrameCache.getInstance(),
    thumbnails: ThumbnailCache.getInstance(),
    compositions: CompositionCache.getInstance(),
  };
}

// Initialize caches
export const previewFrameCache = PreviewFrameCache.getInstance();
export const thumbnailCache = ThumbnailCache.getInstance();
export const compositionCache = CompositionCache.getInstance();
