import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface MemoryEntry {
  id: string;
  type: MemoryEntryType;
  size: number;
  lastAccessed: number;
  priority: MemoryPriority;
  data: unknown;
  cleanup?: () => void;
}

export type MemoryEntryType =
  | "video_frame"
  | "audio_buffer"
  | "thumbnail"
  | "preview_cache"
  | "composition_cache"
  | "asset_metadata"
  | "timeline_state";

export type MemoryPriority = "low" | "medium" | "high" | "critical";

interface MemoryState {
  entries: Map<string, MemoryEntry>;
  totalSize: number;
  maxSize: number;
  gcThreshold: number;
  isGCRunning: boolean;

  // Actions
  store: (entry: Omit<MemoryEntry, "lastAccessed">) => void;
  retrieve: (id: string) => MemoryEntry | undefined;
  remove: (id: string) => void;
  updateAccess: (id: string) => void;
  runGarbageCollection: () => Promise<void>;
  setMaxSize: (size: number) => void;
  getMemoryUsage: () => { used: number; available: number; percentage: number };
  clearByType: (type: MemoryEntryType) => void;
  clearLowPriority: () => void;
}

export const useMemoryManagement = create<MemoryState>()(
  devtools((set, get) => ({
    entries: new Map(),
    totalSize: 0,
    maxSize: 500 * 1024 * 1024, // 500MB default
    gcThreshold: 0.8, // Run GC when 80% full
    isGCRunning: false,

    store: (entryData) => {
      const entry: MemoryEntry = {
        ...entryData,
        lastAccessed: Date.now(),
      };

      set((state) => {
        const newEntries = new Map(state.entries);
        const existingEntry = newEntries.get(entry.id);

        // Remove existing entry size from total
        let newTotalSize = state.totalSize;
        if (existingEntry) {
          newTotalSize -= existingEntry.size;
          // Run cleanup if exists
          existingEntry.cleanup?.();
        }

        newEntries.set(entry.id, entry);
        newTotalSize += entry.size;

        return {
          entries: newEntries,
          totalSize: newTotalSize,
        };
      });

      // Check if GC is needed
      const state = get();
      if (
        state.totalSize > state.maxSize * state.gcThreshold &&
        !state.isGCRunning
      ) {
        state.runGarbageCollection();
      }
    },

    retrieve: (id) => {
      const state = get();
      const entry = state.entries.get(id);

      if (entry) {
        // Update last accessed time
        state.updateAccess(id);
        return entry;
      }

      return undefined;
    },

    remove: (id) => {
      set((state) => {
        const newEntries = new Map(state.entries);
        const entry = newEntries.get(id);

        if (entry) {
          // Run cleanup if exists
          entry.cleanup?.();
          newEntries.delete(id);

          return {
            entries: newEntries,
            totalSize: state.totalSize - entry.size,
          };
        }

        return state;
      });
    },

    updateAccess: (id) => {
      set((state) => {
        const newEntries = new Map(state.entries);
        const entry = newEntries.get(id);

        if (entry) {
          newEntries.set(id, {
            ...entry,
            lastAccessed: Date.now(),
          });

          return { entries: newEntries };
        }

        return state;
      });
    },

    runGarbageCollection: async () => {
      const state = get();
      if (state.isGCRunning) return;

      set({ isGCRunning: true });

      try {
        const entries = Array.from(state.entries.values());

        // Sort by priority and last accessed time
        const sortedEntries = entries.sort((a, b) => {
          const priorityWeight = {
            critical: 4,
            high: 3,
            medium: 2,
            low: 1,
          };

          const aPriority = priorityWeight[a.priority];
          const bPriority = priorityWeight[b.priority];

          if (aPriority !== bPriority) {
            return aPriority - bPriority; // Lower priority first
          }

          return a.lastAccessed - b.lastAccessed; // Older first
        });

        // Remove entries until we're under the threshold
        const targetSize = state.maxSize * 0.6; // Target 60% usage
        let currentSize = state.totalSize;
        const toRemove: string[] = [];

        for (const entry of sortedEntries) {
          if (currentSize <= targetSize) break;
          if (entry.priority === "critical") continue; // Never remove critical entries

          toRemove.push(entry.id);
          currentSize -= entry.size;
        }

        // Remove selected entries
        set((state) => {
          const newEntries = new Map(state.entries);
          let newTotalSize = state.totalSize;

          for (const id of toRemove) {
            const entry = newEntries.get(id);
            if (entry) {
              entry.cleanup?.();
              newEntries.delete(id);
              newTotalSize -= entry.size;
            }
          }

          return {
            entries: newEntries,
            totalSize: newTotalSize,
          };
        });

        console.log(
          `GC: Removed ${toRemove.length} entries, freed ${
            (state.totalSize - currentSize) / 1024 / 1024
          }MB`
        );
      } finally {
        set({ isGCRunning: false });
      }
    },

    setMaxSize: (size) => {
      set({ maxSize: size });
    },

    getMemoryUsage: () => {
      const state = get();
      return {
        used: state.totalSize,
        available: state.maxSize - state.totalSize,
        percentage: (state.totalSize / state.maxSize) * 100,
      };
    },

    clearByType: (type) => {
      set((state) => {
        const newEntries = new Map(state.entries);
        let newTotalSize = state.totalSize;

        for (const [id, entry] of newEntries) {
          if (entry.type === type) {
            entry.cleanup?.();
            newEntries.delete(id);
            newTotalSize -= entry.size;
          }
        }

        return {
          entries: newEntries,
          totalSize: newTotalSize,
        };
      });
    },

    clearLowPriority: () => {
      set((state) => {
        const newEntries = new Map(state.entries);
        let newTotalSize = state.totalSize;

        for (const [id, entry] of newEntries) {
          if (entry.priority === "low") {
            entry.cleanup?.();
            newEntries.delete(id);
            newTotalSize -= entry.size;
          }
        }

        return {
          entries: newEntries,
          totalSize: newTotalSize,
        };
      });
    },
  }))
);

// Memory management utilities
export class MemoryManager {
  private static instance: MemoryManager;
  private gcInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startPeriodicGC();
    this.setupMemoryWarnings();
  }

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  private startPeriodicGC() {
    // Run GC every 30 seconds
    this.gcInterval = setInterval(() => {
      const { getMemoryUsage, runGarbageCollection } =
        useMemoryManagement.getState();
      const usage = getMemoryUsage();

      if (usage.percentage > 70) {
        runGarbageCollection();
      }
    }, 30000);
  }

  private setupMemoryWarnings() {
    // Monitor browser memory if available
    if ("memory" in performance) {
      setInterval(() => {
        const memory = (
          performance as unknown as {
            memory: {
              usedJSHeapSize: number;
              totalJSHeapSize: number;
              jsHeapSizeLimit: number;
            };
          }
        ).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;

        if (usedMB > limitMB * 0.9) {
          console.warn(
            `High memory usage: ${usedMB.toFixed(1)}MB / ${limitMB.toFixed(
              1
            )}MB`
          );
          useMemoryManagement.getState().runGarbageCollection();
        }
      }, 10000);
    }
  }

  stopPeriodicGC() {
    if (this.gcInterval) {
      clearInterval(this.gcInterval);
      this.gcInterval = null;
    }
  }

  // Helper methods for common operations
  static storeVideoFrame(
    id: string,
    frameData: ImageData | HTMLCanvasElement,
    priority: MemoryPriority = "medium"
  ) {
    const size =
      frameData instanceof ImageData
        ? frameData.data.length
        : frameData.width * frameData.height * 4; // Assume RGBA

    useMemoryManagement.getState().store({
      id,
      type: "video_frame",
      size,
      priority,
      data: frameData,
      cleanup: () => {
        if (frameData instanceof HTMLCanvasElement) {
          const ctx = frameData.getContext("2d");
          ctx?.clearRect(0, 0, frameData.width, frameData.height);
        }
      },
    });
  }

  static storeThumbnail(
    id: string,
    thumbnail: string | Blob,
    priority: MemoryPriority = "low"
  ) {
    const size =
      thumbnail instanceof Blob ? thumbnail.size : thumbnail.length * 2; // Rough estimate for string

    useMemoryManagement.getState().store({
      id,
      type: "thumbnail",
      size,
      priority,
      data: thumbnail,
    });
  }

  static storeAudioBuffer(
    id: string,
    buffer: AudioBuffer,
    priority: MemoryPriority = "medium"
  ) {
    const size = buffer.length * buffer.numberOfChannels * 4; // 32-bit float

    useMemoryManagement.getState().store({
      id,
      type: "audio_buffer",
      size,
      priority,
      data: buffer,
    });
  }
}

// Hook for memory management
export function useMemoryManager() {
  const memoryService = useMemoryManagement();

  return {
    ...memoryService,
    storeVideoFrame: MemoryManager.storeVideoFrame,
    storeThumbnail: MemoryManager.storeThumbnail,
    storeAudioBuffer: MemoryManager.storeAudioBuffer,
  };
}

// Initialize memory manager
export const memoryManager = MemoryManager.getInstance();
