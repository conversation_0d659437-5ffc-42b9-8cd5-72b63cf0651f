"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Download,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  Play,
  Pause,
  RotateCcw,
} from "lucide-react";
import type { Project } from "@/types/video-editor";

interface BatchExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projects: Project[];
}

interface BatchExportJob {
  projectId: string;
  projectName: string;
  status: "pending" | "exporting" | "completed" | "failed";
  progress: number;
  exportId?: string;
  outputUrl?: string;
  errorMessage?: string;
}

const BATCH_EXPORT_PRESETS = {
  "youtube-batch": {
    name: "YouTube Batch",
    description: "1080p MP4 optimized for YouTube",
    settings: {
      format: "mp4" as const,
      quality: "high" as const,
      resolution: { width: 1920, height: 1080 },
      fps: 30,
      bitrate: 8000,
    },
  },
  "social-media": {
    name: "Social Media",
    description: "720p MP4 for social platforms",
    settings: {
      format: "mp4" as const,
      quality: "medium" as const,
      resolution: { width: 1280, height: 720 },
      fps: 30,
      bitrate: 4000,
    },
  },
  "web-optimized": {
    name: "Web Optimized",
    description: "Compressed for web delivery",
    settings: {
      format: "mp4" as const,
      quality: "medium" as const,
      resolution: { width: 1280, height: 720 },
      fps: 24,
      bitrate: 2500,
    },
  },
};

export function BatchExportDialog({
  open,
  onOpenChange,
  projects,
}: BatchExportDialogProps) {
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string>("youtube-batch");
  const [exportJobs, setExportJobs] = useState<BatchExportJob[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentJobIndex, setCurrentJobIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);

  // Initialize selected projects when dialog opens
  useEffect(() => {
    if (open && projects.length > 0) {
      setSelectedProjects(projects.map((p) => p.id));
    }
  }, [open, projects]);

  // Calculate overall progress
  useEffect(() => {
    if (exportJobs.length > 0) {
      const totalProgress = exportJobs.reduce(
        (sum, job) => sum + job.progress,
        0
      );
      setOverallProgress(totalProgress / exportJobs.length);
    }
  }, [exportJobs]);

  const handleProjectToggle = (projectId: string) => {
    setSelectedProjects((prev) =>
      prev.includes(projectId)
        ? prev.filter((id) => id !== projectId)
        : [...prev, projectId]
    );
  };

  const handleSelectAll = () => {
    setSelectedProjects(projects.map((p) => p.id));
  };

  const handleSelectNone = () => {
    setSelectedProjects([]);
  };

  const initializeExportJobs = () => {
    const jobs: BatchExportJob[] = selectedProjects.map((projectId) => {
      const project = projects.find((p) => p.id === projectId);
      return {
        projectId,
        projectName: project?.name || "Unknown Project",
        status: "pending",
        progress: 0,
      };
    });
    setExportJobs(jobs);
    setCurrentJobIndex(0);
  };

  const exportNextJob = async () => {
    if (isPaused || currentJobIndex >= exportJobs.length) {
      return;
    }

    const currentJob = exportJobs[currentJobIndex];
    if (!currentJob || currentJob.status !== "pending") {
      setCurrentJobIndex((prev) => prev + 1);
      return;
    }

    // Update job status to exporting
    setExportJobs((prev) =>
      prev.map((job, index) =>
        index === currentJobIndex
          ? { ...job, status: "exporting", progress: 0 }
          : job
      )
    );

    try {
      const preset =
        BATCH_EXPORT_PRESETS[
          selectedPreset as keyof typeof BATCH_EXPORT_PRESETS
        ];

      // Start export
      const response = await fetch("/api/video-editor/export", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          projectId: currentJob.projectId,
          settings: preset.settings,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to start export");
      }

      const { exportId } = await response.json();

      // Update job with export ID
      setExportJobs((prev) =>
        prev.map((job, index) =>
          index === currentJobIndex ? { ...job, exportId } : job
        )
      );

      // Poll for progress
      const pollProgress = async () => {
        try {
          const statusResponse = await fetch(
            `/api/video-editor/export/${exportId}`
          );
          if (!statusResponse.ok) {
            throw new Error("Failed to get export status");
          }

          const status = await statusResponse.json();

          // Update job progress
          setExportJobs((prev) =>
            prev.map((job, index) =>
              index === currentJobIndex
                ? { ...job, progress: status.progress }
                : job
            )
          );

          if (status.status === "completed") {
            // Mark job as completed
            setExportJobs((prev) =>
              prev.map((job, index) =>
                index === currentJobIndex
                  ? {
                      ...job,
                      status: "completed",
                      progress: 100,
                      outputUrl: status.outputUrl,
                    }
                  : job
              )
            );

            // Move to next job
            setCurrentJobIndex((prev) => prev + 1);
          } else if (status.status === "failed") {
            // Mark job as failed
            setExportJobs((prev) =>
              prev.map((job, index) =>
                index === currentJobIndex
                  ? {
                      ...job,
                      status: "failed",
                      errorMessage: status.errorMessage || "Export failed",
                    }
                  : job
              )
            );

            // Move to next job
            setCurrentJobIndex((prev) => prev + 1);
          } else if (!isPaused) {
            // Continue polling if not paused
            setTimeout(pollProgress, 1000);
          }
        } catch (error) {
          console.error("Error polling export status:", error);

          // Mark job as failed
          setExportJobs((prev) =>
            prev.map((job, index) =>
              index === currentJobIndex
                ? {
                    ...job,
                    status: "failed",
                    errorMessage: "Failed to get export status",
                  }
                : job
            )
          );

          // Move to next job
          setCurrentJobIndex((prev) => prev + 1);
        }
      };

      // Start polling
      setTimeout(pollProgress, 1000);
    } catch (error) {
      console.error("Export error:", error);

      // Mark job as failed
      setExportJobs((prev) =>
        prev.map((job, index) =>
          index === currentJobIndex
            ? {
                ...job,
                status: "failed",
                errorMessage:
                  error instanceof Error ? error.message : "Export failed",
              }
            : job
        )
      );

      // Move to next job
      setCurrentJobIndex((prev) => prev + 1);
    }
  };

  // Auto-export next job when current job index changes
  useEffect(() => {
    if (isExporting && !isPaused && currentJobIndex < exportJobs.length) {
      const timer = setTimeout(() => {
        exportNextJob();
      }, 500);
      return () => clearTimeout(timer);
    } else if (currentJobIndex >= exportJobs.length && exportJobs.length > 0) {
      // All jobs completed
      setIsExporting(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentJobIndex, isExporting, isPaused, exportJobs.length]);

  const handleStartBatchExport = () => {
    if (selectedProjects.length === 0) return;

    initializeExportJobs();
    setIsExporting(true);
    setIsPaused(false);
  };

  const handlePauseResume = () => {
    setIsPaused(!isPaused);
  };

  const handleStop = () => {
    setIsExporting(false);
    setIsPaused(false);
    setCurrentJobIndex(0);
    setExportJobs([]);
  };

  const handleRetryFailed = () => {
    setExportJobs((prev) =>
      prev.map((job) =>
        job.status === "failed"
          ? { ...job, status: "pending", progress: 0, errorMessage: undefined }
          : job
      )
    );

    // Find first failed job to retry
    const firstFailedIndex = exportJobs.findIndex(
      (job) => job.status === "failed"
    );
    if (firstFailedIndex !== -1) {
      setCurrentJobIndex(firstFailedIndex);
      setIsExporting(true);
      setIsPaused(false);
    }
  };

  const handleDownloadAll = () => {
    const completedJobs = exportJobs.filter(
      (job) => job.status === "completed" && job.outputUrl
    );

    completedJobs.forEach((job, index) => {
      setTimeout(() => {
        const link = document.createElement("a");
        link.href = job.outputUrl!;
        link.download = `${job.projectName}-export.mp4`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }, index * 500); // Stagger downloads
    });
  };

  const handleClose = () => {
    if (!isExporting) {
      setSelectedProjects([]);
      setExportJobs([]);
      setCurrentJobIndex(0);
      setOverallProgress(0);
      onOpenChange(false);
    }
  };

  const completedCount = exportJobs.filter(
    (job) => job.status === "completed"
  ).length;
  const failedCount = exportJobs.filter(
    (job) => job.status === "failed"
  ).length;
  const totalJobs = exportJobs.length;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Batch Export Videos
          </DialogTitle>
          <DialogDescription>
            Export multiple projects with the same settings.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {!isExporting && exportJobs.length === 0 && (
            <>
              {/* Project Selection */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    Select Projects
                  </Label>
                  <div className="space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAll}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectNone}
                    >
                      Select None
                    </Button>
                  </div>
                </div>

                <ScrollArea className="h-48 border rounded-lg p-4">
                  <div className="space-y-3">
                    {projects.map((project) => (
                      <div
                        key={project.id}
                        className="flex items-center space-x-3"
                      >
                        <Checkbox
                          id={project.id}
                          checked={selectedProjects.includes(project.id)}
                          onCheckedChange={() =>
                            handleProjectToggle(project.id)
                          }
                        />
                        <label
                          htmlFor={project.id}
                          className="flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        >
                          {project.name}
                        </label>
                        <Badge variant="outline">
                          {project.settings.width}x{project.settings.height}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              {/* Export Settings */}
              <div className="space-y-2">
                <Label htmlFor="preset">Export Preset</Label>
                <Select
                  value={selectedPreset}
                  onValueChange={setSelectedPreset}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(BATCH_EXPORT_PRESETS).map(
                      ([key, preset]) => (
                        <SelectItem key={key} value={key}>
                          <div>
                            <div className="font-medium">{preset.name}</div>
                            <div className="text-xs text-gray-500">
                              {preset.description}
                            </div>
                          </div>
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}

          {/* Export Progress */}
          {(isExporting || exportJobs.length > 0) && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm text-gray-600">
                    {completedCount}/{totalJobs} completed
                    {failedCount > 0 && `, ${failedCount} failed`}
                  </span>
                </div>
                <Progress value={overallProgress} className="w-full" />
              </div>

              <ScrollArea className="h-64 border rounded-lg p-4">
                <div className="space-y-3">
                  {exportJobs.map((job, index) => (
                    <div
                      key={job.projectId}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        index === currentJobIndex && isExporting
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                          : "border-gray-200 dark:border-gray-700"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {job.status === "pending" && (
                          <div className="w-4 h-4 rounded-full border-2 border-gray-300" />
                        )}
                        {job.status === "exporting" && (
                          <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                        )}
                        {job.status === "completed" && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        {job.status === "failed" && (
                          <AlertCircle className="w-4 h-4 text-red-500" />
                        )}

                        <div>
                          <div className="font-medium text-sm">
                            {job.projectName}
                          </div>
                          {job.status === "failed" && job.errorMessage && (
                            <div className="text-xs text-red-600">
                              {job.errorMessage}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        {job.status === "exporting" && (
                          <div className="text-sm text-gray-600">
                            {job.progress}%
                          </div>
                        )}
                        {job.status === "completed" && job.outputUrl && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const link = document.createElement("a");
                              link.href = job.outputUrl!;
                              link.download = `${job.projectName}-export.mp4`;
                              document.body.appendChild(link);
                              link.click();
                              document.body.removeChild(link);
                            }}
                          >
                            Download
                          </Button>
                        )}
                        <Badge
                          variant={
                            job.status === "completed"
                              ? "default"
                              : job.status === "failed"
                              ? "destructive"
                              : job.status === "exporting"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {job.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>

        <DialogFooter>
          {!isExporting && exportJobs.length === 0 && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleStartBatchExport}
                disabled={selectedProjects.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                Start Batch Export ({selectedProjects.length} projects)
              </Button>
            </>
          )}

          {isExporting && (
            <>
              <Button variant="outline" onClick={handlePauseResume}>
                {isPaused ? (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Resume
                  </>
                ) : (
                  <>
                    <Pause className="w-4 h-4 mr-2" />
                    Pause
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={handleStop}>
                <X className="w-4 h-4 mr-2" />
                Stop
              </Button>
            </>
          )}

          {!isExporting && exportJobs.length > 0 && (
            <>
              {failedCount > 0 && (
                <Button variant="outline" onClick={handleRetryFailed}>
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Retry Failed
                </Button>
              )}
              {completedCount > 0 && (
                <Button onClick={handleDownloadAll}>
                  <Download className="w-4 h-4 mr-2" />
                  Download All ({completedCount})
                </Button>
              )}
              <Button variant="outline" onClick={handleClose}>
                Close
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
