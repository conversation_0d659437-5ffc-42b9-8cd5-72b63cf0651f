import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, videoGenerations } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { fal } from "@fal-ai/client";
import type { AISettings } from "@/types/video-editor";

// Initialize fal.ai client with server-side credentials
fal.config({
  credentials: process.env.FAL_KEY,
});

interface ImageToVideoRequest {
  imageBase64: string;
  mimeType: string;
  prompt: string;
  settings: AISettings;
  projectId?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user details to check credits
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id),
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has enough credits
    if (user.credits <= 0) {
      return NextResponse.json(
        {
          error: "Insufficient credits",
          message:
            "You don't have enough credits to generate a video. Credits reset daily.",
        },
        { status: 403 }
      );
    }

    const body: ImageToVideoRequest = await request.json();
    const { imageBase64, mimeType, prompt, settings, projectId } = body;

    if (!imageBase64) {
      return NextResponse.json(
        { error: "Image data is required" },
        { status: 400 }
      );
    }

    if (!prompt?.trim()) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Validate image format
    if (!mimeType.startsWith("image/")) {
      return NextResponse.json(
        { error: "Invalid image format" },
        { status: 400 }
      );
    }

    // Consume one credit
    await db
      .update(users)
      .set({
        credits: user.credits - 1,
        updatedAt: new Date(),
      })
      .where(eq(users.id, session.user.id));

    // Generate a unique ID for this generation
    const generationId = uuidv4();

    // Convert base64 to data URL for fal.ai
    const imageUrl = `data:${mimeType};base64,${imageBase64}`;

    // Create a record in the database
    await db.insert(videoGenerations).values({
      id: generationId,
      userId: session.user.id,
      prompt,
      resolution: "720p",
      aspectRatio: settings.aspectRatio || "16:9",
      status: "pending",
      imageUrl: imageUrl,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    try {
      // Submit request to fal.ai
      const { request_id } = await fal.queue.submit(
        "fal-ai/ltx-video/image-to-video",
        {
          input: {
            image_url: imageUrl,
            prompt,
            negative_prompt:
              settings.negativePrompt ||
              "worst quality, inconsistent motion, blurry, jittery, distorted",
          },
        }
      );

      // Update the database record with the request ID
      await db
        .update(videoGenerations)
        .set({
          requestId: request_id,
          updatedAt: new Date(),
        })
        .where(eq(videoGenerations.id, generationId));

      // Create asset record for immediate response
      const asset = {
        id: generationId,
        name: `AI Animated: ${prompt.substring(0, 50)}${
          prompt.length > 50 ? "..." : ""
        }`,
        type: "ai-generated" as const,
        url: "", // Will be updated when generation completes
        thumbnailUrl: null,
        duration: settings.duration || 8,
        metadata: {
          width: settings.aspectRatio === "9:16" ? 1080 : 1920,
          height: settings.aspectRatio === "9:16" ? 1920 : 1080,
          fps: 30,
          fileSize: 0,
          mimeType: "video/mp4",
          aiPrompt: prompt,
          aiSettings: settings,
          sourceImageType: mimeType,
          requestId: request_id,
          generationStatus: "pending",
        },
        userId: session.user.id,
        projectId: projectId || null,
        folderId: null,
        createdAt: new Date(),
      };

      return NextResponse.json({
        ...asset,
        requestId: request_id,
        status: "pending",
        creditsRemaining: user.credits - 1,
      });
    } catch (error: unknown) {
      console.error("fal.ai API error:", error);

      // Refund the credit since the generation failed
      await db
        .update(users)
        .set({
          credits: user.credits,
          updatedAt: new Date(),
        })
        .where(eq(users.id, session.user.id));

      // Update the video generation status to failed
      await db
        .update(videoGenerations)
        .set({
          status: "failed",
          updatedAt: new Date(),
        })
        .where(eq(videoGenerations.id, generationId));

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to generate video with external API";
      return NextResponse.json(
        { error: "API error", message: errorMessage },
        { status: 502 }
      );
    }
  } catch (error: unknown) {
    console.error("Error generating video from image:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      { error: "Failed to generate video", message: errorMessage },
      { status: 500 }
    );
  }
}
