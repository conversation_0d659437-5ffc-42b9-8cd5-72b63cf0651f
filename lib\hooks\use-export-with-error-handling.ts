import { useCallback, useState } from "react";
import { useProjectStore } from "@/lib/stores/project-store";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import {
  useError<PERSON>andling,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/lib/services/error-handling-service";
import { toast, createProgressToast } from "@/lib/services/toast-service";
import { loading } from "@/lib/services/loading-service";

interface ExportResult {
  success: boolean;
  downloadUrl?: string;
  error?: string;
  retryable?: boolean;
}

interface ExportSettings {
  format: "mp4" | "webm" | "mov";
  quality: "low" | "medium" | "high" | "ultra";
  resolution: "720p" | "1080p" | "4k";
  fps: number;
  bitrate?: number;
}

interface ExportOptions {
  retryOnFailure?: boolean;
  maxRetries?: number;
  timeout?: number;
}

const DEFAULT_OPTIONS: ExportOptions = {
  retryOnFailure: true,
  maxRetries: 2,
  timeout: 300000, // 5 minutes
};

export function useExportWithErrorHandling() {
  const projectStore = useProjectStore();
  const timelineStore = useTimelineStore();
  const { reportError } = useErrorHandling();
  const [exporting, setExporting] = useState<Record<string, boolean>>({});

  const validateExportSettings = useCallback(
    (settings: ExportSettings): string | null => {
      if (!settings.format) {
        return "Export format is required";
      }

      if (!settings.quality) {
        return "Quality setting is required";
      }

      if (!settings.resolution) {
        return "Resolution is required";
      }

      if (settings.fps <= 0 || settings.fps > 120) {
        return "FPS must be between 1 and 120";
      }

      if (settings.bitrate && settings.bitrate <= 0) {
        return "Bitrate must be greater than 0";
      }

      return null;
    },
    []
  );

  const validateProject = useCallback((): string | null => {
    const project = projectStore.currentProject;
    if (!project) {
      return "No project selected for export";
    }

    if (timelineStore.tracks.length === 0) {
      return "Project has no tracks to export";
    }

    const hasClips = timelineStore.tracks.some(
      (track) => track.clips.length > 0
    );
    if (!hasClips) {
      return "Project has no clips to export";
    }

    if (timelineStore.duration <= 0) {
      return "Project duration must be greater than 0";
    }

    return null;
  }, [projectStore.currentProject, timelineStore]);

  const exportVideo = useCallback(
    async (
      settings: ExportSettings,
      options: ExportOptions = {}
    ): Promise<ExportResult> => {
      const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
      const exportKey = `export-${Date.now()}`;

      if (exporting[exportKey]) {
        return { success: false, error: "Export already in progress" };
      }

      setExporting((prev) => ({ ...prev, [exportKey]: true }));

      try {
        // Validate settings
        const settingsError = validateExportSettings(settings);
        if (settingsError) {
          throw new Error(settingsError);
        }

        // Validate project
        const projectError = validateProject();
        if (projectError) {
          throw new Error(projectError);
        }

        // Create progress toast
        const progressToast = createProgressToast(
          "Exporting Video",
          "Preparing export..."
        );

        // Start loading indicator
        const loadingId = loading.start("export", "Exporting video...");

        let retryCount = 0;
        let lastError: Error | null = null;

        while (retryCount <= (mergedOptions.maxRetries || 0)) {
          try {
            progressToast.update(
              retryCount === 0 ? 5 : 5 + retryCount * 10,
              retryCount === 0
                ? "Starting export..."
                : `Retrying export... (${retryCount}/${mergedOptions.maxRetries})`
            );

            const result = await performExport(settings, {
              timeout: mergedOptions.timeout,
              onProgress: (progress, stage) => {
                progressToast.update(progress, stage);
                loading.update(loadingId, { progress });
              },
            });

            progressToast.success(
              "Export Complete",
              "Your video is ready for download!"
            );
            loading.stop(loadingId);

            // Show download toast
            toast.success(
              "Export Successful",
              "Your video has been exported successfully",
              {
                label: "Download",
                action: () => {
                  const link = document.createElement("a");
                  link.href = result.downloadUrl;
                  link.download = `${
                    projectStore.currentProject?.name || "video"
                  }.${settings.format}`;
                  link.click();
                },
              }
            );

            return { success: true, downloadUrl: result.downloadUrl };
          } catch (error) {
            lastError = error as Error;
            retryCount++;

            // Check if error is retryable
            const isRetryable = isRetryableExportError(lastError);

            if (!isRetryable || retryCount > (mergedOptions.maxRetries || 0)) {
              break;
            }

            // Wait before retry
            await new Promise((resolve) =>
              setTimeout(resolve, 2000 * retryCount)
            );
          }
        }

        // Handle final error
        if (lastError) {
          reportError(
            ErrorHandler.handleExportError(
              lastError,
              settings as unknown as Record<string, unknown>
            )
          );

          let errorMessage = lastError.message;
          let retryable = isRetryableExportError(lastError);

          if (lastError.message.includes("memory")) {
            errorMessage =
              "Insufficient memory for export. Try reducing quality or resolution.";
            retryable = false;
          } else if (lastError.message.includes("codec")) {
            errorMessage = "Codec error. Try a different format.";
            retryable = false;
          } else if (lastError.message.includes("storage")) {
            errorMessage = "Storage error during export. Please try again.";
            retryable = true;
          } else if (lastError.message.includes("timeout")) {
            errorMessage =
              "Export timed out. Try reducing quality or project length.";
            retryable = true;
          }

          progressToast.error("Export Failed", errorMessage);
          loading.stop(loadingId);

          if (retryable && mergedOptions.retryOnFailure) {
            toast.error("Export Failed", errorMessage, {
              label: "Retry",
              action: () => exportVideo(settings, options),
            });
          } else {
            toast.error("Export Failed", errorMessage);
          }

          return { success: false, error: errorMessage, retryable };
        }

        return { success: false, error: "Unknown error occurred" };
      } catch (error) {
        const err = error as Error;
        const errorId = reportError(ErrorHandler.handleExportError(err));

        toast.error("Export Error", err.message, {
          label: "Dismiss",
          action: () => toast.dismiss(errorId),
        });

        return { success: false, error: err.message };
      } finally {
        setExporting((prev) => ({ ...prev, [exportKey]: false }));
      }
    },
    [
      projectStore,
      reportError,
      exporting,
      validateExportSettings,
      validateProject,
    ]
  );

  const cancelExport = useCallback((exportId: string) => {
    // In a real implementation, this would cancel the actual export process
    setExporting((prev) => ({ ...prev, [exportId]: false }));
    loading.stopAll();
    toast.info("Export Cancelled", "The export operation has been cancelled");
  }, []);

  const isExporting = useCallback(() => {
    return Object.values(exporting).some(Boolean);
  }, [exporting]);

  const getExportEstimate = useCallback(
    (
      settings: ExportSettings
    ): {
      estimatedTime: number; // in seconds
      estimatedSize: number; // in MB
    } => {
      const duration = timelineStore.duration;
      const resolutionMultiplier = {
        "720p": 1,
        "1080p": 2.25,
        "4k": 9,
      };

      const qualityMultiplier = {
        low: 0.5,
        medium: 1,
        high: 2,
        ultra: 4,
      };

      const baseTime = duration * 0.5; // Base: 0.5 seconds per second of video
      const estimatedTime =
        baseTime *
        resolutionMultiplier[settings.resolution] *
        qualityMultiplier[settings.quality];

      const baseSizeMB = duration * 2; // Base: 2MB per second
      const estimatedSize =
        baseSizeMB *
        resolutionMultiplier[settings.resolution] *
        qualityMultiplier[settings.quality];

      return {
        estimatedTime: Math.round(estimatedTime),
        estimatedSize: Math.round(estimatedSize),
      };
    },
    [timelineStore.duration]
  );

  return {
    exportVideo,
    cancelExport,
    isExporting,
    getExportEstimate,
    validateExportSettings,
    validateProject,
  };
}

function isRetryableExportError(error: Error): boolean {
  const retryableMessages = [
    "timeout",
    "network",
    "storage",
    "temporary",
    "server error",
  ];

  return retryableMessages.some((msg) =>
    error.message.toLowerCase().includes(msg)
  );
}

// Mock export function - replace with actual implementation
async function performExport(
  settings: ExportSettings,
  options: {
    timeout?: number;
    onProgress: (progress: number, stage: string) => void;
  }
): Promise<{
  downloadUrl: string;
}> {
  return new Promise((resolve, reject) => {
    const stages = [
      "Analyzing project...",
      "Rendering video tracks...",
      "Rendering audio tracks...",
      "Applying effects...",
      "Encoding video...",
      "Finalizing export...",
    ];

    let currentStage = 0;
    let progress = 0;

    const interval = setInterval(() => {
      progress += Math.random() * 8;

      if (progress >= (currentStage + 1) * (100 / stages.length)) {
        currentStage++;
      }

      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        setTimeout(() => {
          resolve({
            downloadUrl: `blob:${
              window.location.origin
            }/${crypto.randomUUID()}`,
          });
        }, 1000);
      } else {
        options.onProgress(
          progress,
          stages[Math.min(currentStage, stages.length - 1)]
        );
      }
    }, 400);

    // Simulate failures
    if (Math.random() < 0.1) {
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error("Export server temporarily unavailable"));
      }, 5000);
    }

    // Timeout handling
    if (options.timeout) {
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error("Export timeout"));
      }, options.timeout);
    }
  });
}
