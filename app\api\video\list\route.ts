import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { eq, desc } from "drizzle-orm";

export async function GET() {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get all video generations for the current user, ordered by creation date (newest first)
    const videos = await db
      .select()
      .from(videoGenerations)
      .where(eq(videoGenerations.userId, session.user.id))
      .orderBy(desc(videoGenerations.createdAt));
    
    return NextResponse.json({ videos });
    
  } catch (error) {
    console.error("Error listing videos:", error);
    return NextResponse.json({ error: "Failed to list videos" }, { status: 500 });
  }
} 