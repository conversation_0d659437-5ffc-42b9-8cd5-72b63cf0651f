import { relations } from "drizzle-orm";
import {
  integer,
  jsonb,
  pgTable,
  primaryKey,
  real,
  text,
  timestamp,
  type AnyPgColumn,
} from "drizzle-orm/pg-core";

export const users = pgTable("users", {
  id: text("id").primaryKey().notNull(),
  name: text("name"),
  email: text("email").unique(),
  emailVerified: timestamp("email_verified", { mode: "date" }),
  image: text("image"),
  password: text("password"),
  userType: text("user_type").notNull().default("basic"),
  credits: integer("credits").notNull().default(5),
  lastCreditReset: timestamp("last_credit_reset").defaultNow().notNull(),
  resetToken: text("reset_token"),
  resetTokenExpiry: timestamp("reset_token_expiry"),
  createdBy: text("created_by").references((): AnyPgColumn => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const accounts = pgTable(
  "accounts",
  {
    userId: text("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    type: text("type").notNull(),
    provider: text("provider").notNull(),
    providerAccountId: text("provider_account_id").notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: text("token_type"),
    scope: text("scope"),
    id_token: text("id_token"),
    session_state: text("session_state"),
  },
  (table) => {
    return {
      compoundKey: primaryKey({
        columns: [table.provider, table.providerAccountId],
      }),
    };
  }
);

export const sessions = pgTable("sessions", {
  sessionToken: text("session_token").primaryKey().notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expires: timestamp("expires", { mode: "date" }).notNull(),
});

export const verificationTokens = pgTable(
  "verification_tokens",
  {
    identifier: text("identifier").notNull(),
    token: text("token").notNull(),
    expires: timestamp("expires", { mode: "date" }).notNull(),
  },
  (table) => {
    return {
      compoundKey: primaryKey({ columns: [table.identifier, table.token] }),
    };
  }
);

export const videoGenerations = pgTable("video_generations", {
  id: text("id").primaryKey().notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  prompt: text("prompt").notNull(),
  negativePrompt: text("negative_prompt"),
  resolution: text("resolution").default("720p"),
  aspectRatio: text("aspect_ratio").default("16:9"),
  seed: integer("seed"),
  videoUrl: text("video_url"),
  imageUrl: text("image_url"),
  status: text("status").notNull().default("pending"),
  requestId: text("request_id"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Video Editor Tables
export const videoProjects = pgTable("video_projects", {
  id: text("id").primaryKey().notNull(),
  name: text("name").notNull(),
  description: text("description"),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  settings: jsonb("settings").notNull(), // ProjectSettings
  timeline: jsonb("timeline").notNull(), // TimelineData
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const videoAssets = pgTable("video_assets", {
  id: text("id").primaryKey().notNull(),
  name: text("name").notNull(),
  type: text("type").notNull(), // "video" | "audio" | "image" | "ai-generated"
  url: text("url").notNull(),
  thumbnailUrl: text("thumbnail_url"),
  duration: real("duration"), // in seconds
  metadata: jsonb("metadata").notNull(), // AssetMetadata
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  projectId: text("project_id").references(() => videoProjects.id, {
    onDelete: "cascade",
  }),
  folderId: text("folder_id").references((): AnyPgColumn => videoFolders.id, {
    onDelete: "set null",
  }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const videoFolders = pgTable("video_folders", {
  id: text("id").primaryKey().notNull(),
  name: text("name").notNull(),
  parentId: text("parent_id").references((): AnyPgColumn => videoFolders.id, {
    onDelete: "cascade",
  }),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  projectId: text("project_id").references(() => videoProjects.id, {
    onDelete: "cascade",
  }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const videoProjectVersions = pgTable("video_project_versions", {
  id: text("id").primaryKey().notNull(),
  projectId: text("project_id")
    .notNull()
    .references(() => videoProjects.id, { onDelete: "cascade" }),
  versionNumber: integer("version_number").notNull(),
  name: text("name"),
  timeline: jsonb("timeline").notNull(), // TimelineData snapshot
  settings: jsonb("settings").notNull(), // ProjectSettings snapshot
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const videoExports = pgTable("video_exports", {
  id: text("id").primaryKey().notNull(),
  projectId: text("project_id")
    .notNull()
    .references(() => videoProjects.id, { onDelete: "cascade" }),
  userId: text("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  settings: jsonb("settings").notNull(), // ExportSettings
  status: text("status").notNull().default("pending"), // "pending" | "processing" | "completed" | "failed"
  progress: real("progress").default(0), // 0-100
  outputUrl: text("output_url"),
  errorMessage: text("error_message"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
});

// Relations
export const usersRelations = relations(users, ({ many, one }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  videoGenerations: many(videoGenerations),
  videoProjects: many(videoProjects),
  videoAssets: many(videoAssets),
  videoFolders: many(videoFolders),
  videoExports: many(videoExports),
  createdUsers: many(users, { relationName: "createdUsers" }),
  creator: one(users, {
    fields: [users.createdBy],
    references: [users.id],
    relationName: "createdUsers",
  }),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const videoGenerationsRelations = relations(
  videoGenerations,
  ({ one }) => ({
    user: one(users, {
      fields: [videoGenerations.userId],
      references: [users.id],
    }),
  })
);

export const videoProjectsRelations = relations(
  videoProjects,
  ({ one, many }) => ({
    user: one(users, {
      fields: [videoProjects.userId],
      references: [users.id],
    }),
    assets: many(videoAssets),
    folders: many(videoFolders),
    versions: many(videoProjectVersions),
    exports: many(videoExports),
  })
);

export const videoAssetsRelations = relations(videoAssets, ({ one }) => ({
  user: one(users, {
    fields: [videoAssets.userId],
    references: [users.id],
  }),
  project: one(videoProjects, {
    fields: [videoAssets.projectId],
    references: [videoProjects.id],
  }),
  folder: one(videoFolders, {
    fields: [videoAssets.folderId],
    references: [videoFolders.id],
  }),
}));

export const videoFoldersRelations = relations(
  videoFolders,
  ({ one, many }) => ({
    user: one(users, {
      fields: [videoFolders.userId],
      references: [users.id],
    }),
    project: one(videoProjects, {
      fields: [videoFolders.projectId],
      references: [videoProjects.id],
    }),
    parent: one(videoFolders, {
      fields: [videoFolders.parentId],
      references: [videoFolders.id],
      relationName: "folderHierarchy",
    }),
    children: many(videoFolders, { relationName: "folderHierarchy" }),
    assets: many(videoAssets),
  })
);

export const videoProjectVersionsRelations = relations(
  videoProjectVersions,
  ({ one }) => ({
    project: one(videoProjects, {
      fields: [videoProjectVersions.projectId],
      references: [videoProjects.id],
    }),
  })
);

export const videoExportsRelations = relations(videoExports, ({ one }) => ({
  project: one(videoProjects, {
    fields: [videoExports.projectId],
    references: [videoProjects.id],
  }),
  user: one(users, {
    fields: [videoExports.userId],
    references: [users.id],
  }),
}));

// Type inference
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;

export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;

export type VerificationToken = typeof verificationTokens.$inferSelect;
export type NewVerificationToken = typeof verificationTokens.$inferInsert;

export type VideoGeneration = typeof videoGenerations.$inferSelect;
export type NewVideoGeneration = typeof videoGenerations.$inferInsert;

export type VideoProject = typeof videoProjects.$inferSelect;
export type NewVideoProject = typeof videoProjects.$inferInsert;

export type VideoAsset = typeof videoAssets.$inferSelect;
export type NewVideoAsset = typeof videoAssets.$inferInsert;

export type VideoFolder = typeof videoFolders.$inferSelect;
export type NewVideoFolder = typeof videoFolders.$inferInsert;

export type VideoProjectVersion = typeof videoProjectVersions.$inferSelect;
export type NewVideoProjectVersion = typeof videoProjectVersions.$inferInsert;

export type VideoExport = typeof videoExports.$inferSelect;
export type NewVideoExport = typeof videoExports.$inferInsert;
