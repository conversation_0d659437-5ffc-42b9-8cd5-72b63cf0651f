import { useCallback, useEffect } from "react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useTimelineClipboard } from "./use-timeline-clipboard";
import { useTimelineEditing } from "./use-timeline-editing";

interface TimelineShortcutsOptions {
  onPlay?: () => void;
  onPause?: () => void;
  onTogglePlayback?: () => void;
  onSeekToStart?: () => void;
  onSeekToEnd?: () => void;
}

export function useTimelineShortcuts(options: TimelineShortcutsOptions = {}) {
  const timelineStore = useTimelineStore();
  const clipboard = useTimelineClipboard();
  const editing = useTimelineEditing();
  // Undo/redo is handled by the useTimelineUndoRedo hook directly

  const { onTogglePlayback, onSeekToStart, onSeekToEnd } = options;

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Check if we're in an input field
      const target = event.target as HTMLElement;
      if (
        target.tagName === "INPUT" ||
        target.tagName === "TEXTAREA" ||
        target.isContentEditable
      ) {
        return;
      }

      const isCtrlOrCmd = event.ctrlKey || event.metaKey;
      const isShift = event.shiftKey;

      // Prevent default for handled shortcuts
      let preventDefault = true;

      switch (event.key) {
        // Playback controls
        case " ": // Spacebar - Play/Pause
          if (onTogglePlayback) {
            onTogglePlayback();
          } else {
            timelineStore.setIsPlaying(!timelineStore.isPlaying);
          }
          break;

        case "Home":
          if (onSeekToStart) {
            onSeekToStart();
          } else {
            timelineStore.setPlayheadPosition(0);
          }
          break;

        case "End":
          if (onSeekToEnd) {
            onSeekToEnd();
          } else {
            timelineStore.setPlayheadPosition(timelineStore.duration);
          }
          break;

        // Frame stepping
        case "ArrowLeft":
          if (isShift) {
            // Jump backward by 1 second
            const newTime = Math.max(0, timelineStore.playheadPosition - 1);
            timelineStore.setPlayheadPosition(newTime);
          } else {
            // Step backward by 1 frame (assuming 30fps)
            const frameTime = 1 / 30;
            const newTime = Math.max(
              0,
              timelineStore.playheadPosition - frameTime
            );
            timelineStore.setPlayheadPosition(newTime);
          }
          break;

        case "ArrowRight":
          if (isShift) {
            // Jump forward by 1 second
            const newTime = Math.min(
              timelineStore.duration,
              timelineStore.playheadPosition + 1
            );
            timelineStore.setPlayheadPosition(newTime);
          } else {
            // Step forward by 1 frame (assuming 30fps)
            const frameTime = 1 / 30;
            const newTime = Math.min(
              timelineStore.duration,
              timelineStore.playheadPosition + frameTime
            );
            timelineStore.setPlayheadPosition(newTime);
          }
          break;

        // Selection
        case "a":
          if (isCtrlOrCmd) {
            // Select all clips
            const allClipIds: string[] = [];
            timelineStore.tracks.forEach((track) => {
              track.clips.forEach((clip) => {
                allClipIds.push(clip.id);
              });
            });
            timelineStore.selectClips(allClipIds);
          } else {
            preventDefault = false;
          }
          break;

        case "Escape":
          // Clear selection
          timelineStore.clearSelection();
          break;

        // Clipboard operations
        case "c":
          if (isCtrlOrCmd) {
            clipboard.copyClips();
          } else {
            preventDefault = false;
          }
          break;

        case "x":
          if (isCtrlOrCmd) {
            clipboard.cutClips();
          } else {
            preventDefault = false;
          }
          break;

        case "v":
          if (isCtrlOrCmd) {
            clipboard.pasteClips();
          } else {
            preventDefault = false;
          }
          break;

        case "d":
          if (isCtrlOrCmd) {
            clipboard.duplicateClips();
          } else {
            preventDefault = false;
          }
          break;

        // Editing operations
        case "Delete":
        case "Backspace":
          editing.deleteSelectedClips();
          break;

        case "s":
          if (isCtrlOrCmd && !isShift) {
            // Split clip at playhead
            editing.splitClipAtPlayhead();
          } else {
            preventDefault = false;
          }
          break;

        // Undo/Redo (handled by useTimelineUndoRedo hook)
        case "z":
          if (isCtrlOrCmd) {
            // Handled by useTimelineUndoRedo
            preventDefault = false;
          } else {
            preventDefault = false;
          }
          break;

        case "y":
          if (isCtrlOrCmd) {
            // Handled by useTimelineUndoRedo
            preventDefault = false;
          } else {
            preventDefault = false;
          }
          break;

        // Zoom controls
        case "=":
        case "+":
          if (isCtrlOrCmd) {
            // Zoom in
            timelineStore.setZoomLevel(
              Math.min(timelineStore.zoomLevel * 1.2, 10)
            );
          } else {
            preventDefault = false;
          }
          break;

        case "-":
          if (isCtrlOrCmd) {
            // Zoom out
            timelineStore.setZoomLevel(
              Math.max(timelineStore.zoomLevel / 1.2, 0.1)
            );
          } else {
            preventDefault = false;
          }
          break;

        case "0":
          if (isCtrlOrCmd) {
            // Reset zoom
            timelineStore.setZoomLevel(1);
          } else {
            preventDefault = false;
          }
          break;

        // Track operations
        case "ArrowUp":
          if (isShift && timelineStore.selectedClips.length > 0) {
            // Move selected clips to track above
            const selectedClips = timelineStore.getSelectedClips();
            selectedClips.forEach((clip) => {
              // Find current track
              const currentTrackIndex = timelineStore.tracks.findIndex(
                (track) => track.clips.some((c) => c.id === clip.id)
              );

              if (currentTrackIndex > 0) {
                const targetTrack = timelineStore.tracks[currentTrackIndex - 1];
                timelineStore.moveClip(clip.id, targetTrack.id, clip.startTime);
              }
            });
          } else {
            preventDefault = false;
          }
          break;

        case "ArrowDown":
          if (isShift && timelineStore.selectedClips.length > 0) {
            // Move selected clips to track below
            const selectedClips = timelineStore.getSelectedClips();
            selectedClips.forEach((clip) => {
              // Find current track
              const currentTrackIndex = timelineStore.tracks.findIndex(
                (track) => track.clips.some((c) => c.id === clip.id)
              );

              if (currentTrackIndex < timelineStore.tracks.length - 1) {
                const targetTrack = timelineStore.tracks[currentTrackIndex + 1];
                timelineStore.moveClip(clip.id, targetTrack.id, clip.startTime);
              }
            });
          } else {
            preventDefault = false;
          }
          break;

        // Markers
        case "m":
          if (!isCtrlOrCmd) {
            // Add marker at playhead
            timelineStore.addMarker({
              time: timelineStore.playheadPosition,
              label: `Marker ${timelineStore.markers.length + 1}`,
              color: "#3b82f6",
            });
          } else {
            preventDefault = false;
          }
          break;

        default:
          preventDefault = false;
          break;
      }

      if (preventDefault) {
        event.preventDefault();
      }
    },
    [
      timelineStore,
      clipboard,
      editing,
      onTogglePlayback,
      onSeekToStart,
      onSeekToEnd,
    ]
  );

  // Set up keyboard event listeners
  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  // Return shortcut information for UI display
  const shortcuts = {
    playback: {
      playPause: "Space",
      seekToStart: "Home",
      seekToEnd: "End",
      frameBackward: "←",
      frameForward: "→",
      jumpBackward: "Shift + ←",
      jumpForward: "Shift + →",
    },
    selection: {
      selectAll: "Ctrl/Cmd + A",
      clearSelection: "Escape",
    },
    clipboard: {
      copy: "Ctrl/Cmd + C",
      cut: "Ctrl/Cmd + X",
      paste: "Ctrl/Cmd + V",
      duplicate: "Ctrl/Cmd + D",
    },
    editing: {
      delete: "Delete/Backspace",
      split: "Ctrl/Cmd + S",
      undo: "Ctrl/Cmd + Z",
      redo: "Ctrl/Cmd + Y or Ctrl/Cmd + Shift + Z",
    },
    zoom: {
      zoomIn: "Ctrl/Cmd + +",
      zoomOut: "Ctrl/Cmd + -",
      resetZoom: "Ctrl/Cmd + 0",
    },
    tracks: {
      moveUp: "Shift + ↑",
      moveDown: "Shift + ↓",
    },
    markers: {
      addMarker: "M",
    },
  };

  return {
    shortcuts,
  };
}
