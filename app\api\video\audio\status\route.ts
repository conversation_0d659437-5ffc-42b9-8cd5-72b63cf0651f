import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { fal } from "@fal-ai/client";

// Initialize fal.ai client with server-side credentials
fal.config({
  credentials: process.env.FAL_KEY
});

export async function GET(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get the video ID and request ID from the query parameters
    const url = new URL(req.url);
    const videoId = url.searchParams.get("videoId");
    const requestId = url.searchParams.get("requestId");
    
    console.log(`Checking audio status for videoId: ${videoId}, requestId: ${requestId}`);
    
    if (!videoId) {
      return NextResponse.json({ error: "Video ID is required" }, { status: 400 });
    }
    
    if (!requestId) {
      return NextResponse.json({ error: "Request ID is required" }, { status: 400 });
    }
    
    // Get the video from the database
    const [video] = await db
      .select()
      .from(videoGenerations)
      .where(eq(videoGenerations.id, videoId))
      .limit(1);
    
    if (!video) {
      return NextResponse.json({ error: "Video not found" }, { status: 404 });
    }
    
    // Check if the video belongs to the current user
    if (video.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    try {
      // Check the status from fal.ai
      console.log(`Fetching status from fal.ai for requestId: ${requestId}`);
      const status = await fal.queue.status("fal-ai/mmaudio-v2", {
        requestId: requestId,
      });
      
      console.log(`Audio status response:`, status);
      
      // If the generation is completed, update the database
      if (status.status === "COMPLETED") {
        console.log(`Audio generation completed, fetching result`);
        try {
          const result = await fal.queue.result("fal-ai/mmaudio-v2", {
            requestId: requestId,
          });
          
          console.log(`Audio result:`, result.data);
          
          const videoWithAudioUrl = result.data?.video?.url;
          
          if (videoWithAudioUrl) {
            console.log(`Updating video with audio URL: ${videoWithAudioUrl}`);
            // Update the video URL to the new one with audio
            await db.update(videoGenerations)
              .set({ 
                status: "completed", 
                videoUrl: videoWithAudioUrl, 
                updatedAt: new Date() 
              })
              .where(eq(videoGenerations.id, videoId));
            
            return NextResponse.json({
              id: videoId,
              status: "completed",
              videoUrl: videoWithAudioUrl,
            });
          } else {
            console.error(`No video URL in result`);
            throw new Error("No video URL in result");
          }
        } catch (resultError) {
          console.error("Error getting result:", resultError);
          await db.update(videoGenerations)
            .set({ 
              status: "failed_audio", 
              updatedAt: new Date() 
            })
            .where(eq(videoGenerations.id, videoId));
          
          return NextResponse.json({
            id: videoId,
            status: "failed_audio",
            error: "Failed to get result"
          }, { status: 200 }); // Return 200 to avoid client retries
        }
      } else if (typeof status.status === "string" && ["FAILED", "ERROR"].includes(status.status)) {
        console.error(`Audio generation failed with status: ${status.status}`);
        await db.update(videoGenerations)
          .set({ 
            status: "failed_audio", 
            updatedAt: new Date() 
          })
          .where(eq(videoGenerations.id, videoId));
        
        return NextResponse.json({
          id: videoId,
          status: "failed_audio",
          error: `Audio generation failed with status: ${status.status}`
        }, { status: 200 });
      }
      
      // If the generation is still in progress, return the status
      console.log(`Audio still processing with status: ${status.status}`);
      return NextResponse.json({
        id: videoId,
        status: "processing_audio",
        queueStatus: status.status
      });
      
    } catch (error: unknown) {
      console.error("Error checking audio status:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      return NextResponse.json({ 
        error: "Failed to check audio status",
        message: errorMessage
      }, { status: 500 });
    }
    
  } catch (error: unknown) {
    console.error("Error checking audio status:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    
    return NextResponse.json({ 
      error: "Failed to check audio status",
      message: errorMessage
    }, { status: 500 });
  }
} 