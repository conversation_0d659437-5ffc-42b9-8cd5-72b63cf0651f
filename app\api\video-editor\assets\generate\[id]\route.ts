import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { fal } from "@fal-ai/client";

// Initialize fal.ai client with server-side credentials
fal.config({
  credentials: process.env.FAL_KEY,
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get user session
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: generationId } = await params;

    if (!generationId) {
      return NextResponse.json(
        { error: "Generation ID is required" },
        { status: 400 }
      );
    }

    // Get generation record from database
    const generation = await db.query.videoGenerations.findFirst({
      where: (videoGenerations, { eq, and }) =>
        and(
          eq(videoGenerations.id, generationId),
          eq(videoGenerations.userId, session.user.id)
        ),
    });

    if (!generation) {
      return NextResponse.json(
        { error: "Generation not found" },
        { status: 404 }
      );
    }

    // If generation is already completed or failed, return the stored status
    if (generation.status === "completed" || generation.status === "failed") {
      return NextResponse.json({
        id: generation.id,
        status: generation.status,
        progress: generation.status === "completed" ? 100 : 0,
        asset:
          generation.status === "completed"
            ? {
                id: generation.id,
                name: `AI Generated: ${generation.prompt.substring(0, 50)}${
                  generation.prompt.length > 50 ? "..." : ""
                }`,
                type: "ai-generated",
                url: generation.videoUrl || "",
                duration: 8,
                createdAt: generation.createdAt,
              }
            : undefined,
        errorMessage:
          generation.status === "failed" ? "Generation failed" : undefined,
      });
    }

    // Check status with fal.ai if we have a request ID
    if (generation.requestId) {
      try {
        const result = (await fal.queue.status("fal-ai/ltx-video", {
          requestId: generation.requestId,
          logs: false,
        })) as { status: "IN_PROGRESS" | "IN_QUEUE" | "COMPLETED" | "FAILED" };

        let progress = 0;
        let status: "pending" | "generating" | "completed" | "failed" =
          (generation.status as
            | "pending"
            | "generating"
            | "completed"
            | "failed") || "pending";
        let videoUrl = generation.videoUrl;

        if (result.status === "IN_PROGRESS" || result.status === "IN_QUEUE") {
          // Estimate progress based on time elapsed
          const elapsed = Date.now() - new Date(generation.createdAt).getTime();
          const estimatedDuration = 60000; // 60 seconds estimated
          progress = Math.min(
            90,
            Math.floor((elapsed / estimatedDuration) * 100)
          );
          status = "generating";
        } else if (result.status === "COMPLETED") {
          progress = 100;
          status = "completed";

          // Get the result from fal.ai
          const completedResult = await fal.queue.result("fal-ai/ltx-video", {
            requestId: generation.requestId,
          });

          // Check if the result has a video URL (different models may have different response structures)
          let resultVideoUrl: string | null = null;
          if (completedResult.data) {
            // Try different possible response structures
            const data = completedResult.data as Record<string, unknown>;
            if (
              data.video &&
              typeof data.video === "object" &&
              data.video !== null
            ) {
              const video = data.video as Record<string, unknown>;
              if (typeof video.url === "string") {
                resultVideoUrl = video.url;
              }
            } else if (typeof data.video_url === "string") {
              resultVideoUrl = data.video_url;
            } else if (typeof data.url === "string") {
              resultVideoUrl = data.url;
            }
          }

          if (resultVideoUrl) {
            videoUrl = resultVideoUrl;

            // Update database with completed status and video URL
            await db
              .update(videoGenerations)
              .set({
                status: "completed",
                videoUrl: videoUrl,
                updatedAt: new Date(),
              })
              .where(eq(videoGenerations.id, generationId));
          }
        } else if (result.status === "FAILED") {
          status = "failed";
          progress = 0;

          // Update database with failed status
          await db
            .update(videoGenerations)
            .set({
              status: "failed",
              updatedAt: new Date(),
            })
            .where(eq(videoGenerations.id, generationId));
        }

        // Update progress in database if still in progress
        if (status === "generating" && generation.status !== "generating") {
          await db
            .update(videoGenerations)
            .set({
              status: "generating",
              updatedAt: new Date(),
            })
            .where(eq(videoGenerations.id, generationId));
        }

        return NextResponse.json({
          id: generation.id,
          status: status,
          progress: progress,
          asset:
            status === "completed"
              ? {
                  id: generation.id,
                  name: `AI Generated: ${generation.prompt.substring(0, 50)}${
                    generation.prompt.length > 50 ? "..." : ""
                  }`,
                  type: "ai-generated",
                  url: videoUrl || "",
                  duration: 8,
                  createdAt: generation.createdAt,
                  metadata: {
                    width: generation.aspectRatio === "9:16" ? 1080 : 1920,
                    height: generation.aspectRatio === "9:16" ? 1920 : 1080,
                    fps: 30,
                    fileSize: 0,
                    mimeType: "video/mp4",
                    aiPrompt: generation.prompt,
                    requestId: generation.requestId,
                  },
                }
              : undefined,
          errorMessage: status === "failed" ? "Generation failed" : undefined,
        });
      } catch (falError) {
        console.error("Error checking fal.ai status:", falError);

        // If we can't check status, return current database status
        return NextResponse.json({
          id: generation.id,
          status: generation.status,
          progress: generation.status === "generating" ? 50 : 0,
          errorMessage: "Unable to check generation status",
        });
      }
    }

    // If no request ID, return current status
    return NextResponse.json({
      id: generation.id,
      status: generation.status,
      progress: 0,
    });
  } catch (error) {
    console.error("Error checking generation progress:", error);
    return NextResponse.json(
      { error: "Failed to check generation progress" },
      { status: 500 }
    );
  }
}
