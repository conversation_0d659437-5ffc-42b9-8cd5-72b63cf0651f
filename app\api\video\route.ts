import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, videoGenerations } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { fal } from "@fal-ai/client";

// Initialize fal.ai client with server-side credentials
// When running on the server, we should use direct credentials
// The proxy is only needed for client-side requests
fal.config({
  credentials: process.env.FAL_KEY
});

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Get user details to check credits
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, session.user.id)
    });
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Check if user has enough credits
    if (user.credits <= 0) {
      return NextResponse.json(
        { 
          error: "Insufficient credits", 
          message: "You don't have enough credits to generate a video. Credits reset daily."
        }, 
        { status: 403 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { 
      prompt, 
      negative_prompt = "worst quality, inconsistent motion, blurry, jittery, distorted",
      resolution = "720p",
      aspect_ratio = "16:9"
    } = body;
    
    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }
    
    // Consume one credit
    await db.update(users)
      .set({ 
        credits: user.credits - 1,
        updatedAt: new Date()
      })
      .where(eq(users.id, session.user.id));
    
    // Generate a unique ID for this generation
    const generationId = uuidv4();
    
    // Create a record in the database
    await db.insert(videoGenerations).values({
      id: generationId,
      userId: session.user.id,
      prompt,
      resolution,
      aspectRatio: aspect_ratio,
      status: "pending",
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    try {
      // Submit request to fal.ai
      const { request_id } = await fal.queue.submit("fal-ai/ltx-video", {
        input: {
          prompt,
          negative_prompt
        },
      });
      
      // Update the database record with the request ID
      await db.update(videoGenerations)
        .set({ requestId: request_id, updatedAt: new Date() })
        .where(eq(videoGenerations.id, generationId));
      
      return NextResponse.json({ 
        id: generationId,
        requestId: request_id,
        status: "pending",
        creditsRemaining: user.credits - 1
      });
    } catch (error: unknown) {
      console.error("fal.ai API error:", error);
      
      // Refund the credit since the generation failed
      await db.update(users)
        .set({ 
          credits: user.credits,
          updatedAt: new Date()
        })
        .where(eq(users.id, session.user.id));
        
      // Update the video generation status to failed
      await db.update(videoGenerations)
        .set({ 
          status: "failed", 
          updatedAt: new Date() 
        })
        .where(eq(videoGenerations.id, generationId));
      
      const errorMessage = error instanceof Error ? error.message : "Failed to generate video with external API";
      
      return NextResponse.json({ 
        error: "API error", 
        message: errorMessage
      }, { status: 502 });
    }
    
  } catch (error: unknown) {
    console.error("Error generating video:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    
    return NextResponse.json({ 
      error: "Failed to generate video",
      message: errorMessage
    }, { status: 500 });
  }
} 