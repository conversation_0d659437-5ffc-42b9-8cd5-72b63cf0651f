import { neon } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import * as schema from "./schema";
import { eq } from "drizzle-orm";

// Create a Neon connection
const sql = neon(process.env.DATABASE_URL!);

// Create a Drizzle client with schema and prepare it for queries
export const db = drizzle(sql, { schema });

// Export schema and eq operator for convenience
export { schema, eq };
