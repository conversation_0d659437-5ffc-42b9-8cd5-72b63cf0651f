import {
  videoMergerService,
  type BatchMergeOperation,
  type CompositeVideoSettings,
  type MergeOperation,
} from "@/lib/services/video-merger";
import type { Asset, Clip } from "@/types/video-editor";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

interface VideoMergerState {
  // Active operations
  mergeOperations: MergeOperation[];
  batchOperations: BatchMergeOperation[];

  // UI state
  isLoading: boolean;
  selectedOperationId: string | null;

  // Actions - Single merge operations
  mergeClips: (
    clips: Clip[],
    assets: Asset[],
    outputSettings: MergeOperation["outputSettings"]
  ) => Promise<MergeOperation>;

  // Actions - Composite video creation
  createCompositeVideo: (
    settings: CompositeVideoSettings,
    assets: Asset[]
  ) => Promise<MergeOperation>;

  // Actions - Batch operations
  batchMergeClips: (
    mergeRequests: Array<{
      clips: Clip[];
      outputSettings: MergeOperation["outputSettings"];
    }>,
    assets: Asset[]
  ) => Promise<BatchMergeOperation>;

  // Actions - Operation management
  cancelMergeOperation: (operationId: string) => Promise<void>;
  removeMergeOperation: (operationId: string) => void;
  removeBatchOperation: (batchId: string) => void;
  clearCompletedOperations: () => void;

  // Actions - UI state
  selectOperation: (operationId: string | null) => void;
  refreshOperations: () => void;

  // Utility functions
  getMergeOperation: (id: string) => MergeOperation | undefined;
  getBatchOperation: (id: string) => BatchMergeOperation | undefined;
  getActiveOperations: () => MergeOperation[];
  getCompletedOperations: () => MergeOperation[];
  getFailedOperations: () => MergeOperation[];
  getTotalProgress: () => number;

  // Private monitoring functions
  monitorOperation: (operationId: string) => void;
  monitorBatchOperation: (batchId: string) => void;
}

export const useVideoMergerStore = create<VideoMergerState>()(
  devtools(
    (set, get) => ({
      // Initial state
      mergeOperations: [],
      batchOperations: [],
      isLoading: false,
      selectedOperationId: null,

      // Single merge operations
      mergeClips: async (clips, assets, outputSettings) => {
        set({ isLoading: true });

        try {
          const operation = await videoMergerService.mergeClips(
            clips,
            assets,
            outputSettings
          );

          set((state) => ({
            mergeOperations: [...state.mergeOperations, operation],
            isLoading: false,
          }));

          // Start monitoring this operation
          get().monitorOperation(operation.id);

          return operation;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      // Composite video creation
      createCompositeVideo: async (settings, assets) => {
        set({ isLoading: true });

        try {
          const operation = await videoMergerService.createCompositeVideo(
            settings,
            assets
          );

          set((state) => ({
            mergeOperations: [...state.mergeOperations, operation],
            isLoading: false,
          }));

          // Start monitoring this operation
          get().monitorOperation(operation.id);

          return operation;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      // Batch operations
      batchMergeClips: async (mergeRequests, assets) => {
        set({ isLoading: true });

        try {
          const batchOperation = await videoMergerService.batchMerge(
            mergeRequests,
            assets
          );

          set((state) => ({
            batchOperations: [...state.batchOperations, batchOperation],
            mergeOperations: [
              ...state.mergeOperations,
              ...batchOperation.operations,
            ],
            isLoading: false,
          }));

          // Start monitoring batch operation
          get().monitorBatchOperation(batchOperation.id);

          return batchOperation;
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      // Operation management
      cancelMergeOperation: async (operationId) => {
        try {
          await videoMergerService.cancelMergeOperation(operationId);

          set((state) => ({
            mergeOperations: state.mergeOperations.map((op) =>
              op.id === operationId
                ? { ...op, status: "failed", error: "Cancelled by user" }
                : op
            ),
          }));
        } catch (error) {
          throw error;
        }
      },

      removeMergeOperation: (operationId) =>
        set((state) => ({
          mergeOperations: state.mergeOperations.filter(
            (op) => op.id !== operationId
          ),
          selectedOperationId:
            state.selectedOperationId === operationId
              ? null
              : state.selectedOperationId,
        })),

      removeBatchOperation: (batchId) =>
        set((state) => {
          const batchOp = state.batchOperations.find((b) => b.id === batchId);
          const operationIds = batchOp?.operations.map((op) => op.id) || [];

          return {
            batchOperations: state.batchOperations.filter(
              (b) => b.id !== batchId
            ),
            mergeOperations: state.mergeOperations.filter(
              (op) => !operationIds.includes(op.id)
            ),
          };
        }),

      clearCompletedOperations: () =>
        set((state) => ({
          mergeOperations: state.mergeOperations.filter(
            (op) => op.status !== "completed"
          ),
          batchOperations: state.batchOperations.filter(
            (batch) => batch.status !== "completed"
          ),
        })),

      // UI state
      selectOperation: (operationId) =>
        set({ selectedOperationId: operationId }),

      refreshOperations: () => {
        const state = get();

        // Refresh all active operations
        state.mergeOperations.forEach((op) => {
          if (op.status === "processing" || op.status === "pending") {
            get().monitorOperation(op.id);
          }
        });

        state.batchOperations.forEach((batch) => {
          if (batch.status === "processing" || batch.status === "pending") {
            get().monitorBatchOperation(batch.id);
          }
        });
      },

      // Utility functions
      getMergeOperation: (id) => {
        const state = get();
        return state.mergeOperations.find((op) => op.id === id);
      },

      getBatchOperation: (id) => {
        const state = get();
        return state.batchOperations.find((batch) => batch.id === id);
      },

      getActiveOperations: () => {
        const state = get();
        return state.mergeOperations.filter(
          (op) => op.status === "processing" || op.status === "pending"
        );
      },

      getCompletedOperations: () => {
        const state = get();
        return state.mergeOperations.filter((op) => op.status === "completed");
      },

      getFailedOperations: () => {
        const state = get();
        return state.mergeOperations.filter((op) => op.status === "failed");
      },

      getTotalProgress: () => {
        const activeOps = get().getActiveOperations();

        if (activeOps.length === 0) return 0;

        const totalProgress = activeOps.reduce(
          (sum, op) => sum + op.progress,
          0
        );

        return totalProgress / activeOps.length;
      },

      // Private monitoring functions (these should be added to the interface)
      monitorOperation: (operationId: string) => {
        const checkStatus = () => {
          const operation = videoMergerService.getMergeOperation(operationId);

          if (operation) {
            set((state) => ({
              mergeOperations: state.mergeOperations.map((op) =>
                op.id === operationId ? operation : op
              ),
            }));

            // Continue monitoring if still active
            if (
              operation.status === "processing" ||
              operation.status === "pending"
            ) {
              setTimeout(checkStatus, 1000);
            }
          }
        };

        checkStatus();
      },

      monitorBatchOperation: (batchId: string) => {
        const checkStatus = () => {
          const batchOperation = videoMergerService.getBatchOperation(batchId);

          if (batchOperation) {
            set((state) => ({
              batchOperations: state.batchOperations.map((batch) =>
                batch.id === batchId ? batchOperation : batch
              ),
              mergeOperations: state.mergeOperations.map((op) => {
                const updatedOp = batchOperation.operations.find(
                  (batchOp) => batchOp.id === op.id
                );
                return updatedOp || op;
              }),
            }));

            // Continue monitoring if still active
            if (
              batchOperation.status === "processing" ||
              batchOperation.status === "pending"
            ) {
              setTimeout(checkStatus, 1000);
            }
          }
        };

        checkStatus();
      },
    }),
    {
      name: "video-merger-store",
    }
  )
);
