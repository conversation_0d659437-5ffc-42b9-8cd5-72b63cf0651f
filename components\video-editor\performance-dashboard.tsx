"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Activity,
  BarChart3,
  HardDrive,
  Zap,
  TrendingUp,
  AlertTriangle,
  Download,
} from "lucide-react";
import { usePerformanceService } from "@/lib/services/performance-service";
import { useMemoryManager } from "@/lib/services/memory-management-service";
import { useAnalyticsManager } from "@/lib/services/analytics-service";
import { useCacheManager } from "@/lib/services/cache-service";

export function PerformanceDashboard() {
  const [isOpen, setIsOpen] = useState(false);
  const performanceService = usePerformanceService();
  const memoryManager = useMemoryManager();
  const analytics = useAnalyticsManager();
  const cache = useCacheManager();

  const [realTimeStats, setRealTimeStats] = useState({
    fps: 0,
    memoryUsage: 0,
    cacheSize: 0,
    activeLoads: 0,
  });

  // Update real-time stats
  useEffect(() => {
    const interval = setInterval(() => {
      const memoryUsage = memoryManager.getMemoryUsage();
      const cacheStats = cache.getStats();
      const latestFps = performanceService.frameRate.slice(-1)[0] || 0;

      setRealTimeStats({
        fps: Math.round(latestFps),
        memoryUsage: memoryUsage.percentage,
        cacheSize: cacheStats.totalSize / 1024 / 1024, // MB
        activeLoads: 0, // Would get from progressive loading service
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [performanceService, memoryManager, cache]);

  const exportPerformanceData = () => {
    const data = {
      performance: performanceService.exportMetrics(),
      memory: memoryManager.getMemoryUsage(),
      cache: cache.getStats(),
      analytics: analytics.getSessionStats(),
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `performance-report-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Activity className="w-4 h-4 mr-2" />
          Performance
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Performance Dashboard
          </DialogTitle>
          <DialogDescription>
            Monitor application performance, memory usage, and system metrics.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="memory">Memory</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">FPS</CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{realTimeStats.fps}</div>
                  <p className="text-xs text-muted-foreground">
                    {realTimeStats.fps >= 30 ? "Smooth" : "Needs optimization"}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Memory</CardTitle>
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {realTimeStats.memoryUsage.toFixed(1)}%
                  </div>
                  <Progress
                    value={realTimeStats.memoryUsage}
                    className="mt-2"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Cache</CardTitle>
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {realTimeStats.cacheSize.toFixed(1)}MB
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {cache.getStats().totalEntries} entries
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Loading</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {realTimeStats.activeLoads}
                  </div>
                  <p className="text-xs text-muted-foreground">Active tasks</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <HealthIndicator
                    label="Frame Rate"
                    value={realTimeStats.fps}
                    threshold={30}
                    unit="fps"
                  />
                  <HealthIndicator
                    label="Memory Usage"
                    value={realTimeStats.memoryUsage}
                    threshold={80}
                    unit="%"
                    reverse
                  />
                  <HealthIndicator
                    label="Cache Efficiency"
                    value={cache.getStats().averageAccessCount}
                    threshold={2}
                    unit="avg hits"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Performance Metrics</h3>
              <Button
                onClick={performanceService.clearMetrics}
                variant="outline"
                size="sm"
              >
                Clear Metrics
              </Button>
            </div>

            <ScrollArea className="h-96">
              <div className="space-y-2">
                {performanceService
                  .getMetricsByCategory("timeline")
                  .map((metric) => (
                    <Card key={metric.id}>
                      <CardContent className="pt-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{metric.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {metric.category}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold">
                              {metric.duration?.toFixed(2)}ms
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(metric.startTime).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="memory" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Memory Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {memoryManager.getMemoryUsage && (
                      <>
                        <div className="flex justify-between">
                          <span>Used:</span>
                          <span>
                            {(
                              memoryManager.getMemoryUsage().used /
                              1024 /
                              1024
                            ).toFixed(1)}
                            MB
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Available:</span>
                          <span>
                            {(
                              memoryManager.getMemoryUsage().available /
                              1024 /
                              1024
                            ).toFixed(1)}
                            MB
                          </span>
                        </div>
                        <Progress
                          value={memoryManager.getMemoryUsage().percentage}
                        />
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Memory Entries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Video Frames:</span>
                      <span>{/* Would show count by type */}0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Thumbnails:</span>
                      <span>0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Audio Buffers:</span>
                      <span>0</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Memory Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  <Button
                    onClick={() => memoryManager.runGarbageCollection()}
                    variant="outline"
                    size="sm"
                  >
                    Run GC
                  </Button>
                  <Button
                    onClick={() => memoryManager.clearLowPriority()}
                    variant="outline"
                    size="sm"
                  >
                    Clear Low Priority
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Session Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Events:</span>
                      <span>{analytics.getSessionStats().totalEvents}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Session Duration:</span>
                      <span>
                        {Math.round(
                          analytics.getSessionStats().sessionDuration /
                            1000 /
                            60
                        )}
                        m
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Features Used:</span>
                      <span>
                        {analytics.getSessionStats().featuresUsed.length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Errors:</span>
                      <span>{analytics.getSessionStats().errorCount}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Features Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-1">
                    {analytics.getSessionStats().featuresUsed.map((feature) => (
                      <Badge key={feature} variant="secondary">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Export Data</CardTitle>
              </CardHeader>
              <CardContent>
                <Button onClick={exportPerformanceData} className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Export Performance Report
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

interface HealthIndicatorProps {
  label: string;
  value: number;
  threshold: number;
  unit: string;
  reverse?: boolean;
}

function HealthIndicator({
  label,
  value,
  threshold,
  unit,
  reverse = false,
}: HealthIndicatorProps) {
  const isHealthy = reverse ? value < threshold : value >= threshold;

  return (
    <div className="flex items-center justify-between">
      <span className="text-sm">{label}</span>
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">
          {value.toFixed(1)} {unit}
        </span>
        {isHealthy ? (
          <div className="w-2 h-2 bg-green-500 rounded-full" />
        ) : (
          <AlertTriangle className="w-4 h-4 text-yellow-500" />
        )}
      </div>
    </div>
  );
}
