import { RegisterForm } from "@/components/register-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import { Suspense } from "react";

export default function RegisterUnlimitedPage() {
  return (
    <div className="auth-container">
      <Card className="auth-card">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Image src="/logo.png" alt="FLOWY AI Logo" width={96} height={96} />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Create your Unlimited account
          </CardTitle>
          <CardDescription className="text-gray-600">
            Start creating professional videos with AI in minutes - Unlimited
            Plan
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Suspense
            fallback={<div className="text-center">Loading form...</div>}
          >
            <RegisterForm userType="unlimited" />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
