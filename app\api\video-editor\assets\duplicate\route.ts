import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetId } = body;

    // In a real implementation, you would:
    // 1. Find the original asset
    // 2. Copy the file
    // 3. Create a new asset record

    // For now, create a mock duplicated asset
    const duplicatedAsset = {
      id: `asset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: `Copy of Asset ${assetId}`,
      type: "video",
      url: `blob:${Date.now()}-copy.mp4`,
      thumbnailUrl: null,
      duration: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      projectId: null,
      folderId: null,
      metadata: {
        fileSize: 1024000,
        width: 1920,
        height: 1080,
        format: "mp4",
        originalName: `copy-${assetId}.mp4`,
      },
    };

    return NextResponse.json(duplicatedAsset);
  } catch (error) {
    console.error("Error duplicating asset:", error);
    return NextResponse.json(
      { error: "Failed to duplicate asset" },
      { status: 500 }
    );
  }
}
