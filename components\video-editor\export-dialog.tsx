"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  Settings,
  Zap,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import type { ExportSettings } from "@/types/video-editor";
import { useProjectStore } from "@/lib/stores/project-store";

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId?: string;
}

// Export presets for common formats
const EXPORT_PRESETS = {
  "youtube-1080p": {
    name: "YouTube 1080p",
    description: "Optimized for YouTube uploads",
    format: "mp4" as const,
    quality: "high" as const,
    resolution: { width: 1920, height: 1080 },
    fps: 30,
    bitrate: 8000,
  },
  "youtube-4k": {
    name: "YouTube 4K",
    description: "Ultra HD for YouTube",
    format: "mp4" as const,
    quality: "ultra" as const,
    resolution: { width: 3840, height: 2160 },
    fps: 30,
    bitrate: 35000,
  },
  "instagram-story": {
    name: "Instagram Story",
    description: "Vertical format for Instagram Stories",
    format: "mp4" as const,
    quality: "high" as const,
    resolution: { width: 1080, height: 1920 },
    fps: 30,
    bitrate: 6000,
  },
  tiktok: {
    name: "TikTok",
    description: "Optimized for TikTok uploads",
    format: "mp4" as const,
    quality: "high" as const,
    resolution: { width: 1080, height: 1920 },
    fps: 30,
    bitrate: 5000,
  },
  "web-optimized": {
    name: "Web Optimized",
    description: "Small file size for web",
    format: "mp4" as const,
    quality: "medium" as const,
    resolution: { width: 1280, height: 720 },
    fps: 30,
    bitrate: 2500,
  },
  "high-quality": {
    name: "High Quality",
    description: "Maximum quality for archival",
    format: "mov" as const,
    quality: "ultra" as const,
    resolution: { width: 1920, height: 1080 },
    fps: 60,
    bitrate: 50000,
  },
};

const QUALITY_DESCRIPTIONS = {
  low: "Smaller file size, lower quality",
  medium: "Balanced quality and file size",
  high: "High quality, larger file size",
  ultra: "Maximum quality, largest file size",
};

export function ExportDialog({ open, onOpenChange }: ExportDialogProps) {
  const projectStore = useProjectStore();
  const [selectedPreset, setSelectedPreset] = useState<string>("youtube-1080p");
  const [customSettings, setCustomSettings] = useState<ExportSettings>({
    format: "mp4",
    quality: "high",
    resolution: { width: 1920, height: 1080 },
    fps: 30,
    bitrate: 8000,
  });
  const [usePreset, setUsePreset] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState<
    "idle" | "exporting" | "completed" | "failed"
  >("idle");
  const [exportJobId, setExportJobId] = useState<string | null>(null);
  const [exportUrl, setExportUrl] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Initialize settings from current project
  useEffect(() => {
    if (projectStore.currentProject) {
      const projectSettings = projectStore.currentProject.settings;
      setCustomSettings({
        format: "mp4",
        quality: "high",
        resolution: {
          width: projectSettings.width,
          height: projectSettings.height,
        },
        fps: projectSettings.fps,
        bitrate: 8000,
      });
    }
  }, [projectStore.currentProject]);

  const getCurrentSettings = (): ExportSettings => {
    if (usePreset) {
      return EXPORT_PRESETS[selectedPreset as keyof typeof EXPORT_PRESETS];
    }
    return customSettings;
  };

  const handleExport = async () => {
    if (!projectStore.currentProject) {
      setErrorMessage("No project selected");
      return;
    }

    setIsExporting(true);
    setExportStatus("exporting");
    setExportProgress(0);
    setErrorMessage(null);

    try {
      const settings = getCurrentSettings();

      // Start export job
      const response = await fetch("/api/video-editor/export", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          projectId: projectStore.currentProject.id,
          settings,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to start export");
      }

      const { exportId } = await response.json();
      setExportJobId(exportId);

      // Poll for progress
      const pollProgress = async () => {
        try {
          const statusResponse = await fetch(
            `/api/video-editor/export/${exportId}`
          );
          if (!statusResponse.ok) {
            throw new Error("Failed to get export status");
          }

          const status = await statusResponse.json();
          setExportProgress(status.progress);

          if (status.status === "completed") {
            setExportStatus("completed");
            setExportUrl(status.outputUrl);
            setIsExporting(false);
          } else if (status.status === "failed") {
            setExportStatus("failed");
            setErrorMessage(status.errorMessage || "Export failed");
            setIsExporting(false);
          } else {
            // Continue polling
            setTimeout(pollProgress, 1000);
          }
        } catch (error) {
          console.error("Error polling export status:", error);
          setExportStatus("failed");
          setErrorMessage("Failed to get export status");
          setIsExporting(false);
        }
      };

      // Start polling
      setTimeout(pollProgress, 1000);
    } catch (error) {
      console.error("Export error:", error);
      setExportStatus("failed");
      setErrorMessage(error instanceof Error ? error.message : "Export failed");
      setIsExporting(false);
    }
  };

  const handleDownload = () => {
    if (exportUrl) {
      const link = document.createElement("a");
      link.href = exportUrl;
      link.download = `${projectStore.currentProject?.name || "video"}-export.${
        getCurrentSettings().format
      }`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleCancel = async () => {
    if (exportJobId && isExporting) {
      try {
        await fetch(`/api/video-editor/cancel-operation/${exportJobId}`, {
          method: "POST",
        });
      } catch (error) {
        console.error("Failed to cancel export:", error);
      }
    }

    setIsExporting(false);
    setExportStatus("idle");
    setExportProgress(0);
    setExportJobId(null);
    setExportUrl(null);
    setErrorMessage(null);
  };

  const resetDialog = () => {
    handleCancel();
    setSelectedPreset("youtube-1080p");
    setUsePreset(true);
  };

  const handleClose = () => {
    if (!isExporting) {
      resetDialog();
      onOpenChange(false);
    }
  };

  const currentSettings = getCurrentSettings();
  const estimatedFileSize = Math.round(
    ((currentSettings.bitrate || 8000) *
      (projectStore.currentProject?.timeline.duration || 30)) /
      8 /
      1024
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export Video
          </DialogTitle>
          <DialogDescription>
            Choose your export settings and download your finished video.
          </DialogDescription>
        </DialogHeader>

        {exportStatus === "idle" && (
          <Tabs
            value={usePreset ? "presets" : "custom"}
            onValueChange={(value) => setUsePreset(value === "presets")}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="presets" className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Presets
              </TabsTrigger>
              <TabsTrigger value="custom" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Custom
              </TabsTrigger>
            </TabsList>

            <TabsContent value="presets" className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                {Object.entries(EXPORT_PRESETS).map(([key, preset]) => (
                  <div
                    key={key}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedPreset === key
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                    }`}
                    onClick={() => setSelectedPreset(key)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{preset.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {preset.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline">
                          {preset.resolution.width}x{preset.resolution.height}
                        </Badge>
                        <p className="text-xs text-gray-500 mt-1">
                          {preset.fps}fps • {preset.format.toUpperCase()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="format">Format</Label>
                  <Select
                    value={customSettings.format}
                    onValueChange={(value: "mp4" | "webm" | "mov") =>
                      setCustomSettings({ ...customSettings, format: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mp4">MP4 (Recommended)</SelectItem>
                      <SelectItem value="webm">WebM</SelectItem>
                      <SelectItem value="mov">MOV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quality">Quality</Label>
                  <Select
                    value={customSettings.quality}
                    onValueChange={(
                      value: "low" | "medium" | "high" | "ultra"
                    ) =>
                      setCustomSettings({ ...customSettings, quality: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="ultra">Ultra</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    {QUALITY_DESCRIPTIONS[customSettings.quality]}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="width">Width</Label>
                  <Input
                    id="width"
                    type="number"
                    value={customSettings.resolution.width}
                    onChange={(e) =>
                      setCustomSettings({
                        ...customSettings,
                        resolution: {
                          ...customSettings.resolution,
                          width: parseInt(e.target.value) || 1920,
                        },
                      })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="height">Height</Label>
                  <Input
                    id="height"
                    type="number"
                    value={customSettings.resolution.height}
                    onChange={(e) =>
                      setCustomSettings({
                        ...customSettings,
                        resolution: {
                          ...customSettings.resolution,
                          height: parseInt(e.target.value) || 1080,
                        },
                      })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fps">FPS</Label>
                  <Input
                    id="fps"
                    type="number"
                    value={customSettings.fps}
                    onChange={(e) =>
                      setCustomSettings({
                        ...customSettings,
                        fps: parseInt(e.target.value) || 30,
                      })
                    }
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bitrate">Bitrate (kbps)</Label>
                <Input
                  id="bitrate"
                  type="number"
                  value={customSettings.bitrate || 8000}
                  onChange={(e) =>
                    setCustomSettings({
                      ...customSettings,
                      bitrate: parseInt(e.target.value) || 8000,
                    })
                  }
                />
              </div>
            </TabsContent>
          </Tabs>
        )}

        {exportStatus === "exporting" && (
          <div className="space-y-4">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-medium">Exporting Video...</h3>
              <p className="text-gray-600 dark:text-gray-400">
                This may take a few minutes depending on your video length and
                quality settings.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
          </div>
        )}

        {exportStatus === "completed" && (
          <div className="text-center space-y-4">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
            <div>
              <h3 className="text-lg font-medium">Export Complete!</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Your video has been successfully exported.
              </p>
            </div>
            <Button onClick={handleDownload} className="w-full">
              <Download className="w-4 h-4 mr-2" />
              Download Video
            </Button>
          </div>
        )}

        {exportStatus === "failed" && (
          <div className="text-center space-y-4">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto" />
            <div>
              <h3 className="text-lg font-medium">Export Failed</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {errorMessage || "An error occurred during export."}
              </p>
            </div>
            <Button onClick={() => setExportStatus("idle")} variant="outline">
              Try Again
            </Button>
          </div>
        )}

        {exportStatus === "idle" && (
          <>
            <Separator />

            <div className="space-y-3">
              <h4 className="font-medium">Export Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Format:
                  </span>
                  <span className="ml-2 font-medium">
                    {currentSettings.format.toUpperCase()}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Quality:
                  </span>
                  <span className="ml-2 font-medium capitalize">
                    {currentSettings.quality}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Resolution:
                  </span>
                  <span className="ml-2 font-medium">
                    {currentSettings.resolution.width}x
                    {currentSettings.resolution.height}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Frame Rate:
                  </span>
                  <span className="ml-2 font-medium">
                    {currentSettings.fps} fps
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Bitrate:
                  </span>
                  <span className="ml-2 font-medium">
                    {currentSettings.bitrate} kbps
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Est. File Size:
                  </span>
                  <span className="ml-2 font-medium">
                    {estimatedFileSize} MB
                  </span>
                </div>
              </div>
            </div>
          </>
        )}

        <DialogFooter>
          {exportStatus === "idle" && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleExport}
                disabled={!projectStore.currentProject}
              >
                <Download className="w-4 h-4 mr-2" />
                Start Export
              </Button>
            </>
          )}

          {exportStatus === "exporting" && (
            <Button variant="outline" onClick={handleCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel Export
            </Button>
          )}

          {(exportStatus === "completed" || exportStatus === "failed") && (
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
