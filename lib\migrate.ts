import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import { resolve } from 'path';

// For migrations
const migrationClient = postgres(process.env.DATABASE_URL!, { max: 1 });

async function runMigrations() {
  const db = drizzle(migrationClient);
  
  console.log('Running migrations...');
  
  await migrate(db, {
    migrationsFolder: resolve('./drizzle'),
  });
  
  console.log('Migrations completed!');
  
  await migrationClient.end();
  process.exit(0);
}

runMigrations().catch((err) => {
  console.error('Migration failed:', err);
  process.exit(1);
}); 