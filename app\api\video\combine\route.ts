import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";
import path from "path";
import os from "os";
import { exec } from "child_process";
import { promisify } from "util";

// Promisify exec for easier use with async/await
const execAsync = promisify(exec);

// Temporary directory for processing videos
const TMP_DIR = path.join(os.tmpdir(), 'veo-video-processing');

// Ensure temp directory exists
if (!fs.existsSync(TMP_DIR)) {
  fs.mkdirSync(TMP_DIR, { recursive: true });
}

// Check if FFmpeg is installed
async function isFFmpegInstalled() {
  try {
    await execAsync('ffmpeg -version');
    return true;
  } catch (error) {
    console.error("FFmpeg is not installed:", error);
    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse the form data
    const formData = await req.formData();
    const clipCount = parseInt(formData.get('clipCount') as string) || 0;
    const audioFile = formData.get('audio');
    
    if (clipCount === 0) {
      return NextResponse.json({ error: "No clips provided" }, { status: 400 });
    }
    
    // Generate a unique ID for this render
    const renderId = uuidv4();
    
    // Check if FFmpeg is installed
    const ffmpegAvailable = await isFFmpegInstalled();
    
    if (!ffmpegAvailable) {
      console.log("FFmpeg is not installed. Returning the first clip as fallback.");
      
      // If FFmpeg is not available, just return the first clip as a fallback
      const firstClip = formData.get('clip_0');
      
      if (!firstClip || !(firstClip instanceof Blob)) {
        return NextResponse.json({ error: "Invalid first clip" }, { status: 400 });
      }
      
      const clipBuffer = Buffer.from(await firstClip.arrayBuffer());
      
      return new NextResponse(clipBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="combined-video-${renderId}.mp4"`,
        },
      });
    }
    
    // Temporary file paths
    const clipPaths: string[] = [];
    const trimmedClipPaths: string[] = [];
    const fileListPath = path.join(TMP_DIR, `filelist-${renderId}.txt`);
    const outputVideoPath = path.join(TMP_DIR, `output-${renderId}.mp4`);
    const finalOutputPath = path.join(TMP_DIR, `final-${renderId}.mp4`);
    const tempAudioPath = audioFile ? path.join(TMP_DIR, `audio-${renderId}.mp3`) : null;
    
    try {
      // Process each clip
      for (let i = 0; i < clipCount; i++) {
        const clipFile = formData.get(`clip_${i}`);
        const startTime = parseFloat(formData.get(`clip_${i}_start`) as string) || 0;
        const endTime = parseFloat(formData.get(`clip_${i}_end`) as string) || 0;
        
        if (!clipFile || !(clipFile instanceof Blob)) {
          throw new Error(`Missing clip file for index ${i}`);
        }
        
        // Write the clip file to disk
        const clipPath = path.join(TMP_DIR, `clip-${renderId}-${i}.mp4`);
        const clipBuffer = Buffer.from(await clipFile.arrayBuffer());
        fs.writeFileSync(clipPath, clipBuffer);
        clipPaths.push(clipPath);
        
        // Trim the clip
        const trimmedClipPath = path.join(TMP_DIR, `trimmed-clip-${renderId}-${i}.mp4`);
        const trimCommand = `ffmpeg -i "${clipPath}" -ss ${startTime} -to ${endTime} -vf "scale=trunc(iw)/2*2:trunc(ih)/2*2" -c:v libx264 -preset fast -crf 22 "${trimmedClipPath}"`;
        
        console.log(`Executing trim command: ${trimCommand}`);
        await execAsync(trimCommand);
        
        if (!fs.existsSync(trimmedClipPath)) {
          throw new Error(`Failed to trim clip ${i}`);
        }
        
        trimmedClipPaths.push(trimmedClipPath);
      }
      
      // Create a file list for concatenation
      const fileListContent = trimmedClipPaths.map(p => `file '${p}'`).join('\n');
      fs.writeFileSync(fileListPath, fileListContent);
      
      // Concatenate the clips
      const concatCommand = `ffmpeg -f concat -safe 0 -i "${fileListPath}" -vf "scale=trunc(iw)/2*2:trunc(ih)/2*2" -c:v libx264 -preset fast -crf 22 -c:a aac "${outputVideoPath}"`;
      console.log(`Executing concat command: ${concatCommand}`);
      await execAsync(concatCommand);
      
      if (!fs.existsSync(outputVideoPath)) {
        throw new Error("Failed to concatenate clips");
      }
      
      // Add audio if provided
      let finalVideoPath = outputVideoPath;
      
      if (audioFile && audioFile instanceof Blob) {
        // Write the audio file to disk
        const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
        fs.writeFileSync(tempAudioPath!, audioBuffer);
        
        // Add audio to the video
        const audioCommand = `ffmpeg -i "${outputVideoPath}" -i "${tempAudioPath}" -map 0:v -map 1:a -vf "scale=trunc(iw)/2*2:trunc(ih)/2*2" -c:v libx264 -preset fast -crf 22 -c:a aac -shortest "${finalOutputPath}"`;
        console.log(`Executing audio command: ${audioCommand}`);
        await execAsync(audioCommand);
        
        if (!fs.existsSync(finalOutputPath)) {
          throw new Error("Failed to add audio to video");
        }
        
        finalVideoPath = finalOutputPath;
      }
      
      // Read the processed video
      const processedVideo = fs.readFileSync(finalVideoPath);
      
      // Clean up temporary files
      clipPaths.forEach(p => fs.existsSync(p) && fs.unlinkSync(p));
      trimmedClipPaths.forEach(p => fs.existsSync(p) && fs.unlinkSync(p));
      if (fs.existsSync(fileListPath)) fs.unlinkSync(fileListPath);
      if (fs.existsSync(outputVideoPath)) fs.unlinkSync(outputVideoPath);
      if (fs.existsSync(finalOutputPath)) fs.unlinkSync(finalOutputPath);
      if (tempAudioPath && fs.existsSync(tempAudioPath)) fs.unlinkSync(tempAudioPath);
      
      // Return the processed video as a downloadable file
      return new NextResponse(processedVideo, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="combined-video-${renderId}.mp4"`,
        },
      });
      
    } catch (ffmpegError) {
      console.error("FFmpeg error:", ffmpegError);
      
      // Clean up temporary files on error
      clipPaths.forEach(p => fs.existsSync(p) && fs.unlinkSync(p));
      trimmedClipPaths.forEach(p => fs.existsSync(p) && fs.unlinkSync(p));
      if (fs.existsSync(fileListPath)) fs.unlinkSync(fileListPath);
      if (fs.existsSync(outputVideoPath)) fs.unlinkSync(outputVideoPath);
      if (fs.existsSync(finalOutputPath)) fs.unlinkSync(finalOutputPath);
      if (tempAudioPath && fs.existsSync(tempAudioPath)) fs.unlinkSync(tempAudioPath);
      
      console.log("FFmpeg processing failed. Returning the first clip as fallback.");
      
      // If FFmpeg processing fails, return the first clip as a fallback
      const firstClip = formData.get('clip_0');
      
      if (!firstClip || !(firstClip instanceof Blob)) {
        throw new Error("Invalid first clip");
      }
      
      const clipBuffer = Buffer.from(await firstClip.arrayBuffer());
      
      return new NextResponse(clipBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="combined-video-${renderId}.mp4"`,
        },
      });
    }
    
  } catch (error) {
    console.error("Error processing video:", error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to process video" 
    }, { status: 500 });
  }
} 