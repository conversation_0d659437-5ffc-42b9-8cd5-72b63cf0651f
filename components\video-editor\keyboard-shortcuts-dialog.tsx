"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, RotateCcw, Save, AlertTriangle, Keyboard } from "lucide-react";
import {
  useKeyboardShortcuts,
  formatShortcut,
  normalizeKey,
} from "@/lib/services/keyboard-shortcut-service";

interface KeyboardShortcutsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function KeyboardShortcutsDialog({
  open,
  onOpenChange,
}: KeyboardShortcutsDialogProps) {
  const {
    shortcuts,
    categories,
    isEnabled,
    updateShortcutKeys,
    toggleShortcut,
    resetShortcut,
    resetAllShortcuts,
    setEnabled,
    getShortcutsByCategory,
  } = useKeyboardShortcuts();

  const [searchQuery, setSearchQuery] = useState("");
  const [editingShortcut, setEditingShortcut] = useState<string | null>(null);
  const [newKeys, setNewKeys] = useState<string[]>([]);
  const [recordingKeys, setRecordingKeys] = useState(false);
  const [conflictWarning, setConflictWarning] = useState<string | null>(null);

  // Filter shortcuts based on search
  const filteredShortcuts = Object.values(shortcuts).filter(
    (shortcut) =>
      shortcut.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      formatShortcut(shortcut.keys)
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
  );

  // Group shortcuts by category
  const shortcutsByCategory = Object.keys(categories).reduce(
    (acc, categoryId) => {
      const categoryShortcuts = getShortcutsByCategory(categoryId).filter(
        (shortcut) =>
          !searchQuery ||
          shortcut.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          shortcut.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          formatShortcut(shortcut.keys)
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
      );

      if (categoryShortcuts.length > 0) {
        acc[categoryId] = categoryShortcuts;
      }

      return acc;
    },
    {} as Record<string, (typeof shortcuts)[string][]>
  );

  // Handle key recording
  useEffect(() => {
    if (!recordingKeys) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      event.preventDefault();
      event.stopPropagation();

      const key = normalizeKey(event.key);
      const modifiers: string[] = [];

      if (event.ctrlKey) modifiers.push("Ctrl");
      if (event.metaKey) modifiers.push("Cmd");
      if (event.shiftKey) modifiers.push("Shift");
      if (event.altKey) modifiers.push("Alt");

      // Don't record modifier keys alone
      if (["Ctrl", "Cmd", "Shift", "Alt"].includes(key)) {
        return;
      }

      const keys = [...modifiers, key];
      setNewKeys(keys);

      // Check for conflicts
      const existingShortcut = Object.values(shortcuts).find(
        (shortcut) =>
          shortcut.id !== editingShortcut &&
          formatShortcut(shortcut.keys) === formatShortcut(keys)
      );

      if (existingShortcut) {
        setConflictWarning(
          `This shortcut is already used by "${existingShortcut.name}"`
        );
      } else {
        setConflictWarning(null);
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      // Stop recording on Escape
      if (event.key === "Escape") {
        setRecordingKeys(false);
        setNewKeys([]);
        setConflictWarning(null);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("keyup", handleKeyUp);
    };
  }, [recordingKeys, editingShortcut, shortcuts]);

  const startEditing = (shortcutId: string) => {
    setEditingShortcut(shortcutId);
    setNewKeys([...shortcuts[shortcutId].keys]);
    setRecordingKeys(false);
    setConflictWarning(null);
  };

  const saveShortcut = () => {
    if (!editingShortcut || newKeys.length === 0) return;

    updateShortcutKeys(editingShortcut, newKeys);
    setEditingShortcut(null);
    setNewKeys([]);
    setConflictWarning(null);
  };

  const cancelEditing = () => {
    setEditingShortcut(null);
    setNewKeys([]);
    setRecordingKeys(false);
    setConflictWarning(null);
  };

  const startRecording = () => {
    setRecordingKeys(true);
    setNewKeys([]);
    setConflictWarning(null);
  };

  const renderShortcutItem = (shortcut: (typeof shortcuts)[string]) => {
    const isEditing = editingShortcut === shortcut.id;
    const displayKeys = isEditing ? newKeys : shortcut.keys;

    return (
      <div
        key={shortcut.id}
        className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
      >
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h4 className="font-medium text-sm">{shortcut.name}</h4>
            {!shortcut.enabled && (
              <Badge variant="secondary" className="text-xs">
                Disabled
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {shortcut.description}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <Switch
              checked={shortcut.enabled}
              onCheckedChange={() => toggleShortcut(shortcut.id)}
            />
          </div>

          {isEditing ? (
            <div className="flex items-center space-x-2">
              <div className="min-w-[120px]">
                {recordingKeys ? (
                  <div className="flex items-center space-x-2">
                    <div className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs animate-pulse">
                      Recording...
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setRecordingKeys(false)}
                    >
                      Stop
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                      onClick={startRecording}
                    >
                      {displayKeys.length > 0
                        ? formatShortcut(displayKeys)
                        : "Click to record"}
                    </Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={startRecording}
                    >
                      <Keyboard className="w-3 h-3" />
                    </Button>
                  </div>
                )}
              </div>

              {conflictWarning && (
                <div className="flex items-center space-x-1 text-amber-600 dark:text-amber-400">
                  <AlertTriangle className="w-3 h-3" />
                  <span className="text-xs">{conflictWarning}</span>
                </div>
              )}

              <div className="flex items-center space-x-1">
                <Button
                  size="sm"
                  onClick={saveShortcut}
                  disabled={newKeys.length === 0 || !!conflictWarning}
                >
                  <Save className="w-3 h-3" />
                </Button>
                <Button size="sm" variant="outline" onClick={cancelEditing}>
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="min-w-[80px] justify-center">
                {formatShortcut(shortcut.keys)}
              </Badge>
              <Button
                size="sm"
                variant="outline"
                onClick={() => startEditing(shortcut.id)}
              >
                Edit
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => resetShortcut(shortcut.id)}
              >
                <RotateCcw className="w-3 h-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="w-5 h-5" />
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Customize keyboard shortcuts for video editing operations.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Global Settings */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center space-x-3">
              <Switch checked={isEnabled} onCheckedChange={setEnabled} />
              <div>
                <Label className="font-medium">Enable Keyboard Shortcuts</Label>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Toggle all keyboard shortcuts on/off
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={resetAllShortcuts}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset All
            </Button>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search shortcuts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Shortcuts List */}
          <div className="flex-1 overflow-hidden">
            {searchQuery ? (
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  {filteredShortcuts.map(renderShortcutItem)}
                </div>
              </ScrollArea>
            ) : (
              <Tabs
                defaultValue={Object.keys(shortcutsByCategory)[0]}
                className="h-full flex flex-col"
              >
                <TabsList className="grid w-full grid-cols-6">
                  {Object.keys(shortcutsByCategory).map((categoryId) => (
                    <TabsTrigger
                      key={categoryId}
                      value={categoryId}
                      className="text-xs"
                    >
                      {categoryId}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {Object.entries(shortcutsByCategory).map(
                  ([categoryId, categoryShortcuts]) => (
                    <TabsContent
                      key={categoryId}
                      value={categoryId}
                      className="flex-1 overflow-hidden"
                    >
                      <ScrollArea className="h-full">
                        <div className="space-y-3">
                          {categoryShortcuts.map(renderShortcutItem)}
                        </div>
                      </ScrollArea>
                    </TabsContent>
                  )
                )}
              </Tabs>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
