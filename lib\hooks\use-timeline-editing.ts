import { useCallback, useState, useRef } from "react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useUndoRedoStore } from "@/lib/stores/undo-redo-store";
import type { Clip } from "@/types/video-editor";

interface TrimState {
  isActive: boolean;
  clipId: string | null;
  edge: "start" | "end" | null;
  originalClip: Clip | null;
  startX: number;
}

interface SelectionState {
  isSelecting: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

export function useTimelineEditing() {
  const timelineStore = useTimelineStore();
  const undoRedoStore = useUndoRedoStore();

  const [trimState, setTrimState] = useState<TrimState>({
    isActive: false,
    clipId: null,
    edge: null,
    originalClip: null,
    startX: 0,
  });

  const [selectionState, setSelectionState] = useState<SelectionState>({
    isSelecting: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
  });

  const pixelsPerSecondRef = useRef<number>(100);

  // Set pixels per second for calculations
  const setPixelsPerSecond = useCallback((pps: number) => {
    pixelsPerSecondRef.current = pps;
  }, []);

  // Convert pixels to time
  const pixelsToTime = useCallback((pixels: number, padding: number = 0) => {
    return Math.max(0, (pixels - padding) / pixelsPerSecondRef.current);
  }, []);

  // Convert time to pixels
  const timeToPixels = useCallback((time: number, padding: number = 0) => {
    return time * pixelsPerSecondRef.current + padding;
  }, []);

  // Start trimming operation
  const startTrim = useCallback(
    (clipId: string, edge: "start" | "end", startX: number) => {
      const clip = timelineStore.getClipById(clipId);
      if (!clip) return;

      setTrimState({
        isActive: true,
        clipId,
        edge,
        originalClip: { ...clip },
        startX,
      });
    },
    [timelineStore]
  );

  // Update trim during drag
  const updateTrim = useCallback(
    (currentX: number) => {
      if (!trimState.isActive || !trimState.clipId || !trimState.originalClip)
        return;

      const deltaX = currentX - trimState.startX;
      const deltaTime = deltaX / pixelsPerSecondRef.current;
      const clip = trimState.originalClip;

      let newStartTime = clip.startTime;
      let newEndTime = clip.endTime;
      let newTrimStart = clip.trimStart;
      let newTrimEnd = clip.trimEnd;

      if (trimState.edge === "start") {
        // Trimming the start of the clip
        newStartTime = Math.max(0, clip.startTime + deltaTime);
        newStartTime = Math.min(newStartTime, clip.endTime - 0.1); // Minimum clip duration

        // Update trim start to maintain the same content
        const trimDelta = newStartTime - clip.startTime;
        newTrimStart = Math.max(0, clip.trimStart + trimDelta);
      } else if (trimState.edge === "end") {
        // Trimming the end of the clip
        newEndTime = Math.max(clip.startTime + 0.1, clip.endTime + deltaTime);

        // Update trim end to maintain the same content
        const trimDelta = newEndTime - clip.endTime;
        newTrimEnd = Math.max(0, clip.trimEnd - trimDelta);
      }

      // Apply the trim update
      timelineStore.updateClip(trimState.clipId, {
        startTime: newStartTime,
        endTime: newEndTime,
        trimStart: newTrimStart,
        trimEnd: newTrimEnd,
      });
    },
    [trimState, timelineStore]
  );

  // End trimming operation
  const endTrim = useCallback(() => {
    if (!trimState.isActive || !trimState.clipId || !trimState.originalClip)
      return;

    const currentClip = timelineStore.getClipById(trimState.clipId);
    if (!currentClip) return;

    // Check if the clip actually changed
    const hasChanged =
      currentClip.startTime !== trimState.originalClip.startTime ||
      currentClip.endTime !== trimState.originalClip.endTime ||
      currentClip.trimStart !== trimState.originalClip.trimStart ||
      currentClip.trimEnd !== trimState.originalClip.trimEnd;

    if (hasChanged) {
      // Record the trim operation for undo
      const beforeState = {
        tracks: timelineStore.tracks.map((track) => ({
          ...track,
          clips: track.clips.map((clip) =>
            clip.id === trimState.clipId ? trimState.originalClip! : clip
          ),
        })),
        duration: timelineStore.duration,
        markers: timelineStore.markers,
      };

      const afterState = {
        tracks: timelineStore.tracks,
        duration: timelineStore.duration,
        markers: timelineStore.markers,
      };

      undoRedoStore.pushAction({
        type: "trim_clip",
        description: `Trim clip ${trimState.edge}`,
        beforeState,
        afterState,
      });
    }

    setTrimState({
      isActive: false,
      clipId: null,
      edge: null,
      originalClip: null,
      startX: 0,
    });
  }, [trimState, timelineStore, undoRedoStore]);

  // Split clip at playhead position
  const splitClipAtPlayhead = useCallback(() => {
    const selectedClips = timelineStore.getSelectedClips();
    if (selectedClips.length !== 1) return false;

    const clip = selectedClips[0];
    const playheadPosition = timelineStore.playheadPosition;

    // Check if playhead is within the clip bounds
    if (
      playheadPosition <= clip.startTime ||
      playheadPosition >= clip.endTime
    ) {
      return false;
    }

    // Record state for undo
    const beforeState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    // Perform the split
    timelineStore.splitClip(clip.id, playheadPosition);

    // Record action for undo
    const afterState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    undoRedoStore.pushAction({
      type: "split_clip",
      description: "Split clip at playhead",
      beforeState,
      afterState,
    });

    return true;
  }, [timelineStore, undoRedoStore]);

  // Delete selected clips
  const deleteSelectedClips = useCallback(() => {
    const selectedClips = timelineStore.getSelectedClips();
    if (selectedClips.length === 0) return false;

    // Record state for undo
    const beforeState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    // Delete clips
    selectedClips.forEach((clip) => {
      timelineStore.removeClip(clip.id);
    });
    timelineStore.clearSelection();

    // Record action for undo
    const afterState = {
      tracks: timelineStore.tracks,
      duration: timelineStore.duration,
      markers: timelineStore.markers,
    };

    undoRedoStore.pushAction({
      type: "delete_clips",
      description: `Delete ${selectedClips.length} clip(s)`,
      beforeState,
      afterState,
    });

    return true;
  }, [timelineStore, undoRedoStore]);

  // Start box selection
  const startBoxSelection = useCallback((startX: number, startY: number) => {
    setSelectionState({
      isSelecting: true,
      startX,
      startY,
      currentX: startX,
      currentY: startY,
    });
  }, []);

  // Update box selection
  const updateBoxSelection = useCallback(
    (currentX: number, currentY: number) => {
      if (!selectionState.isSelecting) return;

      setSelectionState((prev) => ({
        ...prev,
        currentX,
        currentY,
      }));
    },
    [selectionState.isSelecting]
  );

  // End box selection and select clips within the box
  const endBoxSelection = useCallback(
    (
      timelinePadding: number,
      trackHeight: number,
      rulerHeight: number = 32
    ) => {
      if (!selectionState.isSelecting) return;

      const { startX, startY, currentX, currentY } = selectionState;

      // Calculate selection bounds
      const left = Math.min(startX, currentX);
      const right = Math.max(startX, currentX);
      const top = Math.min(startY, currentY);
      const bottom = Math.max(startY, currentY);

      // Convert to time and track indices
      const startTime = pixelsToTime(left, timelinePadding);
      const endTime = pixelsToTime(right, timelinePadding);
      const startTrackIndex = Math.floor((top - rulerHeight) / trackHeight);
      const endTrackIndex = Math.floor((bottom - rulerHeight) / trackHeight);

      // Find clips within the selection box
      const selectedClipIds: string[] = [];

      timelineStore.tracks.forEach((track, trackIndex) => {
        if (trackIndex >= startTrackIndex && trackIndex <= endTrackIndex) {
          track.clips.forEach((clip) => {
            // Check if clip overlaps with selection time range
            if (clip.startTime < endTime && clip.endTime > startTime) {
              selectedClipIds.push(clip.id);
            }
          });
        }
      });

      // Update selection
      if (selectedClipIds.length > 0) {
        timelineStore.selectClips(selectedClipIds);
      } else {
        timelineStore.clearSelection();
      }

      setSelectionState({
        isSelecting: false,
        startX: 0,
        startY: 0,
        currentX: 0,
        currentY: 0,
      });
    },
    [selectionState, timelineStore, pixelsToTime]
  );

  // Cancel box selection
  const cancelBoxSelection = useCallback(() => {
    setSelectionState({
      isSelecting: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
    });
  }, []);

  // Get selection box style for rendering
  const getSelectionBoxStyle = useCallback(() => {
    if (!selectionState.isSelecting) return null;

    const { startX, startY, currentX, currentY } = selectionState;
    const left = Math.min(startX, currentX);
    const top = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);

    return {
      position: "absolute" as const,
      left,
      top,
      width,
      height,
      border: "1px dashed #3b82f6",
      backgroundColor: "rgba(59, 130, 246, 0.1)",
      pointerEvents: "none" as const,
      zIndex: 10,
    };
  }, [selectionState]);

  return {
    // Trimming
    trimState,
    startTrim,
    updateTrim,
    endTrim,

    // Selection
    selectionState,
    startBoxSelection,
    updateBoxSelection,
    endBoxSelection,
    cancelBoxSelection,
    getSelectionBoxStyle,

    // Editing operations
    splitClipAtPlayhead,
    deleteSelectedClips,

    // Utilities
    setPixelsPerSecond,
    pixelsToTime,
    timeToPixels,
  };
}
