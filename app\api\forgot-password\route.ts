import { NextResponse } from "next/server";
import { z } from "zod";
import { storeResetToken } from "@/lib/tokens";
import { sendPasswordResetEmail } from "@/lib/email";

// Define schema for validation
const forgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input data
    const result = forgotPasswordSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.flatten() },
        { status: 400 }
      );
    }
    
    const { email } = result.data;
    
    // Generate and store reset token
    const resetData = await storeResetToken(email);
    
    if (!resetData) {
      // Don't reveal if the email exists in the system for security reasons
      return NextResponse.json(
        { success: true, message: "If your email is in our system, you will receive a password reset link" },
        { status: 200 }
      );
    }
    
    // Send password reset email
    const { token, user } = resetData;
    const emailResult = await sendPasswordResetEmail(
      email,
      token,
      user.name || "User"
    );
    
    if (!emailResult.success) {
      console.error("Failed to send password reset email:", emailResult.error);
      return NextResponse.json(
        { error: "Failed to send password reset email" },
        { status: 500 }
      );
    }
    
    // Return success response (don't reveal if the email exists in the system)
    return NextResponse.json(
      { success: true, message: "If your email is in our system, you will receive a password reset link" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "An error occurred during the password reset request" },
      { status: 500 }
    );
  }
} 