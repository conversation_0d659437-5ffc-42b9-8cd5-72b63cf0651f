import { useEffect, useCallback } from "react";
import { useKeyboardShortcuts } from "@/lib/services/keyboard-shortcut-service";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useTimelineClipboard } from "./use-timeline-clipboard";
import { useTimelineEditing } from "./use-timeline-editing";
import { useTimelineUndoRedo } from "./use-timeline-undo-redo";

interface EnhancedShortcutsOptions {
  onPlay?: () => void;
  onPause?: () => void;
  onTogglePlayback?: () => void;
  onSeekToStart?: () => void;
  onSeekToEnd?: () => void;
  onSave?: () => void;
  onExport?: () => void;
  onImport?: () => void;
  onNewProject?: () => void;
}

export function useEnhancedShortcuts(options: EnhancedShortcutsOptions = {}) {
  const timelineStore = useTimelineStore();
  const projectStore = useProjectStore();
  const clipboard = useTimelineClipboard();
  const editing = useTimelineEditing();
  const { undo, redo } = useTimelineUndoRedo();
  const { registerShortcut } = useKeyboardShortcuts();

  const {
    onTogglePlayback,
    onSeekToStart,
    onSeekToEnd,
    onSave,
    onExport,
    onImport,
    onNewProject,
  } = options;

  // Playback shortcuts
  const registerPlaybackShortcuts = useCallback(() => {
    registerShortcut(
      {
        id: "playback.toggle",
        name: "Play/Pause",
        description: "Toggle video playback",
        category: "Playback",
        defaultKeys: ["Space"],
        keys: ["Space"],
        enabled: true,
      },
      () => {
        if (onTogglePlayback) {
          onTogglePlayback();
        } else {
          timelineStore.setIsPlaying(!timelineStore.isPlaying);
        }
      }
    );

    registerShortcut(
      {
        id: "playback.seekStart",
        name: "Seek to Start",
        description: "Jump to the beginning of the timeline",
        category: "Playback",
        defaultKeys: ["Home"],
        keys: ["Home"],
        enabled: true,
      },
      () => {
        if (onSeekToStart) {
          onSeekToStart();
        } else {
          timelineStore.setPlayheadPosition(0);
        }
      }
    );

    registerShortcut(
      {
        id: "playback.seekEnd",
        name: "Seek to End",
        description: "Jump to the end of the timeline",
        category: "Playback",
        defaultKeys: ["End"],
        keys: ["End"],
        enabled: true,
      },
      () => {
        if (onSeekToEnd) {
          onSeekToEnd();
        } else {
          timelineStore.setPlayheadPosition(timelineStore.duration);
        }
      }
    );

    registerShortcut(
      {
        id: "playback.frameBackward",
        name: "Frame Backward",
        description: "Step backward by one frame",
        category: "Playback",
        defaultKeys: ["←"],
        keys: ["←"],
        enabled: true,
      },
      () => {
        const frameTime = 1 / 30;
        const newTime = Math.max(0, timelineStore.playheadPosition - frameTime);
        timelineStore.setPlayheadPosition(newTime);
      }
    );

    registerShortcut(
      {
        id: "playback.frameForward",
        name: "Frame Forward",
        description: "Step forward by one frame",
        category: "Playback",
        defaultKeys: ["→"],
        keys: ["→"],
        enabled: true,
      },
      () => {
        const frameTime = 1 / 30;
        const newTime = Math.min(
          timelineStore.duration,
          timelineStore.playheadPosition + frameTime
        );
        timelineStore.setPlayheadPosition(newTime);
      }
    );

    registerShortcut(
      {
        id: "playback.jumpBackward",
        name: "Jump Backward",
        description: "Jump backward by 1 second",
        category: "Playback",
        defaultKeys: ["Shift", "←"],
        keys: ["Shift", "←"],
        enabled: true,
      },
      () => {
        const newTime = Math.max(0, timelineStore.playheadPosition - 1);
        timelineStore.setPlayheadPosition(newTime);
      }
    );

    registerShortcut(
      {
        id: "playback.jumpForward",
        name: "Jump Forward",
        description: "Jump forward by 1 second",
        category: "Playback",
        defaultKeys: ["Shift", "→"],
        keys: ["Shift", "→"],
        enabled: true,
      },
      () => {
        const newTime = Math.min(
          timelineStore.duration,
          timelineStore.playheadPosition + 1
        );
        timelineStore.setPlayheadPosition(newTime);
      }
    );

    registerShortcut(
      {
        id: "playback.jumpBackward5",
        name: "Jump Backward 5s",
        description: "Jump backward by 5 seconds",
        category: "Playback",
        defaultKeys: ["J"],
        keys: ["J"],
        enabled: true,
      },
      () => {
        const newTime = Math.max(0, timelineStore.playheadPosition - 5);
        timelineStore.setPlayheadPosition(newTime);
      }
    );

    registerShortcut(
      {
        id: "playback.jumpForward5",
        name: "Jump Forward 5s",
        description: "Jump forward by 5 seconds",
        category: "Playback",
        defaultKeys: ["L"],
        keys: ["L"],
        enabled: true,
      },
      () => {
        const newTime = Math.min(
          timelineStore.duration,
          timelineStore.playheadPosition + 5
        );
        timelineStore.setPlayheadPosition(newTime);
      }
    );

    registerShortcut(
      {
        id: "playback.playPause",
        name: "Play/Pause (K)",
        description: "Alternative play/pause shortcut",
        category: "Playback",
        defaultKeys: ["K"],
        keys: ["K"],
        enabled: true,
      },
      () => {
        if (onTogglePlayback) {
          onTogglePlayback();
        } else {
          timelineStore.setIsPlaying(!timelineStore.isPlaying);
        }
      }
    );
  }, [
    timelineStore,
    onTogglePlayback,
    onSeekToStart,
    onSeekToEnd,
    registerShortcut,
  ]);

  // Selection shortcuts
  const registerSelectionShortcuts = useCallback(() => {
    registerShortcut(
      {
        id: "selection.selectAll",
        name: "Select All",
        description: "Select all clips in the timeline",
        category: "Selection",
        defaultKeys: ["Ctrl", "A"],
        keys: ["Ctrl", "A"],
        enabled: true,
      },
      () => {
        const allClipIds: string[] = [];
        timelineStore.tracks.forEach((track) => {
          track.clips.forEach((clip) => {
            allClipIds.push(clip.id);
          });
        });
        timelineStore.selectClips(allClipIds);
      }
    );

    registerShortcut(
      {
        id: "selection.clearSelection",
        name: "Clear Selection",
        description: "Clear all selected clips",
        category: "Selection",
        defaultKeys: ["Esc"],
        keys: ["Esc"],
        enabled: true,
      },
      () => {
        timelineStore.clearSelection();
      }
    );

    registerShortcut(
      {
        id: "selection.selectClipsAtPlayhead",
        name: "Select Clips at Playhead",
        description: "Select all clips at the current playhead position",
        category: "Selection",
        defaultKeys: ["Shift", "C"],
        keys: ["Shift", "C"],
        enabled: true,
      },
      () => {
        const playheadTime = timelineStore.playheadPosition;
        const clipsAtPlayhead: string[] = [];

        timelineStore.tracks.forEach((track) => {
          track.clips.forEach((clip) => {
            if (
              clip.startTime <= playheadTime &&
              clip.endTime >= playheadTime
            ) {
              clipsAtPlayhead.push(clip.id);
            }
          });
        });

        timelineStore.selectClips(clipsAtPlayhead);
      }
    );
  }, [timelineStore, registerShortcut]);

  // Clipboard shortcuts
  const registerClipboardShortcuts = useCallback(() => {
    registerShortcut(
      {
        id: "clipboard.copy",
        name: "Copy",
        description: "Copy selected clips",
        category: "Clipboard",
        defaultKeys: ["Ctrl", "C"],
        keys: ["Ctrl", "C"],
        enabled: true,
      },
      () => clipboard.copyClips()
    );

    registerShortcut(
      {
        id: "clipboard.cut",
        name: "Cut",
        description: "Cut selected clips",
        category: "Clipboard",
        defaultKeys: ["Ctrl", "X"],
        keys: ["Ctrl", "X"],
        enabled: true,
      },
      () => clipboard.cutClips()
    );

    registerShortcut(
      {
        id: "clipboard.paste",
        name: "Paste",
        description: "Paste clips at playhead",
        category: "Clipboard",
        defaultKeys: ["Ctrl", "V"],
        keys: ["Ctrl", "V"],
        enabled: true,
      },
      () => clipboard.pasteClips()
    );

    registerShortcut(
      {
        id: "clipboard.duplicate",
        name: "Duplicate",
        description: "Duplicate selected clips",
        category: "Clipboard",
        defaultKeys: ["Ctrl", "D"],
        keys: ["Ctrl", "D"],
        enabled: true,
      },
      () => clipboard.duplicateClips()
    );
  }, [clipboard, registerShortcut]);

  // Editing shortcuts
  const registerEditingShortcuts = useCallback(() => {
    registerShortcut(
      {
        id: "editing.delete",
        name: "Delete",
        description: "Delete selected clips",
        category: "Editing",
        defaultKeys: ["Del"],
        keys: ["Del"],
        enabled: true,
      },
      () => editing.deleteSelectedClips()
    );

    registerShortcut(
      {
        id: "editing.deleteBackspace",
        name: "Delete (Backspace)",
        description: "Delete selected clips with backspace",
        category: "Editing",
        defaultKeys: ["Backspace"],
        keys: ["Backspace"],
        enabled: true,
      },
      () => editing.deleteSelectedClips()
    );

    registerShortcut(
      {
        id: "editing.split",
        name: "Split at Playhead",
        description: "Split selected clips at playhead position",
        category: "Editing",
        defaultKeys: ["Ctrl", "S"],
        keys: ["Ctrl", "S"],
        enabled: true,
      },
      () => editing.splitClipAtPlayhead()
    );

    registerShortcut(
      {
        id: "editing.splitBlade",
        name: "Split (Blade Tool)",
        description: "Alternative split shortcut",
        category: "Editing",
        defaultKeys: ["B"],
        keys: ["B"],
        enabled: true,
      },
      () => editing.splitClipAtPlayhead()
    );

    registerShortcut(
      {
        id: "editing.undo",
        name: "Undo",
        description: "Undo last action",
        category: "Editing",
        defaultKeys: ["Ctrl", "Z"],
        keys: ["Ctrl", "Z"],
        enabled: true,
      },
      () => undo()
    );

    registerShortcut(
      {
        id: "editing.redo",
        name: "Redo",
        description: "Redo last undone action",
        category: "Editing",
        defaultKeys: ["Ctrl", "Y"],
        keys: ["Ctrl", "Y"],
        enabled: true,
      },
      () => redo()
    );

    registerShortcut(
      {
        id: "editing.redoShift",
        name: "Redo (Shift+Z)",
        description: "Alternative redo shortcut",
        category: "Editing",
        defaultKeys: ["Ctrl", "Shift", "Z"],
        keys: ["Ctrl", "Shift", "Z"],
        enabled: true,
      },
      () => redo()
    );
  }, [editing, undo, redo, registerShortcut]);

  // Timeline navigation shortcuts
  const registerTimelineShortcuts = useCallback(() => {
    registerShortcut(
      {
        id: "timeline.zoomIn",
        name: "Zoom In",
        description: "Zoom in on timeline",
        category: "Timeline",
        defaultKeys: ["Ctrl", "+"],
        keys: ["Ctrl", "+"],
        enabled: true,
      },
      () => {
        timelineStore.setZoomLevel(Math.min(timelineStore.zoomLevel * 1.2, 10));
      }
    );

    registerShortcut(
      {
        id: "timeline.zoomOut",
        name: "Zoom Out",
        description: "Zoom out on timeline",
        category: "Timeline",
        defaultKeys: ["Ctrl", "-"],
        keys: ["Ctrl", "-"],
        enabled: true,
      },
      () => {
        timelineStore.setZoomLevel(
          Math.max(timelineStore.zoomLevel / 1.2, 0.1)
        );
      }
    );

    registerShortcut(
      {
        id: "timeline.resetZoom",
        name: "Reset Zoom",
        description: "Reset timeline zoom to 100%",
        category: "Timeline",
        defaultKeys: ["Ctrl", "0"],
        keys: ["Ctrl", "0"],
        enabled: true,
      },
      () => {
        timelineStore.setZoomLevel(1);
      }
    );

    registerShortcut(
      {
        id: "timeline.fitToWindow",
        name: "Fit to Window",
        description: "Fit entire timeline to window",
        category: "Timeline",
        defaultKeys: ["Shift", "Z"],
        keys: ["Shift", "Z"],
        enabled: true,
      },
      () => {
        // Calculate zoom level to fit entire timeline
        const timelineWidth = 1000; // Approximate timeline width
        const duration = timelineStore.duration;
        const pixelsPerSecond = timelineWidth / duration;
        const zoomLevel = pixelsPerSecond / 50; // Base pixels per second
        timelineStore.setZoomLevel(Math.max(0.1, Math.min(10, zoomLevel)));
      }
    );

    registerShortcut(
      {
        id: "timeline.addMarker",
        name: "Add Marker",
        description: "Add marker at playhead position",
        category: "Timeline",
        defaultKeys: ["M"],
        keys: ["M"],
        enabled: true,
      },
      () => {
        timelineStore.addMarker({
          time: timelineStore.playheadPosition,
          label: `Marker ${timelineStore.markers.length + 1}`,
          color: "#3b82f6",
        });
      }
    );
  }, [timelineStore, registerShortcut]);

  // Project shortcuts
  const registerProjectShortcuts = useCallback(() => {
    registerShortcut(
      {
        id: "project.save",
        name: "Save Project",
        description: "Save current project",
        category: "Project",
        defaultKeys: ["Ctrl", "S"],
        keys: ["Ctrl", "S"],
        enabled: true,
        global: true,
      },
      () => {
        if (onSave) {
          onSave();
        } else {
          projectStore.saveProject();
        }
      }
    );

    registerShortcut(
      {
        id: "project.export",
        name: "Export Video",
        description: "Export current project as video",
        category: "Project",
        defaultKeys: ["Ctrl", "E"],
        keys: ["Ctrl", "E"],
        enabled: true,
        global: true,
      },
      () => {
        if (onExport) {
          onExport();
        }
      }
    );

    registerShortcut(
      {
        id: "project.import",
        name: "Import Media",
        description: "Import media files",
        category: "Project",
        defaultKeys: ["Ctrl", "I"],
        keys: ["Ctrl", "I"],
        enabled: true,
        global: true,
      },
      () => {
        if (onImport) {
          onImport();
        }
      }
    );

    registerShortcut(
      {
        id: "project.new",
        name: "New Project",
        description: "Create new project",
        category: "Project",
        defaultKeys: ["Ctrl", "N"],
        keys: ["Ctrl", "N"],
        enabled: true,
        global: true,
      },
      () => {
        if (onNewProject) {
          onNewProject();
        }
      }
    );
  }, [
    projectStore,
    onSave,
    onExport,
    onImport,
    onNewProject,
    registerShortcut,
  ]);

  // Register all shortcuts
  useEffect(() => {
    registerPlaybackShortcuts();
    registerSelectionShortcuts();
    registerClipboardShortcuts();
    registerEditingShortcuts();
    registerTimelineShortcuts();
    registerProjectShortcuts();
  }, [
    registerPlaybackShortcuts,
    registerSelectionShortcuts,
    registerClipboardShortcuts,
    registerEditingShortcuts,
    registerTimelineShortcuts,
    registerProjectShortcuts,
  ]);

  return {
    // Expose shortcut categories for UI
    categories: [
      "Playback",
      "Selection",
      "Clipboard",
      "Editing",
      "Timeline",
      "Project",
    ],
  };
}
