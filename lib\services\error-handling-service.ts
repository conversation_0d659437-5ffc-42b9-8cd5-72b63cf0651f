import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: string;
  timestamp: Date;
  context?: Record<string, unknown>;
  recoverable: boolean;
  retryable: boolean;
  retryCount?: number;
  maxRetries?: number;
  stack?: string;
}

export type ErrorType =
  | "media_import"
  | "ai_generation"
  | "rendering"
  | "export"
  | "network"
  | "validation"
  | "permission"
  | "storage"
  | "unknown";

export type ErrorSeverity = "low" | "medium" | "high" | "critical";

export interface ErrorRecoveryAction {
  id: string;
  label: string;
  action: () => Promise<void> | void;
  primary?: boolean;
}

interface ErrorHandlingState {
  errors: AppError[];
  isRecovering: boolean;
  maxErrors: number;

  // Actions
  reportError: (error: Omit<AppError, "id" | "timestamp">) => string;
  clearError: (errorId: string) => void;
  clearAllErrors: () => void;
  retryError: (errorId: string) => Promise<boolean>;
  getErrorsByType: (type: ErrorType) => AppError[];
  getErrorsBySeverity: (severity: ErrorSeverity) => AppError[];
}

export const useErrorHandling = create<ErrorHandlingState>()(
  devtools((set, get) => ({
    errors: [],
    isRecovering: false,
    maxErrors: 50,

    reportError: (errorData) => {
      const errorId = crypto.randomUUID();
      const error: AppError = {
        ...errorData,
        id: errorId,
        timestamp: new Date(),
        retryCount: 0,
      };

      set((state) => {
        const newErrors = [...state.errors, error];

        // Limit the number of stored errors
        if (newErrors.length > state.maxErrors) {
          newErrors.shift();
        }

        return { errors: newErrors };
      });

      // Log error for debugging
      console.error(`[${error.type}] ${error.message}`, {
        details: error.details,
        context: error.context,
        stack: error.stack,
      });

      return errorId;
    },

    clearError: (errorId) => {
      set((state) => ({
        errors: state.errors.filter((error) => error.id !== errorId),
      }));
    },

    clearAllErrors: () => {
      set({ errors: [] });
    },

    retryError: async (errorId) => {
      const state = get();
      const error = state.errors.find((e) => e.id === errorId);

      if (!error || !error.retryable) {
        return false;
      }

      const maxRetries = error.maxRetries || 3;
      if ((error.retryCount || 0) >= maxRetries) {
        return false;
      }

      set({ isRecovering: true });

      try {
        // Update retry count
        set((state) => ({
          errors: state.errors.map((e) =>
            e.id === errorId ? { ...e, retryCount: (e.retryCount || 0) + 1 } : e
          ),
        }));

        // The actual retry logic would be handled by the calling component
        // This just tracks the retry attempt
        return true;
      } finally {
        set({ isRecovering: false });
      }
    },

    getErrorsByType: (type) => {
      return get().errors.filter((error) => error.type === type);
    },

    getErrorsBySeverity: (severity) => {
      return get().errors.filter((error) => error.severity === severity);
    },
  }))
);

// Error handling utilities
export class ErrorHandler {
  static createError(
    type: ErrorType,
    message: string,
    options: Partial<AppError> = {}
  ): Omit<AppError, "id" | "timestamp"> {
    return {
      type,
      message,
      severity: "medium",
      recoverable: false,
      retryable: false,
      ...options,
    };
  }

  static handleMediaImportError(
    error: Error,
    fileName?: string
  ): Omit<AppError, "id" | "timestamp"> {
    let message = "Failed to import media file";
    let recoverable = true;
    let retryable = true;

    if (fileName) {
      message += `: ${fileName}`;
    }

    // Determine specific error type and recovery options
    if (error.message.includes("format")) {
      message = "Unsupported file format";
      recoverable = false;
      retryable = false;
    } else if (error.message.includes("size")) {
      message = "File size too large";
      recoverable = false;
      retryable = false;
    } else if (error.message.includes("network")) {
      message = "Network error during upload";
      retryable = true;
    }

    return this.createError("media_import", message, {
      details: error.message,
      severity: "medium",
      recoverable,
      retryable,
      maxRetries: 3,
      context: { fileName, originalError: error.name },
      stack: error.stack,
    });
  }

  static handleAIGenerationError(
    error: Error,
    prompt?: string
  ): Omit<AppError, "id" | "timestamp"> {
    let message = "AI generation failed";
    let severity: ErrorSeverity = "medium";
    let retryable = true;

    if (error.message.includes("quota")) {
      message = "AI generation quota exceeded";
      severity = "high";
      retryable = false;
    } else if (error.message.includes("content")) {
      message = "Content policy violation";
      severity = "medium";
      retryable = false;
    } else if (error.message.includes("timeout")) {
      message = "AI generation timed out";
      retryable = true;
    }

    return this.createError("ai_generation", message, {
      details: error.message,
      severity,
      recoverable: true,
      retryable,
      maxRetries: 2,
      context: { prompt, originalError: error.name },
      stack: error.stack,
    });
  }

  static handleRenderingError(
    error: Error,
    projectId?: string
  ): Omit<AppError, "id" | "timestamp"> {
    let message = "Video rendering failed";
    let retryable = true;

    if (error.message.includes("memory")) {
      message = "Insufficient memory for rendering";
      retryable = false;
    } else if (error.message.includes("codec")) {
      message = "Codec error during rendering";
      retryable = false;
    }

    return this.createError("rendering", message, {
      details: error.message,
      severity: "high",
      recoverable: true,
      retryable,
      maxRetries: 2,
      context: { projectId, originalError: error.name },
      stack: error.stack,
    });
  }

  static handleExportError(
    error: Error,
    exportSettings?: Record<string, unknown>
  ): Omit<AppError, "id" | "timestamp"> {
    let message = "Export failed";
    let retryable = true;

    if (error.message.includes("storage")) {
      message = "Storage error during export";
      retryable = true;
    } else if (error.message.includes("format")) {
      message = "Invalid export format";
      retryable = false;
    }

    return this.createError("export", message, {
      details: error.message,
      severity: "high",
      recoverable: true,
      retryable,
      maxRetries: 3,
      context: { exportSettings, originalError: error.name },
      stack: error.stack,
    });
  }

  static handleNetworkError(
    error: Error,
    url?: string
  ): Omit<AppError, "id" | "timestamp"> {
    let message = "Network request failed";
    const retryable = true;

    if (error.message.includes("timeout")) {
      message = "Request timed out";
    } else if (error.message.includes("offline")) {
      message = "No internet connection";
    }

    return this.createError("network", message, {
      details: error.message,
      severity: "medium",
      recoverable: true,
      retryable,
      maxRetries: 3,
      context: { url, originalError: error.name },
      stack: error.stack,
    });
  }

  static handleValidationError(
    message: string,
    field?: string
  ): Omit<AppError, "id" | "timestamp"> {
    return this.createError("validation", message, {
      severity: "low",
      recoverable: true,
      retryable: false,
      context: { field },
    });
  }
}
