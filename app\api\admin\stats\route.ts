import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { count } from "drizzle-orm";

export async function GET() {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if user is admin (optional security measure)
    // if (session.user.userType !== "admin") {
    //   return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    // }
    
    // Get total videos count across all users
    const totalVideosResult = await db
      .select({ count: count() })
      .from(videoGenerations);
    
    const totalVideos = totalVideosResult[0]?.count || 0;
    
    return NextResponse.json({
      stats: {
        totalVideos,
        lastUpdated: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch admin stats" },
      { status: 500 }
    );
  }
} 