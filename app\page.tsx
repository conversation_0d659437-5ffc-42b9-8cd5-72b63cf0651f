"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  ArrowRight,
  BarChart3,
  CheckCircle,
  Globe,
  Palette,
  Play,
  Sparkles,
  Star,
  Users,
  Zap,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function LandingPage() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
        <motion.div
          className="absolute w-96 h-96 bg-purple-500/20 rounded-full blur-3xl"
          animate={{
            x: mousePosition.x / 10,
            y: mousePosition.y / 10,
          }}
          transition={{ type: "spring", stiffness: 50, damping: 30 }}
        />
        <motion.div
          className="absolute top-1/4 right-1/4 w-64 h-64 bg-blue-500/20 rounded-full blur-3xl"
          animate={{
            x: -mousePosition.x / 15,
            y: -mousePosition.y / 15,
          }}
          transition={{ type: "spring", stiffness: 30, damping: 30 }}
        />
      </div>

      {/* Navigation */}
      <motion.nav
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="relative z-50 flex items-center justify-between px-6 py-4 backdrop-blur-md bg-white/10 border-b border-white/20"
      >
        <motion.div
          className="flex items-center space-x-4"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <div className="relative">
            <Image
              src="/logo-w.png"
              alt="FLOWY AI Logo"
              width={64}
              height={64}
              className="w-20 h-14 object-contain drop-shadow-lg"
              priority
            />
          </div>
        </motion.div>

        <div className="hidden md:flex items-center space-x-8">
          <motion.a
            href="#features"
            className="text-white/80 hover:text-white font-medium transition-colors duration-200"
            whileHover={{ y: -2 }}
          >
            Features
          </motion.a>
          <motion.a
            href="#templates"
            className="text-white/80 hover:text-white font-medium transition-colors duration-200"
            whileHover={{ y: -2 }}
          >
            Templates
          </motion.a>
          <motion.a
            href="#about"
            className="text-white/80 hover:text-white font-medium transition-colors duration-200"
            whileHover={{ y: -2 }}
          >
            About
          </motion.a>
        </div>

        <div className="flex items-center space-x-4">
          <Link href="/login">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 hover:from-purple-600 hover:to-pink-600">
                Get Started
              </Button>
            </motion.div>
          </Link>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section className="relative z-10 py-32 px-6">
        <motion.div
          className="max-w-7xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div variants={itemVariants}>
            <Badge className="mb-8 bg-white/20 text-white border-white/30 backdrop-blur-sm hover:bg-white/30 transition-all duration-300">
              <Sparkles className="w-4 h-4 mr-2" />
              New: AI-Powered Video Generation
            </Badge>
          </motion.div>

          <motion.h1
            className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight"
            variants={itemVariants}
          >
            Turn Text Into{" "}
            <motion.span
              className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "linear",
              }}
            >
              Professional
            </motion.span>
            <br />
            Videos in Minutes
          </motion.h1>

          <motion.p
            className="text-xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            Create stunning AI-generated videos without any video editing
            skills. From text to video in just a few clicks with cutting-edge AI
            technology.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center mb-20"
            variants={itemVariants}
          >
            <Link href="/login">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="group"
              >
                <Button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-lg px-10 py-4 rounded-2xl border-0 hover:from-purple-600 hover:to-pink-600 shadow-2xl hover:shadow-purple-500/25 transition-all duration-300">
                  Start Creating Free
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-5 h-5" />
                  </motion.div>
                </Button>
              </motion.div>
            </Link>
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                className="bg-white/10 text-white border-white/30 backdrop-blur-sm text-lg px-10 py-4 rounded-2xl hover:bg-white/20 transition-all duration-300"
              >
                <Play className="mr-2 w-5 h-5" />
                Watch Demo
              </Button>
            </motion.div>
          </motion.div>

          {/* Animated Video Preview */}
          <motion.div className="max-w-5xl mx-auto" variants={itemVariants}>
            <motion.div
              className="relative rounded-3xl overflow-hidden shadow-2xl backdrop-blur-sm bg-white/10 border border-white/20"
              whileHover={{ scale: 1.02, y: -10 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <div className="aspect-video bg-gradient-to-br from-purple-900/50 to-pink-900/50 flex items-center justify-center relative overflow-hidden">
                {/* Animated background elements */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20"
                  animate={{
                    background: [
                      "linear-gradient(45deg, rgba(168,85,247,0.2), rgba(236,72,153,0.2))",
                      "linear-gradient(135deg, rgba(236,72,153,0.2), rgba(168,85,247,0.2))",
                      "linear-gradient(225deg, rgba(168,85,247,0.2), rgba(236,72,153,0.2))",
                      "linear-gradient(315deg, rgba(236,72,153,0.2), rgba(168,85,247,0.2))",
                    ],
                  }}
                  transition={{ duration: 4, repeat: Infinity }}
                />

                <motion.div
                  className="relative z-10"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Button
                    size="lg"
                    className="bg-white/90 text-gray-900 hover:bg-white w-20 h-20 rounded-full shadow-2xl"
                  >
                    <Play className="w-8 h-8 ml-1" />
                  </Button>
                </motion.div>

                {/* Floating elements */}
                <motion.div
                  className="absolute top-4 left-4 w-3 h-3 bg-white/60 rounded-full"
                  animate={{
                    y: [0, -10, 0],
                    opacity: [0.6, 1, 0.6],
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                <motion.div
                  className="absolute top-8 right-8 w-2 h-2 bg-purple-400/80 rounded-full"
                  animate={{
                    y: [0, -15, 0],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{ duration: 2.5, repeat: Infinity, delay: 0.5 }}
                />
                <motion.div
                  className="absolute bottom-6 left-8 w-4 h-4 bg-pink-400/70 rounded-full"
                  animate={{
                    y: [0, -8, 0],
                    opacity: [0.7, 1, 0.7],
                  }}
                  transition={{ duration: 1.8, repeat: Infinity, delay: 1 }}
                />
              </div>

              {/* Animated Progress Bar */}
              <div className="p-6 bg-white/5 backdrop-blur-sm border-t border-white/10">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-white/80">
                    AI Generation Progress
                  </span>
                  <span className="text-sm text-white/60">Processing...</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden">
                  <motion.div
                    className="h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: "75%" }}
                    transition={{ duration: 2, ease: "easeInOut" }}
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section
        id="features"
        className="relative z-10 py-32 px-6 bg-slate-900/50 backdrop-blur-sm"
      >
        <motion.div
          className="max-w-7xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="text-center mb-20"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Total Creative Control in{" "}
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Every Scene
              </span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Professional video creation tools powered by advanced AI
              technology
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Sparkles,
                title: "Brand Elements",
                description:
                  "Add your brand colors, fonts, and logos to maintain consistency across all videos.",
                color: "from-purple-500 to-purple-600",
                bgColor: "bg-purple-500/20",
              },
              {
                icon: Palette,
                title: "Background & Assets",
                description:
                  "Choose from thousands of backgrounds, images, and video assets for your content.",
                color: "from-blue-500 to-blue-600",
                bgColor: "bg-blue-500/20",
              },
              {
                icon: BarChart3,
                title: "Audio & Music",
                description:
                  "AI-generated voiceovers and background music that perfectly match your content.",
                color: "from-green-500 to-green-600",
                bgColor: "bg-green-500/20",
              },
              {
                icon: Users,
                title: "Text Overlays & Captions",
                description:
                  "Automatically generate captions and add custom text overlays with perfect timing.",
                color: "from-orange-500 to-orange-600",
                bgColor: "bg-orange-500/20",
              },
              {
                icon: Zap,
                title: "Transitions & Animations",
                description:
                  "Smooth transitions and engaging animations that bring your content to life.",
                color: "from-pink-500 to-pink-600",
                bgColor: "bg-pink-500/20",
              },
              {
                icon: Globe,
                title: "Multi-Scene Timeline",
                description:
                  "Create complex videos with multiple scenes and seamless scene transitions.",
                color: "from-indigo-500 to-indigo-600",
                bgColor: "bg-indigo-500/20",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group"
              >
                <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 h-full">
                  <CardContent className="p-8">
                    <motion.div
                      className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </motion.div>
                    <h3 className="text-2xl font-semibold text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-white/70 leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* Templates Section */}
      <section id="templates" className="relative z-10 py-32 px-6">
        <motion.div
          className="max-w-7xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="text-center mb-20"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Pre-Made Templates to{" "}
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Jumpstart Your Story
              </span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">
              Choose from hundreds of professionally designed templates
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Sales Pitch",
                category: "Business",
                gradient: "from-blue-500 to-purple-600",
              },
              {
                title: "Product Demo",
                category: "Marketing",
                gradient: "from-green-500 to-blue-600",
              },
              {
                title: "Social Media",
                category: "Content",
                gradient: "from-pink-500 to-purple-600",
              },
              {
                title: "Tutorial",
                category: "Education",
                gradient: "from-orange-500 to-red-600",
              },
              {
                title: "Testimonial",
                category: "Social Proof",
                gradient: "from-teal-500 to-green-600",
              },
              {
                title: "Event Promo",
                category: "Marketing",
                gradient: "from-purple-500 to-pink-600",
              },
            ].map((template, index) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group"
              >
                <Card className="bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 overflow-hidden">
                  <CardContent className="p-0">
                    <div className="aspect-video relative overflow-hidden">
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-br ${template.gradient} opacity-80`}
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <motion.div
                          className="text-white text-center"
                          initial={{ scale: 0.8, opacity: 0.7 }}
                          whileHover={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Play className="w-12 h-12 mx-auto mb-2" />
                          <p className="text-sm font-medium">
                            Preview Template
                          </p>
                        </motion.div>
                      </div>
                      <motion.div
                        className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center"
                        whileHover={{ opacity: 1 }}
                      >
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button className="bg-white/90 text-gray-900 hover:bg-white backdrop-blur-sm">
                            Use Template
                          </Button>
                        </motion.div>
                      </motion.div>
                    </div>
                    <div className="p-6">
                      <Badge className="mb-3 bg-white/20 text-white border-white/30">
                        {template.category}
                      </Badge>
                      <h3 className="text-xl font-semibold text-white group-hover:text-purple-300 transition-colors duration-300">
                        {template.title}
                      </h3>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-32 px-6 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute -top-24 -left-24 w-48 h-48 bg-white/10 rounded-full blur-3xl"
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
            }}
            transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div
            className="absolute -bottom-24 -right-24 w-64 h-64 bg-white/10 rounded-full blur-3xl"
            animate={{
              x: [0, -80, 0],
              y: [0, 60, 0],
            }}
            transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
          />
        </div>

        <motion.div
          className="relative z-10 max-w-5xl mx-auto text-center"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            className="text-5xl md:text-6xl font-bold text-white mb-8"
            initial={{ scale: 0.9 }}
            whileInView={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Ready to Make Videos That Look Like{" "}
            <span className="text-yellow-300">Your Brand?</span>
          </motion.h2>

          <motion.p
            className="text-xl text-white/90 mb-12 max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Join thousands of creators and businesses using FLOWY AI to create
            professional videos that convert
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center mb-8"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <Link href="/login">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-12 py-4 rounded-2xl font-semibold shadow-2xl">
                  Get Started Free
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </motion.div>
            </Link>
          </motion.div>

          <motion.div
            className="flex items-center justify-center space-x-8 text-white/80"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center space-x-2">
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <CheckCircle className="w-5 h-5 text-green-300" />
              </motion.div>
              <span className="text-sm">No credit card required</span>
            </div>
            <div className="flex items-center space-x-2">
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
              >
                <CheckCircle className="w-5 h-5 text-green-300" />
              </motion.div>
              <span className="text-sm">14-day free trial</span>
            </div>
            <div className="flex items-center space-x-2">
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
              >
                <CheckCircle className="w-5 h-5 text-green-300" />
              </motion.div>
              <span className="text-sm">Cancel anytime</span>
            </div>
          </motion.div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 bg-slate-900/80 backdrop-blur-sm border-t border-white/10 text-white py-20 px-6">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center mb-12">
            <motion.div
              className="flex flex-col items-center md:items-start text-center md:text-left"
              initial={{ x: -50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="flex items-center space-x-5 mb-8"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <div className="relative">
                  <Image
                    src="/logo-w.png"
                    alt="FLOWY AI Logo"
                    width={64}
                    height={64}
                    className="w-20 h-16 object-contain drop-shadow-lg"
                  />
                </div>
                <span className="text-3xl font-bold text-white tracking-wide">
                  FLOWY AI
                </span>
              </motion.div>
              <p className="text-white/70 max-w-sm leading-relaxed">
                AI-powered video generation platform for creators and
                businesses. Transform your ideas into stunning videos with
                cutting-edge technology.
              </p>

              {/* Social proof */}
              <motion.div
                className="flex items-center space-x-4 mt-6"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.6 + i * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    </motion.div>
                  ))}
                </div>
                <span className="text-white/60 text-sm">
                  Trusted by 10,000+ creators
                </span>
              </motion.div>
            </motion.div>

            <motion.div
              className="flex flex-col items-center md:items-end text-center md:text-right"
              initial={{ x: 50, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="grid grid-cols-2 gap-8 mb-8">
                <div>
                  <h4 className="text-white font-semibold mb-4">Product</h4>
                  <ul className="space-y-3 text-white/70">
                    <li>
                      <motion.a
                        href="#features"
                        className="hover:text-white transition-colors duration-200"
                        whileHover={{ x: 5 }}
                      >
                        Features
                      </motion.a>
                    </li>
                    <li>
                      <motion.a
                        href="#templates"
                        className="hover:text-white transition-colors duration-200"
                        whileHover={{ x: 5 }}
                      >
                        Templates
                      </motion.a>
                    </li>
                    <li>
                      <motion.a
                        href="#"
                        className="hover:text-white transition-colors duration-200"
                        whileHover={{ x: 5 }}
                      >
                        Pricing
                      </motion.a>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-white font-semibold mb-4">Support</h4>
                  <ul className="space-y-3 text-white/70">
                    <li>
                      <motion.a
                        href="#"
                        className="hover:text-white transition-colors duration-200"
                        whileHover={{ x: 5 }}
                      >
                        Help Center
                      </motion.a>
                    </li>
                    <li>
                      <motion.a
                        href="#"
                        className="hover:text-white transition-colors duration-200"
                        whileHover={{ x: 5 }}
                      >
                        Documentation
                      </motion.a>
                    </li>
                    <li>
                      <motion.a
                        href="#"
                        className="hover:text-white transition-colors duration-200"
                        whileHover={{ x: 5 }}
                      >
                        Community
                      </motion.a>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="border-t border-white/20 pt-8 flex flex-col md:flex-row justify-between items-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <p className="text-white/60 mb-4 md:mb-0">
              © 2025 FLOWY AI. All rights reserved.
            </p>
            <div className="flex space-x-8">
              <motion.a
                href="#"
                className="text-white/60 hover:text-white transition-colors duration-200"
                whileHover={{ y: -2 }}
              >
                Privacy
              </motion.a>
              <motion.a
                href="#"
                className="text-white/60 hover:text-white transition-colors duration-200"
                whileHover={{ y: -2 }}
              >
                Terms
              </motion.a>
              <motion.a
                href="#"
                className="text-white/60 hover:text-white transition-colors duration-200"
                whileHover={{ y: -2 }}
              >
                Cookies
              </motion.a>
              <Link href="/why-internet">
                <motion.span
                  className="text-white/60 hover:text-white transition-colors duration-200 cursor-pointer"
                  whileHover={{ y: -2 }}
                >
                  Why Internet?
                </motion.span>
              </Link>
            </div>
          </motion.div>
        </motion.div>
      </footer>
    </div>
  );
}
