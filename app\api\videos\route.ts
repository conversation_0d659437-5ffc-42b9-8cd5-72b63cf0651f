import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { count, desc, eq } from "drizzle-orm";

export async function GET(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const offset = parseInt(url.searchParams.get("offset") || "0");
    
    // Get total videos count for the user
    const videosResult = await db
      .select({ count: count() })
      .from(videoGenerations)
      .where(eq(videoGenerations.userId, session.user.id));
    
    const totalCount = videosResult[0]?.count || 0;
    
    // Get videos with pagination
    const videos = await db
      .select()
      .from(videoGenerations)
      .where(eq(videoGenerations.userId, session.user.id))
      .orderBy(desc(videoGenerations.createdAt))
      .limit(limit)
      .offset(offset);
    
    return NextResponse.json({
      videos,
      totalCount,
      limit,
      offset
    });
    
  } catch (error) {
    console.error("Error fetching videos:", error);
    return NextResponse.json(
      { error: "Failed to fetch videos" },
      { status: 500 }
    );
  }
} 