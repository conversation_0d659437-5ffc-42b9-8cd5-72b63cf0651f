/**
 * Utility functions for video generation and status checking
 */

/**
 * Video status response interface
 */
export interface VideoStatusResponse {
  id: string;
  status: "pending" | "completed" | "failed" | "processing_audio" | "failed_audio";
  videoUrl?: string;
  error?: string;
  queueStatus?: string;
  position?: number;
  requestId?: string;
  prompt?: string;
}

/**
 * Sleep for a specified amount of time
 */
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Check video status with exponential backoff
 * @param videoId The ID of the video to check
 * @param options Configuration options
 * @returns The final video data or null if timeout is reached
 */
export async function checkVideoStatusWithBackoff(
  videoId: string, 
  options: {
    maxAttempts?: number;
    initialDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    onStatusUpdate?: (status: VideoStatusResponse) => void;
    autoGenerateAudio?: boolean;
  } = {}
) {
  const {
    maxAttempts = 30,
    initialDelay = 2000,
    maxDelay = 30000,
    backoffFactor = 1.5,
    onStatusUpdate,
    autoGenerateAudio = true
  } = options;
  
  let currentDelay = initialDelay;
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`/api/video/status?id=${videoId}`);
      
      if (!response.ok) {
        throw new Error(`Status check failed with status: ${response.status}`);
      }
      
      const data = await response.json() as VideoStatusResponse;
      
      // Call the status update callback if provided
      if (onStatusUpdate) {
        onStatusUpdate(data);
      }
      
      // If the video is completed and we want to add audio, start the process
      if (data.status === "completed" && autoGenerateAudio && data.videoUrl && data.prompt) {
        // Start audio generation process
        console.log("Video completed, starting audio generation...");
        try {
          // Update status to processing_audio before starting the process
          const processingUpdate: VideoStatusResponse = {
            ...data,
            status: "processing_audio"
          };
          
          // Call the status update callback with processing_audio status
          if (onStatusUpdate) {
            onStatusUpdate(processingUpdate);
          }
          
          // Generate audio
          const audioResponse = await generateAudioForVideo(data.id, data.prompt);
          
          // If audio generation was successful, return the updated response
          if (audioResponse) {
            return audioResponse;
          }
          
          // If audio generation failed but the video is still valid, return the original video data
          return data;
        } catch (audioError) {
          console.error("Error generating audio:", audioError);
          // Continue with the video without audio
          return data;
        }
      }
      
      // If the video is failed, return the data
      if (data.status === "failed") {
        return data;
      }
      
      // If the video is completed but we're not adding audio, return the data
      if (data.status === "completed" && !autoGenerateAudio) {
        return data;
      }
      
      // If still pending or processing_audio, wait and try again
      attempts++;
      await sleep(currentDelay);
      
      // Increase delay with exponential backoff, but cap it at maxDelay
      currentDelay = Math.min(currentDelay * backoffFactor, maxDelay);
    } catch (error) {
      console.error("Error checking video status:", error);
      attempts++;
      await sleep(currentDelay);
      currentDelay = Math.min(currentDelay * backoffFactor, maxDelay);
    }
  }
  
  // If we've reached max attempts, return null
  return null;
}

/**
 * Check audio generation status with exponential backoff
 * @param videoId The ID of the video
 * @param requestId The ID of the audio generation request
 * @param options Configuration options
 * @returns The final video data or null if timeout is reached
 */
export async function checkAudioStatusWithBackoff(
  videoId: string,
  requestId: string,
  options: {
    maxAttempts?: number;
    initialDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    onStatusUpdate?: (status: VideoStatusResponse) => void;
  } = {}
) {
  const {
    maxAttempts = 60, // Increased for audio which can take longer
    initialDelay = 3000,
    maxDelay = 30000,
    backoffFactor = 1.5,
    onStatusUpdate
  } = options;
  
  let currentDelay = initialDelay;
  let attempts = 0;
  
  console.log(`Starting audio status check for videoId: ${videoId}, requestId: ${requestId}`);
  
  while (attempts < maxAttempts) {
    try {
      console.log(`Audio status check attempt ${attempts + 1}/${maxAttempts}`);
      
      const response = await fetch(`/api/video/audio/status?videoId=${videoId}&requestId=${requestId}`);
      
      if (!response.ok) {
        console.error(`Audio status check failed with status: ${response.status}`);
        throw new Error(`Audio status check failed with status: ${response.status}`);
      }
      
      const data = await response.json() as VideoStatusResponse;
      console.log(`Audio status response:`, data);
      
      // Call the status update callback if provided
      if (onStatusUpdate) {
        onStatusUpdate(data);
      }
      
      // If the audio is completed or failed, return the data
      if (data.status === "completed" || data.status === "failed_audio") {
        console.log(`Audio generation ${data.status}`);
        return data;
      }
      
      // If still processing, wait and try again
      console.log(`Audio still processing, waiting ${currentDelay}ms before next check`);
      attempts++;
      await sleep(currentDelay);
      
      // Increase delay with exponential backoff, but cap it at maxDelay
      currentDelay = Math.min(currentDelay * backoffFactor, maxDelay);
    } catch (error) {
      console.error("Error checking audio status:", error);
      attempts++;
      await sleep(currentDelay);
      currentDelay = Math.min(currentDelay * backoffFactor, maxDelay);
    }
  }
  
  console.error(`Audio status check timed out after ${maxAttempts} attempts`);
  
  // If we've reached max attempts, return a failed status
  const timeoutResponse: VideoStatusResponse = {
    id: videoId,
    status: "failed_audio",
    error: "Audio generation timed out"
  };
  
  // Call the status update callback with the timeout response
  if (onStatusUpdate) {
    onStatusUpdate(timeoutResponse);
  }
  
  return timeoutResponse;
}

/**
 * Video generation options interface
 */
export interface VideoGenerationOptions {
  negative_prompt?: string;
  resolution?: string;
  aspect_ratio?: string;
}

/**
 * Generate a video and wait for completion with status updates
 * @param prompt The prompt for the video
 * @param options Additional options for video generation
 * @param statusCallback Callback function for status updates
 * @returns The completed video data or null if generation failed
 */
export async function generateVideoAndWaitForCompletion(
  prompt: string,
  options: VideoGenerationOptions = {},
  statusCallback?: (status: VideoStatusResponse) => void
) {
  try {
    // Submit the video generation request
    const response = await fetch("/api/video", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        prompt,
        ...options
      }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to generate video");
    }
    
    const { id } = await response.json();
    
    // Check status with backoff
    return await checkVideoStatusWithBackoff(id, {
      onStatusUpdate: statusCallback
    });
  } catch (error) {
    console.error("Error generating video:", error);
    return null;
  }
}

/**
 * Generate audio for a video and wait for completion with status updates
 * @param videoId The ID of the video to add audio to
 * @param prompt The prompt for the audio generation
 * @param statusCallback Callback function for status updates
 * @returns The completed video data or null if generation failed
 */
export async function generateAudioForVideo(
  videoId: string,
  prompt: string,
  statusCallback?: (status: VideoStatusResponse) => void
) {
  try {
    console.log(`Starting audio generation for video ${videoId} with prompt: ${prompt}`);
    
    // Submit the audio generation request
    const response = await fetch("/api/video/audio", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        videoId,
        prompt
      }),
    });
    
    if (!response.ok) {
      const error = await response.json();
      console.error(`Audio generation API error:`, error);
      throw new Error(error.error || "Failed to generate audio");
    }
    
    const data = await response.json();
    const { id, requestId } = data;
    
    console.log(`Audio generation initiated with requestId: ${requestId}`);
    
    if (!requestId) {
      console.error(`No request ID returned from audio generation API`);
      throw new Error("No request ID returned");
    }
    
    // Update status callback if provided
    if (statusCallback) {
      statusCallback({
        id: videoId,
        status: "processing_audio",
        requestId
      });
    }
    
    // Check status with backoff
    return await checkAudioStatusWithBackoff(id, requestId, {
      onStatusUpdate: statusCallback
    });
  } catch (error) {
    console.error("Error generating audio:", error);
    
    // Return failed status
    const failedStatus: VideoStatusResponse = {
      id: videoId,
      status: "failed_audio",
      error: error instanceof Error ? error.message : "Unknown error generating audio"
    };
    
    // Update status callback if provided
    if (statusCallback) {
      statusCallback(failedStatus);
    }
    
    return failedStatus;
  }
} 