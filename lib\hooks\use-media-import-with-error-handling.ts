import { useCallback, useState } from "react";
import { useAssetStore } from "@/lib/stores/asset-store";
import {
  useError<PERSON>and<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/lib/services/error-handling-service";
import { toast, createProgressToast } from "@/lib/services/toast-service";
import { loading } from "@/lib/services/loading-service";

interface ImportResult {
  success: boolean;
  assetId?: string;
  error?: string;
}

interface ImportOptions {
  maxFileSize?: number; // in bytes
  allowedTypes?: string[];
  onProgress?: (progress: number) => void;
  retryOnFailure?: boolean;
}

const DEFAULT_OPTIONS: ImportOptions = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "video/mp4",
    "video/webm",
    "video/quicktime",
    "audio/mp3",
    "audio/wav",
    "audio/ogg",
  ],
  retryOnFailure: true,
};

export function useMediaImportWithErrorHandling() {
  const assetStore = useAssetStore();
  const { reportError } = useErrorHandling();
  const [importing, setImporting] = useState<Record<string, boolean>>({});

  const validateFile = useCallback(
    (file: File, options: ImportOptions): string | null => {
      // Check file size
      if (options.maxFileSize && file.size > options.maxFileSize) {
        return `File size (${(file.size / 1024 / 1024).toFixed(
          1
        )}MB) exceeds maximum allowed size (${(
          options.maxFileSize /
          1024 /
          1024
        ).toFixed(1)}MB)`;
      }

      // Check file type
      if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
        return `File type "${
          file.type
        }" is not supported. Allowed types: ${options.allowedTypes.join(", ")}`;
      }

      // Check if file is corrupted (basic check)
      if (file.size === 0) {
        return "File appears to be empty or corrupted";
      }

      return null;
    },
    []
  );

  const importFile = useCallback(
    async (file: File, options: ImportOptions = {}): Promise<ImportResult> => {
      const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
      const fileKey = `${file.name}-${file.size}`;

      // Prevent duplicate imports
      if (importing[fileKey]) {
        return { success: false, error: "File is already being imported" };
      }

      setImporting((prev) => ({ ...prev, [fileKey]: true }));

      try {
        // Validate file
        const validationError = validateFile(file, mergedOptions);
        if (validationError) {
          const errorId = reportError(
            ErrorHandler.handleMediaImportError(
              new Error(validationError),
              file.name
            )
          );

          toast.error("Import Failed", validationError, {
            label: "Dismiss",
            action: () => toast.dismiss(errorId),
          });

          return { success: false, error: validationError };
        }

        // Create progress toast
        const progressToast = createProgressToast(
          "Importing Media",
          `Uploading ${file.name}...`
        );

        // Start loading indicator
        const loadingId = loading.start(
          "media_import",
          `Importing ${file.name}`
        );

        try {
          // Simulate file upload with progress
          await uploadFileWithProgress(file, (progress) => {
            progressToast.update(progress, `Uploading ${file.name}...`);
            loading.update(loadingId, { progress });
            options.onProgress?.(progress);
          });

          // Add to asset store using the existing import method
          const asset = await assetStore.importAsset(file);
          const assetId = asset.id;

          // Success feedback
          progressToast.success(
            "Import Complete",
            `${file.name} imported successfully`
          );
          loading.stop(loadingId);

          return { success: true, assetId };
        } catch (uploadError) {
          const error = uploadError as Error;

          // Handle specific upload errors
          let shouldRetry = mergedOptions.retryOnFailure;
          let errorMessage = error.message;

          if (error.message.includes("network")) {
            errorMessage =
              "Network error during upload. Please check your connection.";
            shouldRetry = true;
          } else if (error.message.includes("timeout")) {
            errorMessage = "Upload timed out. Please try again.";
            shouldRetry = true;
          } else if (error.message.includes("server")) {
            errorMessage =
              "Server error during upload. Please try again later.";
            shouldRetry = true;
          } else {
            shouldRetry = false;
          }

          // Report error
          reportError(ErrorHandler.handleMediaImportError(error, file.name));

          // Update progress toast to show error
          progressToast.error("Import Failed", errorMessage);
          loading.stop(loadingId);

          // Show retry option if applicable
          if (shouldRetry) {
            toast.error("Import Failed", errorMessage, {
              label: "Retry",
              action: () => importFile(file, options),
            });
          }

          return { success: false, error: errorMessage };
        }
      } catch (error) {
        const err = error as Error;
        const errorId = reportError(
          ErrorHandler.handleMediaImportError(err, file.name)
        );

        toast.error(
          "Import Error",
          "An unexpected error occurred during import",
          {
            label: "Dismiss",
            action: () => toast.dismiss(errorId),
          }
        );

        return { success: false, error: err.message };
      } finally {
        setImporting((prev) => ({ ...prev, [fileKey]: false }));
      }
    },
    [assetStore, reportError, importing, validateFile]
  );

  const importMultipleFiles = useCallback(
    async (
      files: File[],
      options: ImportOptions = {}
    ): Promise<ImportResult[]> => {
      const results: ImportResult[] = [];
      const totalFiles = files.length;
      let completedFiles = 0;

      // Create overall progress toast
      const overallProgress = createProgressToast(
        "Importing Multiple Files",
        `Importing ${totalFiles} files...`
      );

      try {
        // Import files sequentially to avoid overwhelming the system
        for (const file of files) {
          const result = await importFile(file, {
            ...options,
            onProgress: (fileProgress) => {
              const overallProgressValue =
                ((completedFiles + fileProgress / 100) / totalFiles) * 100;
              overallProgress.update(
                overallProgressValue,
                `Importing ${file.name} (${completedFiles + 1}/${totalFiles})`
              );
            },
          });

          results.push(result);
          completedFiles++;
        }

        const successCount = results.filter((r) => r.success).length;
        const failureCount = results.filter((r) => !r.success).length;

        if (failureCount === 0) {
          overallProgress.success(
            "All Files Imported",
            `Successfully imported ${successCount} files`
          );
        } else if (successCount === 0) {
          overallProgress.error(
            "Import Failed",
            `Failed to import all ${failureCount} files`
          );
        } else {
          overallProgress.success(
            "Import Completed",
            `Imported ${successCount} files, ${failureCount} failed`
          );
        }

        return results;
      } catch (error) {
        overallProgress.error("Import Error", "An unexpected error occurred");
        throw error;
      }
    },
    [importFile]
  );

  const isImporting = useCallback(
    (fileName?: string) => {
      if (!fileName) {
        return Object.values(importing).some(Boolean);
      }
      return importing[fileName] || false;
    },
    [importing]
  );

  return {
    importFile,
    importMultipleFiles,
    isImporting,
    validateFile,
  };
}

// Mock upload function - replace with actual implementation
async function uploadFileWithProgress(
  file: File,
  onProgress: (progress: number) => void
): Promise<{
  url: string;
  duration?: number;
  metadata?: Record<string, unknown>;
}> {
  return new Promise((resolve, reject) => {
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 20;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        // Simulate successful upload
        setTimeout(() => {
          resolve({
            url: URL.createObjectURL(file),
            duration: file.type.startsWith("video/") ? 30 : undefined,
            metadata: {
              originalName: file.name,
              uploadedAt: new Date().toISOString(),
            },
          });
        }, 500);
      }
      onProgress(progress);
    }, 200);

    // Simulate random failures for testing
    if (Math.random() < 0.05) {
      // 5% chance of failure
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error("Network timeout during upload"));
      }, 2000);
    }
  });
}
