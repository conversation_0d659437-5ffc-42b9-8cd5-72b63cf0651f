import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { z } from "zod";
import { auth } from "@/lib/auth";
import { eq, and } from "drizzle-orm";

// Schema for updating a user
const updateUserSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  email: z.string().email("Please enter a valid email address").optional(),
  credits: z.number().int().min(0).optional(),
});

// GET - Get a specific user by ID
export async function GET(
  request: Request, 
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await auth();
    
    // Verify the requestor is an agency user
    if (!session || (session.user.userType !== "agency-basic" && session.user.userType !== "agency-deluxe")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { userId } = await params;
    
    // Get the user and verify it belongs to this agency
    const user = await db.query.users.findFirst({
      where: (users, { eq, and }) => and(
        eq(users.id, userId),
        eq(users.createdBy, session.user.id),
        eq(users.userType, "basic")
      )
    });
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Return the user with sensitive information removed
    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        userType: user.userType,
        credits: user.credits,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }
    });
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json({ error: "Failed to fetch user" }, { status: 500 });
  }
}

// PATCH - Update a specific user
export async function PATCH(
  request: Request, 
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await auth();
    
    // Verify the requestor is an agency user
    if (!session || (session.user.userType !== "agency-basic" && session.user.userType !== "agency-deluxe")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { userId } = await params;
    
    // Check if the user exists and belongs to this agency
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq, and }) => and(
        eq(users.id, userId),
        eq(users.createdBy, session.user.id),
        eq(users.userType, "basic")
      )
    });
    
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Parse and validate the request body
    const body = await request.json();
    const result = updateUserSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.flatten() },
        { status: 400 }
      );
    }
    
    // Update the user
    await db.update(users)
      .set({
        ...result.data,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(users.id, userId),
          eq(users.createdBy, session.user.id),
          eq(users.userType, "basic")
        )
      );
    
    // Get the updated user
    const updatedUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, userId)
    });
    
    return NextResponse.json({
      user: {
        id: updatedUser?.id,
        name: updatedUser?.name,
        email: updatedUser?.email,
        userType: updatedUser?.userType,
        credits: updatedUser?.credits,
        createdAt: updatedUser?.createdAt,
        updatedAt: updatedUser?.updatedAt,
      }
    });
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json({ error: "Failed to update user" }, { status: 500 });
  }
}

// DELETE - Delete a specific user
export async function DELETE(
  request: Request, 
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await auth();
    
    // Verify the requestor is an agency user
    if (!session || (session.user.userType !== "agency-basic" && session.user.userType !== "agency-deluxe")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { userId } = await params;
    
    // Check if the user exists and belongs to this agency
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq, and }) => and(
        eq(users.id, userId),
        eq(users.createdBy, session.user.id),
        eq(users.userType, "basic")
      )
    });
    
    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Delete the user
    await db.delete(users).where(
      and(
        eq(users.id, userId),
        eq(users.createdBy, session.user.id),
        eq(users.userType, "basic")
      )
    );
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json({ error: "Failed to delete user" }, { status: 500 });
  }
} 