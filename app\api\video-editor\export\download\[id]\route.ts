import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ExportService } from "@/lib/services/export-service";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const exportJob = ExportService.getExportJob(id);

    if (!exportJob) {
      return NextResponse.json(
        { error: "Export job not found" },
        { status: 404 }
      );
    }

    // Verify the user owns this export job
    if (exportJob.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    if (exportJob.status !== "completed" || !exportJob.outputUrl) {
      return NextResponse.json(
        { error: "Export not completed or no output available" },
        { status: 400 }
      );
    }

    // In a real implementation, this would:
    // 1. Fetch the actual video file from storage
    // 2. Stream it to the client
    // 3. Set appropriate headers for download
    
    // For now, we'll redirect to a sample video or return a mock response
    // In production, you would serve the actual rendered video file
    
    // Mock video content - in production, replace with actual file serving
    const mockVideoUrl = 'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4';
    
    // Set headers for file download
    const headers = new Headers();
    headers.set("Content-Type", "video/mp4");
    headers.set(
      "Content-Disposition",
      `attachment; filename="export-${id}.mp4"`
    );
    
    // In a real implementation, you would:
    // const videoBuffer = await fetchVideoFromStorage(exportJob.outputUrl);
    // return new NextResponse(videoBuffer, { headers });
    
    // For demo purposes, redirect to sample video
    return NextResponse.redirect(mockVideoUrl);
    
  } catch (error) {
    console.error("Error downloading export:", error);
    return NextResponse.json(
      { error: "Failed to download export" },
      { status: 500 }
    );
  }
}
