"use client";

import { useState, useCallback, useRef } from "react";
import { Upload, FileVideo, FileAudio, FileImage, X } from "lucide-react";
import { useAssetStore } from "@/lib/stores/asset-store";
import { Progress } from "@/components/ui/progress";

interface FileUploadZoneProps {
  onFilesSelected: (files: FileList) => void;
  className?: string;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: "uploading" | "completed" | "error";
  error?: string;
}

export function FileUploadZone({
  onFilesSelected,
  className,
}: FileUploadZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const assetStore = useAssetStore();
  const dragCounterRef = useRef(0);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current++;

    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current--;

    if (dragCounterRef.current === 0) {
      setIsDragOver(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleFileUpload = useCallback(
    async (files: FileList) => {
      const validFiles = Array.from(files).filter((file) => {
        const isValid =
          file.type.startsWith("video/") ||
          file.type.startsWith("audio/") ||
          file.type.startsWith("image/");
        return isValid;
      });

      if (validFiles.length === 0) {
        return;
      }

      // Initialize progress tracking
      const initialProgress: UploadProgress[] = validFiles.map((file) => ({
        fileName: file.name,
        progress: 0,
        status: "uploading" as const,
      }));

      setUploadProgress(initialProgress);

      // Upload files one by one
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];

        try {
          // Update progress to show starting
          setUploadProgress((prev) =>
            prev.map((item, index) =>
              index === i ? { ...item, progress: 10 } : item
            )
          );

          // Simulate upload progress (in real implementation, this would come from the upload API)
          const progressInterval = setInterval(() => {
            setUploadProgress((prev) =>
              prev.map((item, index) =>
                index === i && item.progress < 90
                  ? { ...item, progress: item.progress + 10 }
                  : item
              )
            );
          }, 200);

          await assetStore.importAsset(
            file,
            undefined,
            assetStore.currentFolderId || undefined
          );

          clearInterval(progressInterval);

          // Mark as completed
          setUploadProgress((prev) =>
            prev.map((item, index) =>
              index === i
                ? { ...item, progress: 100, status: "completed" as const }
                : item
            )
          );
        } catch (error) {
          // Mark as error
          setUploadProgress((prev) =>
            prev.map((item, index) =>
              index === i
                ? {
                    ...item,
                    status: "error" as const,
                    error:
                      error instanceof Error ? error.message : "Upload failed",
                  }
                : item
            )
          );
        }
      }

      // Clear progress after a delay
      setTimeout(() => {
        setUploadProgress([]);
      }, 3000);

      // Call the callback with all files
      onFilesSelected(files);
    },
    [assetStore, onFilesSelected]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);
      dragCounterRef.current = 0;

      const files = e.dataTransfer.files;
      if (files && files.length > 0) {
        handleFileUpload(files);
      }
    },
    [handleFileUpload]
  );

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();

    if (
      ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"].includes(
        extension || ""
      )
    ) {
      return <FileVideo className="w-5 h-5 text-blue-400" />;
    }

    if (["mp3", "wav", "flac", "aac", "ogg", "m4a"].includes(extension || "")) {
      return <FileAudio className="w-5 h-5 text-green-400" />;
    }

    if (
      ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"].includes(
        extension || ""
      )
    ) {
      return <FileImage className="w-5 h-5 text-purple-400" />;
    }

    return <Upload className="w-5 h-5 text-gray-400" />;
  };

  const removeProgressItem = (index: number) => {
    setUploadProgress((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className={className}>
      {/* Upload Zone */}
      <div
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
          ${
            isDragOver
              ? "border-purple-500 bg-purple-500/10"
              : "border-gray-600 hover:border-gray-500"
          }
          ${assetStore.isImporting ? "opacity-50 pointer-events-none" : ""}
        `}
      >
        <Upload
          className={`w-12 h-12 mx-auto mb-4 ${
            isDragOver ? "text-purple-400" : "text-gray-400"
          }`}
        />
        <h4 className="text-lg font-medium text-white mb-2">
          {isDragOver ? "Drop files here" : "Drag & drop files here"}
        </h4>
        <p className="text-gray-400 text-sm">
          Supports video, audio, and image files
        </p>
        <p className="text-gray-500 text-xs mt-1">
          MP4, AVI, MOV, MP3, WAV, JPG, PNG, and more
        </p>
      </div>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <div className="mt-4 space-y-3">
          <h4 className="text-sm font-medium text-white">Uploading Files</h4>
          {uploadProgress.map((item, index) => (
            <div key={index} className="bg-gray-800 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getFileIcon(item.fileName)}
                  <span className="text-sm text-white truncate max-w-xs">
                    {item.fileName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {item.status === "completed" && (
                    <span className="text-xs text-green-400">✓ Complete</span>
                  )}
                  {item.status === "error" && (
                    <span className="text-xs text-red-400">✗ Error</span>
                  )}
                  <button
                    onClick={() => removeProgressItem(index)}
                    className="text-gray-400 hover:text-white"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {item.status === "uploading" && (
                <Progress value={item.progress} className="h-2" />
              )}

              {item.status === "error" && item.error && (
                <p className="text-xs text-red-400 mt-1">{item.error}</p>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
