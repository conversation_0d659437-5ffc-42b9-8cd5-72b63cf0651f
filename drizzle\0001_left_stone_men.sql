CREATE TABLE "video_generations" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"prompt" text NOT NULL,
	"negative_prompt" text,
	"resolution" text DEFAULT '720p',
	"aspect_ratio" text DEFAULT '16:9',
	"seed" integer,
	"video_url" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"request_id" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "video_generations" ADD CONSTRAINT "video_generations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;