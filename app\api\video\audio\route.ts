import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { fal } from "@fal-ai/client";

// Initialize fal.ai client with server-side credentials
fal.config({
  credentials: process.env.FAL_KEY
});

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await req.json();
    const { videoId, prompt } = body;
    
    if (!videoId) {
      return NextResponse.json({ error: "Video ID is required" }, { status: 400 });
    }
    
    if (!prompt) {
      return NextResponse.json({ error: "Audio prompt is required" }, { status: 400 });
    }
    
    // Get the video from the database
    const [video] = await db
      .select()
      .from(videoGenerations)
      .where(eq(videoGenerations.id, videoId))
      .limit(1);
    
    if (!video) {
      return NextResponse.json({ error: "Video not found" }, { status: 404 });
    }
    
    // Check if the video belongs to the current user
    if (video.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if the video has a URL
    if (!video.videoUrl) {
      return NextResponse.json({ error: "Video URL not found" }, { status: 400 });
    }
    
    // Update the video status to "processing_audio"
    await db.update(videoGenerations)
      .set({ 
        status: "processing_audio", 
        updatedAt: new Date() 
      })
      .where(eq(videoGenerations.id, videoId));
    
    try {
      // Submit request to fal.ai mmaudio-v2
      const { request_id } = await fal.queue.submit("fal-ai/mmaudio-v2", {
        input: {
          video_url: video.videoUrl,
          prompt: prompt
        },
      });
      
      // Update the database with the audio request ID
      await db.update(videoGenerations)
        .set({ 
          status: "processing_audio", 
          requestId: request_id,
          updatedAt: new Date() 
        })
        .where(eq(videoGenerations.id, videoId));
      
      // Return the request ID
      return NextResponse.json({ 
        id: videoId,
        requestId: request_id,
        status: "processing_audio"
      });
    } catch (error: unknown) {
      console.error("fal.ai mmaudio API error:", error);
      
      // Update the video status back to completed
      await db.update(videoGenerations)
        .set({ 
          status: "completed", 
          updatedAt: new Date() 
        })
        .where(eq(videoGenerations.id, videoId));
      
      const errorMessage = error instanceof Error ? error.message : "Failed to generate audio with external API";
      
      return NextResponse.json({ 
        error: "API error", 
        message: errorMessage
      }, { status: 502 });
    }
    
  } catch (error: unknown) {
    console.error("Error generating audio:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    
    return NextResponse.json({ 
      error: "Failed to generate audio",
      message: errorMessage
    }, { status: 500 });
  }
} 