import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";

// This function would typically be called by a cron job once per day
// For this implementation, we'll check and reset if 24 hours have passed
// In a production environment, use a scheduled task/cron job instead

// Function to get max credits based on user type
function getMaxCredits(userType: string): number {
  switch (userType) {
    case "basic":
      return 5;
    case "unlimited":
      return 10;
    case "agency-basic":
    case "agency-deluxe":
      return 5;
    case "admin":
      return 5;
    default:
      return 5;
  }
}

export async function POST(req: Request) {
  try {
    // This endpoint should be secured in production
    // Typically with an API key or restricted to internal use

    const body = await req.json();
    const { userId } = body;
    
    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }

    // Get the user
    const user = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, userId)
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if 24 hours have passed since last reset
    const lastReset = user.lastCreditReset;
    const now = new Date();
    const timeDiff = now.getTime() - lastReset.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);

    if (hoursDiff >= 24) {
      // Reset credits based on user type
      const maxCredits = getMaxCredits(user.userType);
      
      await db.update(users)
        .set({ 
          credits: maxCredits,
          lastCreditReset: now,
          updatedAt: now
        })
        .where(eq(users.id, userId));

      return NextResponse.json({
        success: true,
        message: "Credits reset successfully",
        credits: maxCredits
      });
    }

    return NextResponse.json({
      success: false,
      message: "Credits reset not yet due",
      hoursRemaining: 24 - hoursDiff,
      currentCredits: user.credits
    });

  } catch (error) {
    console.error("Error resetting credits:", error);
    return NextResponse.json(
      { error: "An error occurred while resetting credits" },
      { status: 500 }
    );
  }
}

// Batch reset all eligible users
// This should be called by a scheduled task
export async function GET() {
  try {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Find all users whose credits were last reset more than 24 hours ago
    const usersToReset = await db.query.users.findMany({
      where: (users, { lt }) => lt(users.lastCreditReset, yesterday)
    });
    
    if (usersToReset.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: "No users need credit reset" 
      });
    }
    
    let updated = 0;
    
    // Update each user's credits
    for (const user of usersToReset) {
      const maxCredits = getMaxCredits(user.userType);
      
      await db.update(users)
        .set({ 
          credits: maxCredits,
          lastCreditReset: new Date(),
          updatedAt: new Date()
        })
        .where(eq(users.id, user.id));
      
      updated++;
    }
    
    return NextResponse.json({
      success: true,
      message: `Reset credits for ${updated} users`
    });
    
  } catch (error) {
    console.error("Error in batch reset credits:", error);
    return NextResponse.json(
      { error: "An error occurred during batch credit reset" },
      { status: 500 }
    );
  }
} 