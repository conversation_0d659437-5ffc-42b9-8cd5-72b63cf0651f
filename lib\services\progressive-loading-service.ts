import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { thumbnailCache, previewFrameCache } from "./cache-service";

export interface LoadingTask {
  id: string;
  type: LoadingTaskType;
  priority: LoadingPriority;
  assetId: string;
  url: string;
  metadata?: Record<string, unknown>;
  progress: number;
  status: LoadingStatus;
  startTime: number;
  endTime?: number;
  retryCount: number;
  maxRetries: number;
}

export type LoadingTaskType =
  | "thumbnail"
  | "preview_frame"
  | "video_metadata"
  | "audio_waveform"
  | "full_asset";

export type LoadingPriority = "low" | "medium" | "high" | "critical";
export type LoadingStatus =
  | "pending"
  | "loading"
  | "completed"
  | "failed"
  | "cancelled";

interface ProgressiveLoadingState {
  tasks: Map<string, LoadingTask>;
  activeLoads: Set<string>;
  maxConcurrentLoads: number;
  loadQueue: string[];

  // Actions
  addTask: (
    task: Omit<
      LoadingTask,
      "id" | "progress" | "status" | "startTime" | "retryCount"
    >
  ) => string;
  updateTask: (id: string, updates: Partial<LoadingTask>) => void;
  removeTask: (id: string) => void;
  processQueue: () => void;
  cancelTask: (id: string) => void;
  retryTask: (id: string) => void;
  getTasksByAsset: (assetId: string) => LoadingTask[];
  getTasksByType: (type: LoadingTaskType) => LoadingTask[];
  clearCompleted: () => void;
}

export const useProgressiveLoading = create<ProgressiveLoadingState>()(
  devtools((set, get) => ({
    tasks: new Map(),
    activeLoads: new Set(),
    maxConcurrentLoads: 6,
    loadQueue: [],

    addTask: (taskData) => {
      const id = crypto.randomUUID();
      const task: LoadingTask = {
        ...taskData,
        id,
        progress: 0,
        status: "pending",
        startTime: Date.now(),
        retryCount: 0,
      };

      set((state) => {
        const newTasks = new Map(state.tasks);
        newTasks.set(id, task);

        // Add to queue based on priority
        const newQueue = [...state.loadQueue];
        const insertIndex = newQueue.findIndex((queueId) => {
          const queueTask = newTasks.get(queueId);
          return (
            queueTask &&
            getPriorityValue(queueTask.priority) <
              getPriorityValue(task.priority)
          );
        });

        if (insertIndex === -1) {
          newQueue.push(id);
        } else {
          newQueue.splice(insertIndex, 0, id);
        }

        return {
          tasks: newTasks,
          loadQueue: newQueue,
        };
      });

      // Process queue
      get().processQueue();

      return id;
    },

    updateTask: (id, updates) => {
      set((state) => {
        const newTasks = new Map(state.tasks);
        const task = newTasks.get(id);

        if (task) {
          newTasks.set(id, { ...task, ...updates });
        }

        return { tasks: newTasks };
      });
    },

    removeTask: (id) => {
      set((state) => ({
        tasks: new Map([...state.tasks].filter(([taskId]) => taskId !== id)),
        activeLoads: new Set(
          [...state.activeLoads].filter((loadId) => loadId !== id)
        ),
        loadQueue: state.loadQueue.filter((queueId) => queueId !== id),
      }));
    },

    processQueue: () => {
      const state = get();

      while (
        state.activeLoads.size < state.maxConcurrentLoads &&
        state.loadQueue.length > 0
      ) {
        const taskId = state.loadQueue[0];
        const task = state.tasks.get(taskId);

        if (!task || task.status !== "pending") {
          // Remove invalid task from queue
          set((state) => ({
            loadQueue: state.loadQueue.slice(1),
          }));
          continue;
        }

        // Start loading
        set((state) => ({
          activeLoads: new Set([...state.activeLoads, taskId]),
          loadQueue: state.loadQueue.slice(1),
        }));

        state.updateTask(taskId, { status: "loading" });

        // Execute the loading task
        executeLoadingTask(task)
          .then(() => {
            state.updateTask(taskId, {
              status: "completed",
              progress: 100,
              endTime: Date.now(),
            });
          })
          .catch((error) => {
            console.error(`Loading task ${taskId} failed:`, error);

            if (task.retryCount < task.maxRetries) {
              // Retry the task
              state.retryTask(taskId);
            } else {
              state.updateTask(taskId, {
                status: "failed",
                endTime: Date.now(),
              });
            }
          })
          .finally(() => {
            set((state) => ({
              activeLoads: new Set(
                [...state.activeLoads].filter((id) => id !== taskId)
              ),
            }));

            // Process next task in queue
            setTimeout(() => state.processQueue(), 100);
          });
      }
    },

    cancelTask: (id) => {
      set((state) => {
        const newTasks = new Map(state.tasks);
        const task = newTasks.get(id);

        if (task) {
          newTasks.set(id, { ...task, status: "cancelled" });
        }

        return {
          tasks: newTasks,
          activeLoads: new Set(
            [...state.activeLoads].filter((loadId) => loadId !== id)
          ),
          loadQueue: state.loadQueue.filter((queueId) => queueId !== id),
        };
      });
    },

    retryTask: (id) => {
      const state = get();
      const task = state.tasks.get(id);

      if (task) {
        state.updateTask(id, {
          status: "pending",
          progress: 0,
          retryCount: task.retryCount + 1,
        });

        // Add back to queue
        set((state) => ({
          loadQueue: [id, ...state.loadQueue],
        }));

        state.processQueue();
      }
    },

    getTasksByAsset: (assetId) => {
      const state = get();
      return Array.from(state.tasks.values()).filter(
        (task) => task.assetId === assetId
      );
    },

    getTasksByType: (type) => {
      const state = get();
      return Array.from(state.tasks.values()).filter(
        (task) => task.type === type
      );
    },

    clearCompleted: () => {
      set((state) => {
        const newTasks = new Map();
        for (const [id, task] of state.tasks) {
          if (task.status !== "completed") {
            newTasks.set(id, task);
          }
        }
        return { tasks: newTasks };
      });
    },
  }))
);

// Helper functions
function getPriorityValue(priority: LoadingPriority): number {
  const values = { critical: 4, high: 3, medium: 2, low: 1 };
  return values[priority];
}

async function executeLoadingTask(task: LoadingTask): Promise<unknown> {
  switch (task.type) {
    case "thumbnail":
      return loadThumbnail(task);
    case "preview_frame":
      return loadPreviewFrame(task);
    case "video_metadata":
      return loadVideoMetadata(task);
    case "audio_waveform":
      return loadAudioWaveform(task);
    case "full_asset":
      return loadFullAsset(task);
    default:
      throw new Error(`Unknown task type: ${task.type}`);
  }
}

async function loadThumbnail(task: LoadingTask): Promise<string> {
  const response = await fetch(task.url);
  if (!response.ok) {
    throw new Error(`Failed to load thumbnail: ${response.statusText}`);
  }

  const blob = await response.blob();
  const url = URL.createObjectURL(blob);

  // Cache the thumbnail
  thumbnailCache.setThumbnail(task.assetId, "default", blob);

  return url;
}

async function loadPreviewFrame(task: LoadingTask): Promise<HTMLCanvasElement> {
  const video = document.createElement("video");
  video.crossOrigin = "anonymous";

  return new Promise((resolve, reject) => {
    video.onloadeddata = () => {
      const time = (task.metadata?.time as number) || 0;
      video.currentTime = time;
    };

    video.onseeked = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        reject(new Error("Failed to get canvas context"));
        return;
      }

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0);

      // Cache the frame
      previewFrameCache.setFrame(task.assetId, video.currentTime, canvas);

      resolve(canvas);
    };

    video.onerror = () => reject(new Error("Failed to load video"));
    video.src = task.url;
  });
}

async function loadVideoMetadata(task: LoadingTask): Promise<VideoMetadata> {
  const video = document.createElement("video");
  video.crossOrigin = "anonymous";

  return new Promise((resolve, reject) => {
    video.onloadedmetadata = () => {
      const metadata: VideoMetadata = {
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        fps: 30, // Default, would need more sophisticated detection
      };

      resolve(metadata);
    };

    video.onerror = () => reject(new Error("Failed to load video metadata"));
    video.src = task.url;
  });
}

async function loadAudioWaveform(task: LoadingTask): Promise<Float32Array> {
  const response = await fetch(task.url);
  if (!response.ok) {
    throw new Error(`Failed to load audio: ${response.statusText}`);
  }

  const arrayBuffer = await response.arrayBuffer();
  const audioContext = new AudioContext();
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

  // Generate waveform data
  const channelData = audioBuffer.getChannelData(0);
  const samples = 1000; // Number of waveform samples
  const blockSize = Math.floor(channelData.length / samples);
  const waveform = new Float32Array(samples);

  for (let i = 0; i < samples; i++) {
    let sum = 0;
    for (let j = 0; j < blockSize; j++) {
      sum += Math.abs(channelData[i * blockSize + j]);
    }
    waveform[i] = sum / blockSize;
  }

  return waveform;
}

async function loadFullAsset(task: LoadingTask): Promise<Blob> {
  const response = await fetch(task.url);
  if (!response.ok) {
    throw new Error(`Failed to load asset: ${response.statusText}`);
  }

  return response.blob();
}

interface VideoMetadata {
  duration: number;
  width: number;
  height: number;
  fps: number;
}

// Progressive loading manager
export class ProgressiveLoadingManager {
  private static instance: ProgressiveLoadingManager;

  static getInstance(): ProgressiveLoadingManager {
    if (!ProgressiveLoadingManager.instance) {
      ProgressiveLoadingManager.instance = new ProgressiveLoadingManager();
    }
    return ProgressiveLoadingManager.instance;
  }

  loadThumbnail(
    assetId: string,
    url: string,
    priority: LoadingPriority = "medium"
  ): string {
    return useProgressiveLoading.getState().addTask({
      type: "thumbnail",
      priority,
      assetId,
      url,
      maxRetries: 3,
    });
  }

  loadPreviewFrame(
    assetId: string,
    url: string,
    time: number,
    priority: LoadingPriority = "high"
  ): string {
    return useProgressiveLoading.getState().addTask({
      type: "preview_frame",
      priority,
      assetId,
      url,
      metadata: { time },
      maxRetries: 2,
    });
  }

  loadVideoMetadata(
    assetId: string,
    url: string,
    priority: LoadingPriority = "high"
  ): string {
    return useProgressiveLoading.getState().addTask({
      type: "video_metadata",
      priority,
      assetId,
      url,
      maxRetries: 3,
    });
  }

  preloadAssets(assetIds: string[], urls: string[]) {
    assetIds.forEach((assetId, index) => {
      const url = urls[index];
      if (url) {
        // Load thumbnail first
        this.loadThumbnail(assetId, url, "low");

        // Then metadata
        this.loadVideoMetadata(assetId, url, "low");
      }
    });
  }
}

// Hook for progressive loading
export function useProgressiveLoadingManager() {
  const progressiveLoading = useProgressiveLoading();
  const manager = ProgressiveLoadingManager.getInstance();

  return {
    ...progressiveLoading,
    manager,
  };
}

// Initialize progressive loading manager
export const progressiveLoadingManager =
  ProgressiveLoadingManager.getInstance();
