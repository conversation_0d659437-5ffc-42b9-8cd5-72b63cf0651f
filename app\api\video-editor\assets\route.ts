import { NextRequest, NextResponse } from "next/server";

// Mock data for development
const mockAssets: Array<{
  id: string;
  name: string;
  type: string;
  url: string;
  thumbnailUrl: string | null;
  duration: number;
  createdAt: string;
  updatedAt: string;
  projectId: string | null;
  folderId: string | null;
  metadata: Record<string, unknown>;
}> = [];

const mockFolders: Array<{
  id: string;
  name: string;
  parentId: string | null;
  projectId: string | null;
  createdAt: string;
  updatedAt: string;
}> = [];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get("projectId");

    // Filter by projectId if provided (for future implementation)
    const filteredAssets = projectId
      ? mockAssets.filter((a) => a.projectId === projectId)
      : mockAssets;

    // Return mock data for now
    return NextResponse.json({
      assets: filteredAssets,
      folders: mockFolders,
    });
  } catch (error) {
    console.error("Error loading assets:", error);
    return NextResponse.json(
      { error: "Failed to load assets" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Mock asset creation
    const newAsset = {
      id: `asset-${Date.now()}`,
      name: body.name || "New Asset",
      type: body.type || "video",
      url: body.url || "",
      thumbnailUrl: body.thumbnailUrl || "",
      duration: body.duration || 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      projectId: body.projectId,
      folderId: body.folderId || null,
      metadata: {
        fileSize: body.fileSize || 0,
        width: body.width || 1920,
        height: body.height || 1080,
        format: body.format || "mp4",
        ...body.metadata,
      },
    };

    mockAssets.push(newAsset);

    return NextResponse.json(newAsset);
  } catch (error) {
    console.error("Error creating asset:", error);
    return NextResponse.json(
      { error: "Failed to create asset" },
      { status: 500 }
    );
  }
}
