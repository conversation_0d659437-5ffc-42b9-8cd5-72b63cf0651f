import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ProjectService } from "@/lib/services/project-service";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const versionData = await ProjectService.loadProjectVersion(
      id,
      session.user.id
    );

    if (!versionData) {
      return NextResponse.json({ error: "Version not found" }, { status: 404 });
    }

    return NextResponse.json(versionData);
  } catch (error) {
    console.error("Error loading project version:", error);
    return NextResponse.json(
      { error: "Failed to load project version" },
      { status: 500 }
    );
  }
}
