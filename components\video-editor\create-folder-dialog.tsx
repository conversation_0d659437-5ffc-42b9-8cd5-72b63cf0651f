"use client";

import { useState } from "react";
import { useAssetStore } from "@/lib/stores/asset-store";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Folder, Loader2 } from "lucide-react";

interface CreateFolderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateFolderDialog({
  open,
  onOpenChange,
}: CreateFolderDialogProps) {
  const assetStore = useAssetStore();
  const [folderName, setFolderName] = useState("");
  const [parentFolderId, setParentFolderId] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const availableFolders = assetStore.folders.filter(
    (folder) => folder.id !== assetStore.currentFolderId
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!folderName.trim()) {
      return;
    }

    setIsCreating(true);

    try {
      await assetStore.createFolder(
        folderName.trim(),
        parentFolderId || assetStore.currentFolderId || undefined
      );

      // Reset form and close dialog
      setFolderName("");
      setParentFolderId(null);
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to create folder:", error);
      alert("Failed to create folder");
    } finally {
      setIsCreating(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isCreating) {
      onOpenChange(newOpen);
      if (!newOpen) {
        // Reset form when closing
        setFolderName("");
        setParentFolderId(null);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Folder className="w-5 h-5" />
            Create New Folder
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Create a new folder to organize your assets.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="folder-name" className="text-sm font-medium">
              Folder Name
            </Label>
            <Input
              id="folder-name"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              placeholder="Enter folder name..."
              className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
              disabled={isCreating}
              autoFocus
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="parent-folder" className="text-sm font-medium">
              Parent Folder (Optional)
            </Label>
            <Select
              value={parentFolderId || "root"}
              onValueChange={(value) =>
                setParentFolderId(value === "root" ? null : value)
              }
              disabled={isCreating}
            >
              <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="root" className="text-gray-300">
                  Root (No parent)
                </SelectItem>
                {assetStore.currentFolderId && (
                  <SelectItem
                    value={assetStore.currentFolderId}
                    className="text-gray-300"
                  >
                    Current Folder
                  </SelectItem>
                )}
                {availableFolders.map((folder) => (
                  <SelectItem
                    key={folder.id}
                    value={folder.id}
                    className="text-gray-300"
                  >
                    {folder.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isCreating}
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!folderName.trim() || isCreating}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isCreating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Folder"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
