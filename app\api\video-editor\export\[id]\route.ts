import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ExportService } from "@/lib/services/export-service";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const exportJob = ExportService.getExportJob(id);

    if (!exportJob) {
      return NextResponse.json(
        { error: "Export job not found" },
        { status: 404 }
      );
    }

    // Verify the user owns this export job
    if (exportJob.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    return NextResponse.json({
      id: exportJob.id,
      status: exportJob.status,
      progress: exportJob.progress,
      outputUrl: exportJob.outputUrl,
      errorMessage: exportJob.errorMessage,
      createdAt: exportJob.createdAt,
      completedAt: exportJob.completedAt,
    });
  } catch (error) {
    console.error("Error fetching export status:", error);
    return NextResponse.json(
      { error: "Failed to fetch export status" },
      { status: 500 }
    );
  }
}
