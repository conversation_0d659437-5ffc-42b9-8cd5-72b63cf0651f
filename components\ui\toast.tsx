"use client";

import React, { useEffect } from "react";
import { createPortal } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Loader2,
  X,
} from "lucide-react";
import { useToast, type Toast, type ToastType } from "@/lib/services/toast-service";

const toastIcons: Record<ToastType, React.ReactNode> = {
  success: <CheckCircle className="w-5 h-5 text-green-500" />,
  error: <XCircle className="w-5 h-5 text-red-500" />,
  warning: <AlertTriangle className="w-5 h-5 text-yellow-500" />,
  info: <Info className="w-5 h-5 text-blue-500" />,
  loading: <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />,
};

const toastStyles: Record<ToastType, string> = {
  success: "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20",
  error: "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20",
  warning: "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20",
  info: "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20",
  loading: "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20",
};

interface ToastItemProps {
  toast: Toast;
  onRemove: (id: string) => void;
}

function ToastItem({ toast, onRemove }: ToastItemProps) {
  const [isVisible, setIsVisible] = React.useState(false);
  const [isExiting, setIsExiting] = React.useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleRemove = () => {
    setIsExiting(true);
    setTimeout(() => onRemove(toast.id), 200);
  };

  return (
    <div
      className={cn(
        "relative flex items-start space-x-3 p-4 border rounded-lg shadow-lg transition-all duration-200 ease-in-out transform",
        toastStyles[toast.type],
        isVisible && !isExiting
          ? "translate-x-0 opacity-100"
          : "translate-x-full opacity-0",
        isExiting && "-translate-x-full opacity-0"
      )}
    >
      {/* Icon */}
      <div className="flex-shrink-0 mt-0.5">
        {toastIcons[toast.type]}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
          {toast.title}
        </div>
        {toast.description && (
          <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            {toast.description}
          </div>
        )}
        {toast.action && (
          <div className="mt-2">
            <Button
              size="sm"
              variant="outline"
              onClick={toast.action.action}
              className="text-xs"
            >
              {toast.action.label}
            </Button>
          </div>
        )}
      </div>

      {/* Close button */}
      {!toast.persistent && (
        <button
          onClick={handleRemove}
          className="flex-shrink-0 p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          aria-label="Close notification"
        >
          <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        </button>
      )}
    </div>
  );
}

export function ToastContainer() {
  const { toasts, removeToast } = useToast();
  const [mounted, setMounted] = React.useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  if (toasts.length === 0) {
    return null;
  }

  return createPortal(
    <div className="fixed top-4 right-4 z-50 flex flex-col space-y-2 max-w-sm w-full">
      {toasts.map((toast) => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onRemove={removeToast}
        />
      ))}
    </div>,
    document.body
  );
}

// Hook for using toast notifications
export function useToastNotifications() {
  const { addToast, removeToast, clearAllToasts } = useToast();

  return {
    toast: {
      success: (title: string, description?: string) =>
        addToast({ type: "success", title, description }),
      error: (title: string, description?: string) =>
        addToast({ type: "error", title, description }),
      warning: (title: string, description?: string) =>
        addToast({ type: "warning", title, description }),
      info: (title: string, description?: string) =>
        addToast({ type: "info", title, description }),
      loading: (title: string, description?: string) =>
        addToast({ type: "loading", title, description, persistent: true }),
    },
    dismiss: removeToast,
    clear: clearAllToasts,
  };
}
