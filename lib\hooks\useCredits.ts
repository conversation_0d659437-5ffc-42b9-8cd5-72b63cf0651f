import { useSession } from "next-auth/react";
import { useCallback, useState } from "react";

export function useCredits() {
  const { data: session, update: updateSession } = useSession();
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Get current credit count from session
  const credits = session?.user?.credits || 0;
  
  // Check if user has enough credits
  const hasCredits = credits > 0;
  
  // Force refresh credits from the API
  const refreshCredits = useCallback(async () => {
    try {
      setIsUpdating(true);
      const response = await fetch('/api/user/stats');
      
      if (response.ok) {
        const data = await response.json();
        
        if (data.stats?.credits !== undefined && session?.user) {
          await updateSession({
            user: { ...session.user, credits: data.stats.credits }
          });
          return data.stats.credits;
        }
      }
      
      return credits;
    } catch (error) {
      console.error('Error refreshing credits:', error);
      return credits;
    } finally {
      setIsUpdating(false);
    }
  }, [credits, session, updateSession]);
  
  // Update credits in session after a transaction
  const updateCredits = useCallback(async (newCredits: number) => {
    try {
      if (session?.user) {
        await updateSession({
          user: { ...session.user, credits: newCredits }
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating credits:', error);
      return false;
    }
  }, [session, updateSession]);
  
  return { 
    credits, 
    hasCredits, 
    isUpdating, 
    refreshCredits, 
    updateCredits 
  };
} 