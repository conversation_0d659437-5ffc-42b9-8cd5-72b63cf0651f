"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { <PERSON>rk<PERSON>, Type, Loader2 } from "lucide-react"
import { useState } from "react"

export default function TextToVideoPage() {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = () => {
    setIsGenerating(true);
    // Simulate API call
    setTimeout(() => {
      setIsGenerating(false);
    }, 3000);
  };

  return (
    <div className="w-full">
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Type className="w-5 h-5 text-purple-400" /> Text to Video
          </CardTitle>
          <CardDescription className="text-gray-400">
            Describe your video and generate it using AI.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <Label htmlFor="video-desc" className="text-gray-200 font-medium mb-1">
              Video Prompt
            </Label>
            <Textarea
              id="video-desc"
              placeholder="Describe your video..."
              className="min-h-28 bg-gray-800 border-gray-700 text-white"
            />
          </div>
          <div className="flex gap-4">
            <div className="flex-1">
              <Label className="text-gray-200 font-medium mb-1">Style</Label>
              <Input placeholder="e.g. Cinematic, Animated..." className="bg-gray-800 border-gray-700 text-white" />
            </div>
            <div className="flex-1">
              <Label className="text-gray-200 font-medium mb-1">Duration</Label>
              <Input placeholder="e.g. 30 seconds" className="bg-gray-800 border-gray-700 text-white" />
            </div>
          </div>
          <Button 
            className="w-full bg-purple-600 hover:bg-purple-700 text-white text-base h-12"
            onClick={handleGenerate}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" /> Generating...
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" /> Generate Video
              </>
            )}
          </Button>
          
          {isGenerating && (
            <div className="mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin text-purple-400" />
                <span className="text-gray-300">Processing your request...</span>
              </div>
              <div className="mt-2 h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                <div className="h-full bg-purple-600 rounded-full animate-pulse" style={{ width: "60%" }}></div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 