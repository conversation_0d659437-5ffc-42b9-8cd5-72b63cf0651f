import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
import Together from "together-ai";

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY,
});

export async function POST(request: Request) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const { prompt, aspectRatio } = await request.json();
    
    if (!prompt || prompt.trim() === "") {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }
    
    // Generate image using Together AI
    const response = await together.images.create({
      model: "black-forest-labs/FLUX.1-schnell-Free",
      prompt: prompt,
      width: aspectRatio === "1:1" ? 1024 : aspectRatio === "4:3" ? 1024 : 768,
      height: aspectRatio === "1:1" ? 1024 : aspectRatio === "4:3" ? 768 : 1024,
    });
    
    // Get image URL from response
    const imageUrl = response.data[0].url;
    
    return NextResponse.json({ 
      success: true,
      imageUrl: imageUrl
    });
    
  } catch (error: unknown) {
    console.error("Error generating image:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to generate image";
    return NextResponse.json({ 
      error: errorMessage 
    }, { status: 500 });
  }
} 