import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { TimelineData } from "@/types/video-editor";

interface UndoRedoAction {
  id: string;
  type: string;
  description: string;
  timestamp: Date;
  beforeState: TimelineData;
  afterState: TimelineData;
}

interface UndoRedoState {
  // History stacks
  undoStack: UndoRedoAction[];
  redoStack: UndoRedoAction[];

  // Current state tracking
  maxHistorySize: number;
  isRecording: boolean;

  // Actions
  pushAction: (action: Omit<UndoRedoAction, "id" | "timestamp">) => void;
  undo: () => TimelineData | null;
  redo: () => TimelineData | null;
  clearHistory: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;

  // Batch operations
  startBatch: () => void;
  endBatch: (description: string) => void;
  batchActions: UndoRedoAction[];
}

export const useUndoRedoStore = create<UndoRedoState>()(
  devtools((set, get) => ({
    // Initial state
    undoStack: [],
    redoStack: [],
    maxHistorySize: 50,
    isRecording: true,
    batchActions: [],

    // Push a new action to the undo stack
    pushAction: (actionData) => {
      const state = get();
      if (!state.isRecording) return;

      const action: UndoRedoAction = {
        ...actionData,
        id: crypto.randomUUID(),
        timestamp: new Date(),
      };

      set((state) => {
        const newUndoStack = [...state.undoStack, action];

        // Limit history size
        if (newUndoStack.length > state.maxHistorySize) {
          newUndoStack.shift();
        }

        return {
          undoStack: newUndoStack,
          redoStack: [], // Clear redo stack when new action is added
        };
      });
    },

    // Undo the last action
    undo: () => {
      const state = get();
      if (state.undoStack.length === 0) return null;

      const actionToUndo = state.undoStack[state.undoStack.length - 1];

      set((state) => ({
        undoStack: state.undoStack.slice(0, -1),
        redoStack: [...state.redoStack, actionToUndo],
      }));

      return actionToUndo.beforeState;
    },

    // Redo the last undone action
    redo: () => {
      const state = get();
      if (state.redoStack.length === 0) return null;

      const actionToRedo = state.redoStack[state.redoStack.length - 1];

      set((state) => ({
        undoStack: [...state.undoStack, actionToRedo],
        redoStack: state.redoStack.slice(0, -1),
      }));

      return actionToRedo.afterState;
    },

    // Clear all history
    clearHistory: () =>
      set({
        undoStack: [],
        redoStack: [],
        batchActions: [],
      }),

    // Check if undo is possible
    canUndo: () => get().undoStack.length > 0,

    // Check if redo is possible
    canRedo: () => get().redoStack.length > 0,

    // Start batch operation
    startBatch: () =>
      set({
        batchActions: [],
        isRecording: false,
      }),

    // End batch operation and create single undo action
    endBatch: (description) => {
      const state = get();
      if (state.batchActions.length === 0) {
        set({ isRecording: true });
        return;
      }

      const firstAction = state.batchActions[0];
      const lastAction = state.batchActions[state.batchActions.length - 1];

      const batchAction: UndoRedoAction = {
        id: crypto.randomUUID(),
        type: "batch",
        description,
        timestamp: new Date(),
        beforeState: firstAction.beforeState,
        afterState: lastAction.afterState,
      };

      set((state) => {
        const newUndoStack = [...state.undoStack, batchAction];

        if (newUndoStack.length > state.maxHistorySize) {
          newUndoStack.shift();
        }

        return {
          undoStack: newUndoStack,
          redoStack: [],
          batchActions: [],
          isRecording: true,
        };
      });
    },
  }))
);
