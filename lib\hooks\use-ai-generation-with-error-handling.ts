import { useCallback, useState } from "react";
import { useAssetStore } from "@/lib/stores/asset-store";
import {
  useError<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/lib/services/error-handling-service";
import { toast, createProgressToast } from "@/lib/services/toast-service";
import { loading } from "@/lib/services/loading-service";

interface GenerationResult {
  success: boolean;
  assetId?: string;
  error?: string;
  retryable?: boolean;
}

interface GenerationOptions {
  retryOnFailure?: boolean;
  maxRetries?: number;
  timeout?: number;
}

const DEFAULT_OPTIONS: GenerationOptions = {
  retryOnFailure: true,
  maxRetries: 3,
  timeout: 60000, // 60 seconds
};

export function useAIGenerationWithErrorHandling() {
  const assetStore = useAssetStore();
  const { reportError } = useErrorHandling();
  const [generating, setGenerating] = useState<Record<string, boolean>>({});

  const generateImage = useCallback(
    async (
      prompt: string,
      options: GenerationOptions = {}
    ): Promise<GenerationResult> => {
      const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
      const generationKey = `image-${prompt.slice(0, 50)}`;

      if (generating[generationKey]) {
        return {
          success: false,
          error: "Generation already in progress for this prompt",
        };
      }

      setGenerating((prev) => ({ ...prev, [generationKey]: true }));

      try {
        // Validate prompt
        if (!prompt.trim()) {
          throw new Error("Prompt cannot be empty");
        }

        if (prompt.length > 1000) {
          throw new Error("Prompt is too long (maximum 1000 characters)");
        }

        // Create progress toast
        const progressToast = createProgressToast(
          "Generating Image",
          "Creating your image with AI..."
        );

        // Start loading indicator
        const loadingId = loading.start("ai_generation", "Generating image...");

        let retryCount = 0;
        let lastError: Error | null = null;

        while (retryCount <= (mergedOptions.maxRetries || 0)) {
          try {
            progressToast.update(
              retryCount === 0 ? 20 : 20 + retryCount * 20,
              retryCount === 0
                ? "Sending request..."
                : `Retrying... (${retryCount}/${mergedOptions.maxRetries})`
            );

            const result = await generateImageWithAI(prompt, {
              timeout: mergedOptions.timeout,
              onProgress: (progress) => {
                progressToast.update(progress, "Generating image...");
                loading.update(loadingId, { progress });
              },
            });

            // Create a mock file for the asset store
            const blob = await fetch(result.url).then((r) => r.blob());
            const file = new File([blob], `ai-generated-${Date.now()}.png`, {
              type: "image/png",
            });

            // Add to asset store
            const asset = await assetStore.importAsset(file);
            const assetId = asset.id;

            progressToast.success("Image Generated", "Your AI image is ready!");
            loading.stop(loadingId);

            return { success: true, assetId };
          } catch (error) {
            lastError = error as Error;
            retryCount++;

            // Check if error is retryable
            const isRetryable = isRetryableError(lastError);

            if (!isRetryable || retryCount > (mergedOptions.maxRetries || 0)) {
              break;
            }

            // Wait before retry
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * retryCount)
            );
          }
        }

        // Handle final error
        if (lastError) {
          reportError(ErrorHandler.handleAIGenerationError(lastError, prompt));

          let errorMessage = lastError.message;
          let retryable = isRetryableError(lastError);

          if (lastError.message.includes("quota")) {
            errorMessage =
              "AI generation quota exceeded. Please try again later.";
            retryable = false;
          } else if (lastError.message.includes("content")) {
            errorMessage =
              "Content policy violation. Please modify your prompt.";
            retryable = false;
          } else if (lastError.message.includes("timeout")) {
            errorMessage = "Generation timed out. Please try again.";
            retryable = true;
          }

          progressToast.error("Generation Failed", errorMessage);
          loading.stop(loadingId);

          if (retryable && mergedOptions.retryOnFailure) {
            toast.error("Generation Failed", errorMessage, {
              label: "Retry",
              action: () => generateImage(prompt, options),
            });
          }

          return { success: false, error: errorMessage, retryable };
        }

        return { success: false, error: "Unknown error occurred" };
      } catch (error) {
        const err = error as Error;
        const errorId = reportError(
          ErrorHandler.handleAIGenerationError(err, prompt)
        );

        toast.error("Generation Error", "An unexpected error occurred", {
          label: "Dismiss",
          action: () => toast.dismiss(errorId),
        });

        return { success: false, error: err.message };
      } finally {
        setGenerating((prev) => ({ ...prev, [generationKey]: false }));
      }
    },
    [assetStore, reportError, generating]
  );

  const generateVideo = useCallback(
    async (
      prompt: string,
      options: GenerationOptions = {}
    ): Promise<GenerationResult> => {
      const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
      const generationKey = `video-${prompt.slice(0, 50)}`;

      if (generating[generationKey]) {
        return {
          success: false,
          error: "Generation already in progress for this prompt",
        };
      }

      setGenerating((prev) => ({ ...prev, [generationKey]: true }));

      try {
        // Validate prompt
        if (!prompt.trim()) {
          throw new Error("Prompt cannot be empty");
        }

        // Create progress toast
        const progressToast = createProgressToast(
          "Generating Video",
          "Creating your video with AI... This may take a few minutes."
        );

        // Start loading indicator
        const loadingId = loading.start("ai_generation", "Generating video...");

        const result = await generateVideoWithAI(prompt, {
          timeout: mergedOptions.timeout || 120000, // 2 minutes for video
          onProgress: (progress) => {
            progressToast.update(progress, "Generating video...");
            loading.update(loadingId, { progress });
          },
        });

        // Create a mock file for the asset store
        const blob = await fetch(result.url).then((r) => r.blob());
        const file = new File([blob], `ai-generated-video-${Date.now()}.mp4`, {
          type: "video/mp4",
        });

        // Add to asset store
        const asset = await assetStore.importAsset(file);
        const assetId = asset.id;

        progressToast.success("Video Generated", "Your AI video is ready!");
        loading.stop(loadingId);

        return { success: true, assetId };
      } catch (error) {
        const err = error as Error;
        const errorId = reportError(
          ErrorHandler.handleAIGenerationError(err, prompt)
        );

        let errorMessage = err.message;
        if (err.message.includes("quota")) {
          errorMessage =
            "Video generation quota exceeded. Please try again later.";
        } else if (err.message.includes("timeout")) {
          errorMessage =
            "Video generation timed out. Please try a shorter or simpler prompt.";
        }

        toast.error("Video Generation Failed", errorMessage, {
          label: "Dismiss",
          action: () => toast.dismiss(errorId),
        });

        return { success: false, error: errorMessage };
      } finally {
        setGenerating((prev) => ({ ...prev, [generationKey]: false }));
      }
    },
    [assetStore, reportError, generating]
  );

  const isGenerating = useCallback(
    (type?: "image" | "video") => {
      if (!type) {
        return Object.values(generating).some(Boolean);
      }
      return Object.keys(generating).some(
        (key) => key.startsWith(type) && generating[key]
      );
    },
    [generating]
  );

  return {
    generateImage,
    generateVideo,
    isGenerating,
  };
}

function isRetryableError(error: Error): boolean {
  const retryableMessages = [
    "timeout",
    "network",
    "server error",
    "rate limit",
    "temporary",
  ];

  return retryableMessages.some((msg) =>
    error.message.toLowerCase().includes(msg)
  );
}

// Mock AI generation functions - replace with actual implementations
async function generateImageWithAI(
  prompt: string,
  options: {
    timeout?: number;
    onProgress: (progress: number) => void;
  }
): Promise<{
  url: string;
  model: string;
  parameters: Record<string, unknown>;
}> {
  return new Promise((resolve, reject) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        setTimeout(() => {
          resolve({
            url: `https://picsum.photos/512/512?random=${Date.now()}`,
            model: "stable-diffusion-xl",
            parameters: {
              prompt,
              steps: 50,
              guidance: 7.5,
            },
          });
        }, 500);
      }
      options.onProgress(progress);
    }, 300);

    // Simulate failures
    if (Math.random() < 0.1) {
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error("AI service temporarily unavailable"));
      }, 3000);
    }

    // Timeout handling
    if (options.timeout) {
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error("Generation timeout"));
      }, options.timeout);
    }
  });
}

async function generateVideoWithAI(
  prompt: string,
  options: {
    timeout?: number;
    onProgress: (progress: number) => void;
  }
): Promise<{
  url: string;
  duration: number;
  model: string;
  parameters: Record<string, unknown>;
}> {
  return new Promise((resolve, reject) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 5; // Slower progress for video
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        setTimeout(() => {
          resolve({
            url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            duration: 10,
            model: "runway-gen2",
            parameters: {
              prompt,
              duration: 10,
              fps: 24,
            },
          });
        }, 1000);
      }
      options.onProgress(progress);
    }, 500);

    // Simulate failures
    if (Math.random() < 0.15) {
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error("Video generation quota exceeded"));
      }, 5000);
    }
  });
}
