import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { eq } from "drizzle-orm";
import { hash, compare } from "bcrypt";

export async function PUT(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await req.json();
    const { currentPassword, newPassword } = body;
    
    if (!currentPassword || !newPassword) {
      return NextResponse.json({ 
        error: "Current password and new password are required" 
      }, { status: 400 });
    }
    
    // Get the user from the database
    const user = await db.query.users.findFirst({
      where: (users) => eq(users.id, session.user.id)
    });
    
    if (!user || !user.password) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Verify the current password
    const isPasswordValid = await compare(currentPassword, user.password);
    
    if (!isPasswordValid) {
      return NextResponse.json({ error: "Current password is incorrect" }, { status: 400 });
    }
    
    // Hash the new password
    const hashedPassword = await hash(newPassword, 10);
    
    // Update the password in the database
    await db.update(users)
      .set({
        password: hashedPassword,
        updatedAt: new Date(),
      })
      .where(eq(users.id, session.user.id));
    
    return NextResponse.json({ 
      success: true,
      message: "Password changed successfully",
    });
    
  } catch (error) {
    console.error("Error changing password:", error);
    return NextResponse.json({ error: "Failed to change password" }, { status: 500 });
  }
} 