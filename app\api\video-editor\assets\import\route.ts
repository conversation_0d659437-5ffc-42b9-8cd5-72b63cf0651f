import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const projectId = formData.get("projectId") as string;
    const folderId = formData.get("folderId") as string;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Create a blob URL for the uploaded file
    const arrayBuffer = await file.arrayBuffer();
    const blob = new Blob([arrayBuffer], { type: file.type });
    const url = URL.createObjectURL(blob);

    // Determine asset type based on file type
    let assetType = "video";
    if (file.type.startsWith("audio/")) {
      assetType = "audio";
    } else if (file.type.startsWith("image/")) {
      assetType = "image";
    }

    // Get video/audio duration if possible
    let duration = 0;
    let width = 0;
    let height = 0;

    if (assetType === "video") {
      // For video files, we'll set default values
      // In a real implementation, you'd extract this metadata
      duration = 30; // Default 30 seconds
      width = 1920;
      height = 1080;
    } else if (assetType === "audio") {
      duration = 180; // Default 3 minutes for audio
    }

    const asset = {
      id: `asset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: file.name,
      type: assetType,
      url: url,
      thumbnailUrl: assetType === "image" ? url : null,
      duration: duration,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      projectId: projectId || null,
      folderId: folderId || null,
      metadata: {
        fileSize: file.size,
        width: width,
        height: height,
        format: file.name.split(".").pop()?.toLowerCase() || "unknown",
        originalName: file.name,
        mimeType: file.type,
      },
    };

    return NextResponse.json(asset);
  } catch (error) {
    console.error("Error importing asset:", error);
    return NextResponse.json(
      { error: "Failed to import asset" },
      { status: 500 }
    );
  }
}
