import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ProjectService } from "@/lib/services/project-service";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const versions = await ProjectService.getProjectVersions(
      id,
      session.user.id
    );

    return NextResponse.json(versions);
  } catch (error) {
    console.error("Error loading project versions:", error);
    return NextResponse.json(
      { error: "Failed to load project versions" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const { name } = await request.json();

    const version = await ProjectService.createProjectVersion(
      id,
      session.user.id,
      name
    );

    if (!version) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    return NextResponse.json(version);
  } catch (error) {
    console.error("Error creating project version:", error);
    return NextResponse.json(
      { error: "Failed to create project version" },
      { status: 500 }
    );
  }
}
