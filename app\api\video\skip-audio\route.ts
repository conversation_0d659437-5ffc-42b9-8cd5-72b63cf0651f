import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { videoGenerations } from "@/lib/schema";
import { eq } from "drizzle-orm";

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse request body
    const body = await req.json();
    const { videoId } = body;
    
    if (!videoId) {
      return NextResponse.json({ error: "Video ID is required" }, { status: 400 });
    }
    
    // Get the video from the database
    const [video] = await db
      .select()
      .from(videoGenerations)
      .where(eq(videoGenerations.id, videoId))
      .limit(1);
    
    if (!video) {
      return NextResponse.json({ error: "Video not found" }, { status: 404 });
    }
    
    // Check if the video belongs to the current user
    if (video.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Check if the video is in processing_audio status
    if (video.status !== "processing_audio") {
      return NextResponse.json({ error: "Video is not processing audio" }, { status: 400 });
    }
    
    // Update the video status to completed (skip audio)
    await db.update(videoGenerations)
      .set({ 
        status: "completed", 
        updatedAt: new Date() 
      })
      .where(eq(videoGenerations.id, videoId));
    
    return NextResponse.json({
      id: videoId,
      status: "completed",
      videoUrl: video.videoUrl,
      message: "Audio generation skipped"
    });
    
  } catch (error: unknown) {
    console.error("Error skipping audio:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    
    return NextResponse.json({ 
      error: "Failed to skip audio",
      message: errorMessage
    }, { status: 500 });
  }
} 