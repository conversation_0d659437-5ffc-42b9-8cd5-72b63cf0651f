"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlayerRef } from "@remotion/player";

import { Save, Keyboard, HelpCircle } from "lucide-react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { ToastContainer } from "@/components/ui/toast";
import { GlobalLoadingIndicator } from "@/components/ui/loading-indicators";
import { PerformanceDashboard } from "@/components/video-editor/performance-dashboard";
import { OptimizedTimeline } from "@/components/video-editor/optimized-timeline";
import { useAnalyticsManager } from "@/lib/services/analytics-service";
import { useMemoryManager } from "@/lib/services/memory-management-service";
import { setupWebVitalsTracking } from "@/lib/services/analytics-service";
import { ClipManager } from "@/components/video-editor/clip-manager";
import { RemotionPreview } from "@/components/video-editor/remotion-preview";
import { EffectsPanel } from "@/components/video-editor/effects-panel";
import { ExportDialog } from "@/components/video-editor/export-dialog";
import { KeyboardShortcutsDialog } from "@/components/video-editor/keyboard-shortcuts-dialog";
import {
  ShortcutsHelpOverlay,
  useShortcutsHelp,
  QuickShortcutsDisplay,
} from "@/components/video-editor/shortcuts-help-overlay";
import { useEnhancedShortcuts } from "@/lib/hooks/use-enhanced-shortcuts";
import type { Clip } from "@/types/video-editor";

export default function VideoEditorPage() {
  // Store hooks
  const timelineStore = useTimelineStore();
  const projectStore = useProjectStore();
  const assetStore = useAssetStore();

  // Local UI state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showShortcutsDialog, setShowShortcutsDialog] = useState(false);

  // Keyboard shortcuts
  const { showHelp, setShowHelp } = useShortcutsHelp();

  // Performance monitoring
  const { trackVideoEdit } = useAnalyticsManager();
  const memoryManager = useMemoryManager();

  // Refs
  const playerRef = useRef<PlayerRef>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize project if none exists
  const hasInitialized = useRef(false);

  useEffect(() => {
    const initializeProject = async () => {
      if (!hasInitialized.current) {
        hasInitialized.current = true;
        try {
          if (!projectStore.currentProject) {
            await projectStore.createProject(
              "Untitled Project",
              "Video editing session"
            );
          }
        } catch (error) {
          console.error("Failed to create project:", error);
          hasInitialized.current = false; // Reset on error so we can try again
        }
      }
    };

    initializeProject();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount - projectStore intentionally omitted to prevent infinite loop

  // Sync playhead with player
  useEffect(() => {
    if (playerRef.current && timelineStore.playheadPosition !== undefined) {
      playerRef.current.seekTo(timelineStore.playheadPosition);
    }
  }, [timelineStore.playheadPosition]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Import asset using the asset store
      const asset = await assetStore.importAsset(
        file,
        projectStore.currentProject?.id
      );

      // Create a video element to get the duration
      const video = document.createElement("video");
      video.src = asset.url;
      video.onloadedmetadata = async () => {
        const duration = video.duration;

        // Create or get the main video track
        let videoTrack = timelineStore.tracks.find(
          (track) => track.type === "video"
        );
        if (!videoTrack) {
          timelineStore.addTrack({
            type: "video",
            name: "Video Track",
            clips: [],
            muted: false,
            locked: false,
            height: 100,
          });
          videoTrack = timelineStore.tracks.find(
            (track) => track.type === "video"
          );
        }

        if (videoTrack) {
          // Add clip to timeline
          const newClip: Omit<Clip, "id"> = {
            assetId: asset.id,
            startTime: 0,
            endTime: duration,
            trimStart: 0,
            trimEnd: duration,
            effects: [],
            properties: {
              volume: 1,
              opacity: 1,
              scale: 1,
              rotation: 0,
              filters: {
                brightness: 100,
                contrast: 100,
                saturation: 100,
              },
            },
          };

          timelineStore.addClip(videoTrack.id, newClip);

          // Select the new clip
          const updatedTrack = timelineStore.getTrackById(videoTrack.id);
          if (updatedTrack && updatedTrack.clips.length > 0) {
            const addedClip = updatedTrack.clips[updatedTrack.clips.length - 1];
            timelineStore.selectClip(addedClip.id);
          }
        }

        // Update timeline duration
        timelineStore.setDuration(Math.max(duration, timelineStore.duration));
      };
    } catch (error) {
      console.error("Failed to import asset:", error);
      alert("Failed to import video file");
    }
  };

  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  const saveProject = async () => {
    try {
      if (projectStore.currentProject) {
        // Update project timeline with current timeline state
        projectStore.updateProject({
          timeline: {
            tracks: timelineStore.tracks,
            duration: timelineStore.duration,
            markers: timelineStore.markers,
          },
        });

        await projectStore.saveProject();
        alert("Project saved successfully!");
      }
    } catch (error) {
      console.error("Failed to save project:", error);
      alert("Failed to save project");
    }
  };

  const handleExportClick = () => {
    setShowExportDialog(true);
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleNewProject = async () => {
    if (confirm("Create a new project? Unsaved changes will be lost.")) {
      await projectStore.createProject("New Project", "Created from editor");
    }
  };

  // Initialize enhanced keyboard shortcuts
  useEnhancedShortcuts({
    onTogglePlayback: () =>
      timelineStore.setIsPlaying(!timelineStore.isPlaying),
    onSeekToStart: () => timelineStore.setPlayheadPosition(0),
    onSeekToEnd: () =>
      timelineStore.setPlayheadPosition(timelineStore.duration),
    onSave: saveProject,
    onExport: handleExportClick,
    onImport: handleImportClick,
    onNewProject: handleNewProject,
  });

  // Global shortcut for opening shortcuts dialog
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "k") {
        event.preventDefault();
        setShowShortcutsDialog(true);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Initialize performance monitoring
  useEffect(() => {
    setupWebVitalsTracking();

    // Track page load
    trackVideoEdit("editor_loaded", {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
    });

    // Monitor memory usage
    const memoryInterval = setInterval(() => {
      const usage = memoryManager.getMemoryUsage();
      if (usage.percentage > 90) {
        console.warn("High memory usage detected:", usage);
        memoryManager.runGarbageCollection();
      }
    }, 30000);

    return () => clearInterval(memoryInterval);
  }, [trackVideoEdit, memoryManager]);

  // These functions are now handled by the RemotionTimeline component
  // const handleClipSelect = (clipId: string) => {
  //   timelineStore.selectClip(clipId);
  // };

  // const handleClipDelete = (clipId: string) => {
  //   timelineStore.removeClip(clipId);
  // };

  // These are now handled by the RemotionPreview component
  // const videoStyles = {
  //   transform: `scale(${scale})`,
  //   filter: `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`,
  // };

  // const videoTracks = timelineStore.tracks.filter(
  //   (track) => track.type === "video"
  // );
  // const allClips = videoTracks.flatMap((track) =>
  //   track.clips.map((clip) => ({
  //     ...clip,
  //     trackId: track.id,
  //     asset: assetStore.getAssetById(clip.assetId),
  //   }))
  // );

  return (
    <ErrorBoundary>
      <div className="h-screen flex flex-col bg-gray-950">
        {/* Top Header Bar */}
        <div className="flex-shrink-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">Video Editor</h1>
              {projectStore.currentProject && (
                <span className="text-gray-400 text-sm">
                  {projectStore.currentProject.name}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={triggerFileUpload}
                size="sm"
                className="bg-purple-600 hover:bg-purple-700"
              >
                Upload Video
              </Button>
              <input
                type="file"
                accept="video/*"
                className="hidden"
                ref={fileInputRef}
                onChange={handleFileUpload}
              />
              <Button
                onClick={saveProject}
                disabled={projectStore.isSaving}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {projectStore.isSaving ? "Saving..." : "Save"}
              </Button>
              <Button
                onClick={handleExportClick}
                disabled={!projectStore.currentProject}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                Export Video
              </Button>
              <Button
                onClick={() => setShowShortcutsDialog(true)}
                size="sm"
                variant="outline"
                title="Keyboard Shortcuts (Ctrl+K)"
              >
                <Keyboard className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => setShowHelp(true)}
                size="sm"
                variant="outline"
                title="Help (?)"
              >
                <HelpCircle className="w-4 h-4" />
              </Button>
              <PerformanceDashboard />
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Sidebar - Asset Manager */}
          <div className="w-80 flex-shrink-0 bg-gray-900 border-r border-gray-700 overflow-y-auto">
            <ClipManager className="h-full" />
          </div>

          {/* Center Area - Preview and Timeline */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Video Preview */}
            <div className="flex-1 p-6 bg-gray-900">
              <RemotionPreview
                className="w-full h-full max-w-4xl mx-auto"
                width={1920}
                height={1080}
                fps={30}
              />
            </div>

            {/* Timeline */}
            <div className="flex-shrink-0 bg-gray-800 border-t border-gray-700">
              <OptimizedTimeline containerHeight={320} className="h-80" />
            </div>
          </div>

          {/* Right Sidebar - Effects Panel */}
          <EffectsPanel className="w-80 flex-shrink-0" />
        </div>

        {/* Status Bar */}
        <div className="flex-shrink-0 bg-gray-800 border-t border-gray-700 px-6 py-2">
          <div className="flex items-center justify-between">
            <QuickShortcutsDisplay />
            <div className="text-xs text-gray-400">
              Press{" "}
              <span className="font-mono bg-gray-700 px-1 rounded">?</span> for
              help
            </div>
          </div>
        </div>

        {/* Dialogs */}
        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          projectId={projectStore.currentProject?.id}
        />

        <KeyboardShortcutsDialog
          open={showShortcutsDialog}
          onOpenChange={setShowShortcutsDialog}
        />

        <ShortcutsHelpOverlay open={showHelp} onOpenChange={setShowHelp} />

        {/* Global Components */}
        <ToastContainer />
        <GlobalLoadingIndicator />
      </div>
    </ErrorBoundary>
  );
}
