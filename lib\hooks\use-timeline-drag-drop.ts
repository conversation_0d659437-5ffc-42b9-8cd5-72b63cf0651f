import { useCallback, useState, useRef } from "react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import type { Clip, Asset } from "@/types/video-editor";

interface DragState {
  isDragging: boolean;
  draggedItem: {
    type: "clip" | "asset";
    id: string;
    data: Clip | Asset;
  } | null;
  dragOffset: { x: number; y: number };
  originalPosition?: { trackId: string; time: number };
}

interface TimelineDropZone {
  trackId: string;
  time: number;
  isValid: boolean;
}

export function useTimelineDragDrop() {
  const timelineStore = useTimelineStore();
  const assetStore = useAssetStore();

  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedItem: null,
    dragOffset: { x: 0, y: 0 },
  });

  const [dropZone, setDropZone] = useState<TimelineDropZone | null>(null);
  const dragPreviewRef = useRef<HTMLDivElement>(null);

  // Start dragging a clip
  const startClipDrag = useCallback(
    (clipId: string, event: React.MouseEvent, trackId: string) => {
      const clip = timelineStore.getClipById(clipId);
      if (!clip) return;

      const rect = event.currentTarget.getBoundingClientRect();
      const offset = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      };

      setDragState({
        isDragging: true,
        draggedItem: {
          type: "clip",
          id: clipId,
          data: clip,
        },
        dragOffset: offset,
        originalPosition: {
          trackId,
          time: clip.startTime,
        },
      });

      // Select the clip being dragged
      timelineStore.selectClip(clipId);

      event.preventDefault();
    },
    [timelineStore]
  );

  // Start dragging an asset from the asset library
  const startAssetDrag = useCallback(
    (assetId: string, event: React.MouseEvent) => {
      const asset = assetStore.getAssetById(assetId);
      if (!asset) return;

      const rect = event.currentTarget.getBoundingClientRect();
      const offset = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      };

      setDragState({
        isDragging: true,
        draggedItem: {
          type: "asset",
          id: assetId,
          data: asset,
        },
        dragOffset: offset,
      });

      event.preventDefault();
    },
    [assetStore]
  );

  // Validate if a drop operation is valid
  const validateDrop = useCallback(
    (
      targetTrackId: string,
      targetTime: number,
      draggedItem: DragState["draggedItem"]
    ): boolean => {
      if (!draggedItem) return false;

      const targetTrack = timelineStore.getTrackById(targetTrackId);
      if (!targetTrack) return false;

      // Check track type compatibility
      if (draggedItem.type === "asset") {
        const asset = draggedItem.data as Asset;
        if (asset.type === "video" && targetTrack.type !== "video")
          return false;
        if (asset.type === "audio" && targetTrack.type !== "audio")
          return false;
        if (asset.type === "image" && targetTrack.type !== "video")
          return false;
      }

      // Check for overlapping clips
      const draggedDuration =
        draggedItem.type === "clip"
          ? (draggedItem.data as Clip).endTime -
            (draggedItem.data as Clip).startTime
          : (draggedItem.data as Asset).duration || 5;

      const wouldOverlap = targetTrack.clips.some((clip) => {
        // Skip the clip being dragged if it's the same clip
        if (draggedItem.type === "clip" && clip.id === draggedItem.id) {
          return false;
        }

        return (
          targetTime < clip.endTime &&
          targetTime + draggedDuration > clip.startTime
        );
      });

      return !wouldOverlap;
    },
    [timelineStore]
  );

  // Handle drag over timeline
  const handleDragOver = useCallback(
    (
      event: React.DragEvent | MouseEvent,
      timelineRect: DOMRect,
      pixelsPerSecond: number,
      trackHeight: number,
      timelinePadding: number
    ) => {
      if (!dragState.isDragging) return;

      const x = event.clientX - timelineRect.left;
      const y = event.clientY - timelineRect.top;

      // Calculate time position
      const time = Math.max(0, (x - timelinePadding) / pixelsPerSecond);

      // Calculate track index
      const trackIndex = Math.floor((y - 32) / trackHeight); // 32px for ruler
      const track = timelineStore.tracks[trackIndex];

      if (track) {
        // Check if drop is valid
        const isValid = validateDrop(track.id, time, dragState.draggedItem);

        setDropZone({
          trackId: track.id,
          time,
          isValid,
        });
      } else {
        setDropZone(null);
      }

      event.preventDefault();
    },
    [dragState, timelineStore.tracks, validateDrop]
  );

  // Handle drop
  const handleDrop = useCallback(() => {
    if (
      !dragState.isDragging ||
      !dragState.draggedItem ||
      !dropZone ||
      !dropZone.isValid
    ) {
      // Reset drag state
      setDragState({
        isDragging: false,
        draggedItem: null,
        dragOffset: { x: 0, y: 0 },
      });
      setDropZone(null);
      return;
    }

    const { draggedItem } = dragState;
    const { trackId, time } = dropZone;

    if (draggedItem.type === "clip") {
      const clip = draggedItem.data as Clip;
      const duration = clip.endTime - clip.startTime;

      // Move existing clip
      timelineStore.moveClip(draggedItem.id, trackId, time);
      timelineStore.updateClip(draggedItem.id, {
        startTime: time,
        endTime: time + duration,
      });
    } else if (draggedItem.type === "asset") {
      const asset = draggedItem.data as Asset;
      const duration = asset.duration || 5;

      // Create new clip from asset
      const newClip: Omit<Clip, "id"> = {
        assetId: asset.id,
        startTime: time,
        endTime: time + duration,
        trimStart: 0,
        trimEnd: duration,
        effects: [],
        properties: {
          volume: asset.type === "audio" ? 1 : undefined,
          opacity: 1,
          scale: 1,
          rotation: 0,
          filters: {
            brightness: 100,
            contrast: 100,
            saturation: 100,
          },
        },
      };

      timelineStore.addClip(trackId, newClip);

      // Update timeline duration if necessary
      const newDuration = time + duration;
      if (newDuration > timelineStore.duration) {
        timelineStore.setDuration(newDuration);
      }
    }

    // Reset drag state
    setDragState({
      isDragging: false,
      draggedItem: null,
      dragOffset: { x: 0, y: 0 },
    });
    setDropZone(null);
  }, [dragState, dropZone, timelineStore]);

  // Cancel drag
  const cancelDrag = useCallback(() => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      dragOffset: { x: 0, y: 0 },
    });
    setDropZone(null);
  }, []);

  // Get drag preview style
  const getDragPreviewStyle = useCallback(
    (event: MouseEvent) => {
      if (!dragState.isDragging) return {};

      return {
        position: "fixed" as const,
        left: event.clientX - dragState.dragOffset.x,
        top: event.clientY - dragState.dragOffset.y,
        pointerEvents: "none" as const,
        zIndex: 1000,
        opacity: 0.8,
      };
    },
    [dragState]
  );

  return {
    dragState,
    dropZone,
    startClipDrag,
    startAssetDrag,
    handleDragOver,
    handleDrop,
    cancelDrag,
    getDragPreviewStyle,
    dragPreviewRef,
  };
}
