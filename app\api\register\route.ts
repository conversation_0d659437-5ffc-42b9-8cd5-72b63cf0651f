import { NextResponse } from "next/server";
import { hash } from "bcrypt";
import { db } from "@/lib/db";
import { users } from "@/lib/schema";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

// Define schema for validation
const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  userType: z.enum(["basic", "unlimited", "agency-basic", "agency-deluxe", "admin"]).default("basic"),
});

// Function to get initial credits based on user type
function getInitialCredits(userType: string): number {
  switch (userType) {
    case "basic":
      return 15;
    case "unlimited":
      return 25;
    case "agency-basic":
    case "agency-deluxe":
      return 15;
    case "admin":
      return 15;
    default:
      return 15;
  }
}

export async function POST(request: Request) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input data
    const result = registerSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.flatten() },
        { status: 400 }
      );
    }
    
    const { name, email, password, userType } = result.data;
    
    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: (users, { eq }) => eq(users.email, email)
    });
    
    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }
    
    // Hash password
    const hashedPassword = await hash(password, 12);
    
    // Determine initial credits based on user type
    const initialCredits = getInitialCredits(userType);
    
    // Create user
    const userId = uuidv4();
    await db.insert(users).values({
      id: userId,
      name,
      email,
      password: hashedPassword,
      userType: userType,
      credits: initialCredits,
      lastCreditReset: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    
    // Return success response with auth token
    return NextResponse.json(
      { 
        success: true, 
        message: "User registered successfully",
        user: { id: userId, name, email, userType: userType, credits: initialCredits }
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "An error occurred during registration" },
      { status: 500 }
    );
  }
}
