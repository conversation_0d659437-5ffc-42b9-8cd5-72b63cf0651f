"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  Table, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableBody, 
  TableCell 
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, <PERSON>r<PERSON><PERSON>, Refresh<PERSON>w } from "lucide-react";
import { format } from "date-fns";
import { z } from "zod";

type User = {
  id: string;
  name: string | null;
  email: string | null;
  userType: string | null;
  credits: number | null;
  createdAt: string;
};

export default function UsersPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  
  // Add user dialog state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    password: "",
    userType: "basic"
  });
  const [isAddingUser, setIsAddingUser] = useState(false);
  
  // Edit user dialog state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editUserForm, setEditUserForm] = useState({
    name: "",
    email: "",
    userType: "basic",
    credits: 0
  });
  const [isUpdatingUser, setIsUpdatingUser] = useState(false);
  
  // Delete confirmation dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [isDeletingUser, setIsDeletingUser] = useState(false);

  // Reset credits confirmation dialog state
  const [isResetCreditsDialogOpen, setIsResetCreditsDialogOpen] = useState(false);
  const [isResettingCredits, setIsResettingCredits] = useState(false);
  
  // Add state to track which user is being processed
  const [processingUserId, setProcessingUserId] = useState<string | null>(null);
  
  // Check if user is admin
  useEffect(() => {
    if (session?.user?.userType !== "admin") {
      router.push("/dashboard");
    }
  }, [session, router]);
  
  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch("/api/admin/users");
        if (!response.ok) {
          throw new Error("Failed to fetch users");
        }
        const data = await response.json();
        setUsers(data.users);
        setLoading(false);
      } catch (error) {
        setError(error instanceof Error ? error.message : "An error occurred");
        setLoading(false);
      }
    };
    
    if (session?.user?.userType === "admin") {
      fetchUsers();
    }
  }, [session]);
  
  const handleAddUser = async () => {
    try {
      setIsAddingUser(true);
      // Client-side validation
      const schema = z.object({
        name: z.string().min(2, "Name must be at least 2 characters"),
        email: z.string().email("Please enter a valid email address"),
        password: z.string().min(8, "Password must be at least 8 characters"),
        userType: z.enum(["basic", "unlimited", "agency-basic", "agency-deluxe", "admin"])
      });
      
      schema.parse(newUser);
      
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newUser),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to create user");
      }
      
      const data = await response.json();
      setUsers([...users, data.user]);
      setIsAddDialogOpen(false);
      setNewUser({
        name: "",
        email: "",
        password: "",
        userType: "basic"
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to create user");
    } finally {
      setIsAddingUser(false);
    }
  };
  
  const handleEditUser = async () => {
    if (!editingUser) return;
    
    try {
      setIsUpdatingUser(true);
      setProcessingUserId(editingUser.id);
      
      const response = await fetch(`/api/admin/users/${editingUser.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(editUserForm),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to update user");
      }
      
      const data = await response.json();
      setUsers(users.map(user => user.id === editingUser.id ? data.user : user));
      setIsEditDialogOpen(false);
      setEditingUser(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to update user");
    } finally {
      setIsUpdatingUser(false);
      setProcessingUserId(null);
    }
  };
  
  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    
    try {
      setIsDeletingUser(true);
      setProcessingUserId(userToDelete);
      
      const response = await fetch(`/api/admin/users/${userToDelete}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to delete user");
      }
      
      setUsers(users.filter(user => user.id !== userToDelete));
      setIsDeleteDialogOpen(false);
      setUserToDelete(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to delete user");
    } finally {
      setIsDeletingUser(false);
      setProcessingUserId(null);
    }
  };

  const handleResetAllCredits = async () => {
    try {
      setIsResettingCredits(true);
      const response = await fetch("/api/cron/reset-credits", {
        method: "POST",
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to reset credits");
      }
      
      // Refetch users to show updated credits
      const fetchResponse = await fetch("/api/admin/users");
      if (!fetchResponse.ok) {
        throw new Error("Failed to refetch users after resetting credits");
      }
      const data = await fetchResponse.json();
      setUsers(data.users);
      setIsResetCreditsDialogOpen(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred while resetting credits");
    } finally {
      setIsResettingCredits(false);
    }
  };
  
  const openEditDialog = (user: User) => {
    setEditingUser(user);
    setEditUserForm({
      name: user.name || "",
      email: user.email || "",
      userType: user.userType || "basic",
      credits: user.credits || 0
    });
    setIsEditDialogOpen(true);
  };
  
  const openDeleteDialog = (userId: string) => {
    setUserToDelete(userId);
    setIsDeleteDialogOpen(true);
  };
  
  if (session?.user?.userType !== "admin") {
    return null;
  }
  
  const userTypeColorMap: Record<string, string> = {
    "basic": "bg-blue-600",
    "unlimited": "bg-green-600",
    "agency-basic": "bg-amber-600",
    "agency-deluxe": "bg-purple-600",
    "admin": "bg-red-600"
  };
  
  const filteredUsers = users.filter(user => 
    user.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-white">User Management</h1>
        <div className="flex items-center gap-4">
          <Input
            type="text"
            placeholder="Search by email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-gray-800 border-gray-700 text-white w-64"
          />
          <Button
            className="bg-red-600 hover:bg-red-700 text-white flex gap-2 items-center"
            onClick={() => setIsResetCreditsDialogOpen(true)}
            disabled={isResettingCredits}
          >
            <RefreshCw className="w-4 h-4" />
            {isResettingCredits ? "Resetting..." : "Reset All Credits"}
          </Button>
          <Button 
            className="bg-purple-600 hover:bg-purple-700 text-white flex gap-2 items-center"
            onClick={() => setIsAddDialogOpen(true)}
          >
            <UserPlus className="w-4 h-4" />
            Add User
          </Button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500 text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full"></div>
        </div>
      ) : (
        <div className="border border-gray-800 rounded-lg overflow-x-auto">
          <Table className="w-full table-fixed">
            <TableHeader className="bg-gray-900">
              <TableRow className="border-gray-800">
                <TableHead className="text-white w-1/6">Name</TableHead>
                <TableHead className="text-white w-1/4">Email</TableHead>
                <TableHead className="text-white w-1/6">User Type</TableHead>
                <TableHead className="text-white w-1/12">Credits</TableHead>
                <TableHead className="text-white w-1/6">Created At</TableHead>
                <TableHead className="text-white text-right w-1/6">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-400">
                    {searchQuery ? `No users found for "${searchQuery}"` : "No users found"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id} className="border-gray-800">
                    <TableCell className="text-white font-medium truncate">{user.name}</TableCell>
                    <TableCell className="text-gray-300 truncate">{user.email}</TableCell>
                    <TableCell>
                      <Badge className={`${userTypeColorMap[user.userType || "basic"] || "bg-gray-600"}`}>
                        {user.userType?.toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-gray-300">{user.credits}</TableCell>
                    <TableCell className="text-gray-300">
                      {user.createdAt ? format(new Date(user.createdAt), "MMM d, yyyy") : "N/A"}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          className="bg-purple-600 hover:bg-purple-700"
                          onClick={() => openEditDialog(user)}
                          disabled={processingUserId === user.id}
                        >
                          {processingUserId === user.id ? (
                            <span className="animate-spin">⏳</span>
                          ) : (
                            <Edit className="w-4 h-4 text-gray-300" />
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="bg-purple-600 hover:bg-purple-700 hover:text-red-400"
                          onClick={() => openDeleteDialog(user.id)}
                          disabled={processingUserId === user.id}
                        >
                          {processingUserId === user.id ? (
                            <span className="animate-spin">⏳</span>
                          ) : (
                            <Trash2 className="w-4 h-4 text-gray-300" />
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}
      
      {/* Reset Credits Confirmation Dialog */}
      <AlertDialog open={isResetCreditsDialogOpen} onOpenChange={setIsResetCreditsDialogOpen}>
        <AlertDialogContent className="bg-gray-900 text-white border-gray-800">
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              This action will reset the credits for ALL users to their default values. This cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-gray-700 hover:bg-gray-600"
              disabled={isResettingCredits}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={handleResetAllCredits}
              disabled={isResettingCredits}
            >
              {isResettingCredits ? (
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              ) : null}
              {isResettingCredits ? "Resetting..." : "Confirm Reset"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add User Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !isAddingUser && setIsAddDialogOpen(open)}>
        <DialogContent className="bg-gray-900 text-white border-gray-800">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription className="text-gray-400">
              Create a new user account with specified permissions.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={newUser.name}
                onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="userType">User Type</Label>
              <Select 
                value={newUser.userType} 
                onValueChange={(value) => setNewUser({ ...newUser, userType: value })}
              >
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select user type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="unlimited">Unlimited</SelectItem>
                  <SelectItem value="agency-basic">Agency Basic</SelectItem>
                  <SelectItem value="agency-deluxe">Agency Deluxe</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddDialogOpen(false)}
              className="border-gray-700 bg-purple-600 hover:bg-purple-700"
              disabled={isAddingUser}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleAddUser}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={isAddingUser}
            >
              {isAddingUser ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Creating...
                </>
              ) : (
                "Create User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !isUpdatingUser && setIsEditDialogOpen(open)}>
        <DialogContent className="bg-gray-900 text-white border-gray-800">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription className="text-gray-400">
              Update user details and permissions.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Full Name</Label>
              <Input
                id="edit-name"
                value={editUserForm.name}
                onChange={(e) => setEditUserForm({ ...editUserForm, name: e.target.value })}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editUserForm.email}
                onChange={(e) => setEditUserForm({ ...editUserForm, email: e.target.value })}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-userType">User Type</Label>
              <Select 
                value={editUserForm.userType} 
                onValueChange={(value) => setEditUserForm({ ...editUserForm, userType: value })}
              >
                <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                  <SelectValue placeholder="Select user type" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-700 text-white">
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="unlimited">Unlimited</SelectItem>
                  <SelectItem value="agency-basic">Agency Basic</SelectItem>
                  <SelectItem value="agency-deluxe">Agency Deluxe</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-credits">Credits</Label>
              <Input
                id="edit-credits"
                type="number"
                value={editUserForm.credits}
                onChange={(e) => setEditUserForm({ ...editUserForm, credits: parseInt(e.target.value) || 0 })}
                className="bg-gray-800 border-gray-700 text-white"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsEditDialogOpen(false)}
              className="border-gray-700 bg-purple-600 hover:bg-purple-700"
              disabled={isUpdatingUser}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleEditUser}
              className="bg-purple-600 hover:bg-purple-700"
              disabled={isUpdatingUser}
            >
              {isUpdatingUser ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={(open) => !isDeletingUser && setIsDeleteDialogOpen(open)}>
        <AlertDialogContent className="bg-gray-900 text-white border-gray-800">
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              Are you sure you want to delete this user? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              className="border-gray-700 bg-purple-600 hover:bg-purple-700"
              disabled={isDeletingUser}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteUser}
              className="bg-red-600 hover:bg-red-700"
              disabled={isDeletingUser}
            >
              {isDeletingUser ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 