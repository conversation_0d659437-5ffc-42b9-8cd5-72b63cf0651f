import { useCallback, useEffect } from "react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useUndoRedoStore } from "@/lib/stores/undo-redo-store";

export function useTimelineUndoRedo() {
  const timelineStore = useTimelineStore();
  const undoRedoStore = useUndoRedoStore();

  // Undo the last action
  const undo = useCallback(() => {
    const previousState = undoRedoStore.undo();
    if (previousState) {
      // Restore the timeline state
      timelineStore.setTimeline(previousState);
      timelineStore.clearSelection();
    }
  }, [timelineStore, undoRedoStore]);

  // Redo the last undone action
  const redo = useCallback(() => {
    const nextState = undoRedoStore.redo();
    if (nextState) {
      // Restore the timeline state
      timelineStore.setTimeline(nextState);
      timelineStore.clearSelection();
    }
  }, [timelineStore, undoRedoStore]);

  // Keyboard shortcuts for undo/redo
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if we're in an input field
      const target = event.target as HTMLElement;
      if (
        target.tagName === "INPUT" ||
        target.tagName === "TEXTAREA" ||
        target.isContentEditable
      ) {
        return;
      }

      const isCtrlOrCmd = event.ctrlKey || event.metaKey;

      if (isCtrlOrCmd && event.key === "z" && !event.shiftKey) {
        event.preventDefault();
        undo();
      } else if (
        isCtrlOrCmd &&
        (event.key === "y" || (event.key === "z" && event.shiftKey))
      ) {
        event.preventDefault();
        redo();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [undo, redo]);

  return {
    undo,
    redo,
    canUndo: undoRedoStore.canUndo(),
    canRedo: undoRedoStore.canRedo(),
    clearHistory: undoRedoStore.clearHistory,
  };
}
