import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ExportService } from "@/lib/services/export-service";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's export jobs
    const exportJobs = ExportService.getUserExportJobs(session.user.id);

    // Clean up old jobs (older than 24 hours)
    ExportService.cleanupOldJobs();

    return NextResponse.json({
      exports: exportJobs.map((job) => ({
        id: job.id,
        projectId: job.projectId,
        settings: job.settings,
        status: job.status,
        progress: job.progress,
        outputUrl: job.outputUrl,
        errorMessage: job.errorMessage,
        createdAt: job.createdAt,
        completedAt: job.completedAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching export history:", error);
    return NextResponse.json(
      { error: "Failed to fetch export history" },
      { status: 500 }
    );
  }
}
