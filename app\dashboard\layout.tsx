"use client";

import { CreditDisplay } from "@/components/credit-display";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useCreditStore } from "@/lib/stores/creditStore";
import clsx from "clsx";
import {
  ImageIcon,
  Image as ImageLucide,
  LogOut,
  Menu,
  Scissors,
  Settings,
  Sparkles,
  Type,
  Users,
  Video,
  X,
} from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push(`/login?callbackUrl=${encodeURIComponent(pathname)}`);
    },
  });
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const fetchCredits = useCreditStore((state) => state.fetchCredits);

  // Initialize credit store when dashboard loads
  useEffect(() => {
    if (session?.user) {
      fetchCredits();
    }
  }, [session, fetchCredits]);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    setIsMobileSidebarOpen(false);
  }, [pathname]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar");
      if (
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        isMobileSidebarOpen
      ) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileSidebarOpen]);

  const handleSignOut = async () => {
    await signOut({ redirect: true, callbackUrl: "/" });
  };

  // Dynamic navigation items based on user type
  const navItems = useMemo(() => {
    const items = [
      { label: "Dashboard", path: "/dashboard", icon: Sparkles },
      { label: "Text to Video", path: "/dashboard/video", icon: Type },
      {
        label: "Text to Image",
        path: "/dashboard/text-to-image",
        icon: ImageLucide,
      },
      { label: "Image to Video", path: "/dashboard/image", icon: ImageIcon },
      { label: "Video Editor", path: "/dashboard/editor", icon: Scissors },
      { label: "My Videos", path: "/dashboard/myvideos", icon: Video },
      { label: "Settings", path: "/dashboard/settings", icon: Settings },
    ];

    // Add Users management for admin users
    if (session?.user?.userType === "admin") {
      items.splice(items.length - 1, 0, {
        label: "Users",
        path: "/dashboard/users",
        icon: Users,
      });
    }

    // Add User Access Control for agency users
    if (
      session?.user?.userType === "agency-basic" ||
      session?.user?.userType === "agency-deluxe"
    ) {
      items.splice(items.length - 1, 0, {
        label: "User Access",
        path: "/dashboard/user-access",
        icon: Users,
      });
    }

    return items;
  }, [session]);

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-950">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        <span className="ml-3 text-white">Loading...</span>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-950">
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-20 bg-gray-900 border-b border-gray-800 flex items-center justify-between px-4 py-3">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-300"
            onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
          >
            <Menu className="w-6 h-6" />
          </Button>
          <Link href="/" className="flex items-center">
            <Image
              src="/logo-w.png"
              alt="FLOWY AI Logo"
              width={64}
              height={64}
            />
          </Link>
        </div>
        <div className="flex items-center gap-3">
          <div className="hidden sm:block">
            <CreditDisplay variant="compact" />
          </div>
          <Avatar className="w-8 h-8">
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback className="bg-purple-600 text-white">
              {session?.user?.name?.charAt(0)}
              {session?.user?.name?.charAt(1)}
            </AvatarFallback>
          </Avatar>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <div className="lg:hidden fixed inset-0 bg-black/50 z-30" />
      )}

      {/* Sidebar - Desktop (always visible) & Mobile (toggleable) */}
      <aside
        id="mobile-sidebar"
        className={clsx(
          "bg-gray-900 border-r border-gray-800 flex flex-col h-screen z-40",
          "lg:w-64 lg:sticky lg:top-0 lg:left-0",
          "fixed top-0 left-0 w-64 transition-transform duration-300 ease-in-out",
          isMobileSidebarOpen
            ? "translate-x-0"
            : "-translate-x-full lg:translate-x-0"
        )}
      >
        {/* Mobile Close Button */}
        <div className="lg:hidden flex justify-between items-center px-4 py-3 border-b border-gray-800">
          <Link href="/" className="flex items-center">
            <Image
              src="/logo-w.png"
              alt="FLOWY AI Logo"
              width={64}
              height={64}
            />
          </Link>
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-300"
            onClick={() => setIsMobileSidebarOpen(false)}
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Desktop Logo - Hidden on Mobile */}
        <div className="hidden lg:flex items-center gap-2 px-6 py-5 border-b border-gray-800">
          <Link href="/" className="flex items-center gap-2">
            <div className="ml-5">
              <Image
                src="/logo-w.png"
                alt="FLOWY AI Logo"
                width={96}
                height={96}
              />
            </div>
          </Link>
        </div>

        {/* Navigation - Scrollable if needed */}
        <nav className="flex-grow overflow-y-auto py-6 px-4 space-y-1">
          {navItems.map((item) => {
            const isActive =
              item.path === "/dashboard"
                ? pathname === "/dashboard"
                : pathname.startsWith(item.path);

            return (
              <Link
                key={item.path}
                href={item.path}
                className={clsx(
                  "flex items-center w-full px-3 py-2 rounded-lg text-sm font-medium transition-colors gap-3",
                  isActive
                    ? "bg-purple-700 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                )}
              >
                <item.icon className="w-5 h-5" />
                {item.label}
              </Link>
            );
          })}
        </nav>

        {/* Credits display */}
        <div className="p-4 border-t border-gray-800">
          <CreditDisplay showProgress showReset />
        </div>

        {/* User profile - Fixed at bottom */}
        <div className="p-4 border-t border-gray-800 mt-auto">
          <div className="flex items-center gap-3 mb-3">
            <Avatar className="w-9 h-9">
              <AvatarImage src="/placeholder.svg" />
              <AvatarFallback className="bg-purple-600 text-white">
                {session?.user?.name?.charAt(0)}
                {session?.user?.name?.charAt(1)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-white font-medium text-sm">
                {session?.user?.name}
              </p>
              <div className="flex items-center space-x-2">
                <Badge className="bg-green-600 text-white text-xs">
                  {session?.user?.userType?.toUpperCase()}
                </Badge>
              </div>
            </div>
          </div>
          <Button
            variant="outline"
            className="w-full border-gray-700 bg-purple-600 hover:bg-purple-700"
            onClick={handleSignOut}
          >
            <LogOut className="w-4 h-4 mr-2 " />
            Sign Out
          </Button>
        </div>
      </aside>

      {/* Main Content - Scrollable */}
      <main className="flex-1 overflow-y-auto bg-gray-950 lg:pt-0 pt-16">
        <div className="w-full max-w-4xl mx-auto py-6 px-4">{children}</div>
      </main>
    </div>
  );
}
