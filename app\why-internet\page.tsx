"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Service {
  name: string;
  feature: string;
}

interface WhyInternetData {
  reason: string;
  services: Service[];
}

export default function WhyInternetPage() {
  const [data, setData] = useState<WhyInternetData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await fetch("/api/why-internet");
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-2xl w-full">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-gray-900">Why Does This App Need an Internet Connection?</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading...</p>
          ) : data ? (
            <div>
              <p className="text-lg text-gray-700 mb-6">{data.reason}</p>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Services We Use:</h3>
              <div className="space-y-4">
                {data.services.map((service, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold">{service.name}</span>
                      <Badge>{service.feature}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p>Failed to load information. Please try again later.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}