"use client"

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, CheckCircle2, Loader2, Settings } from "lucide-react"
import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"

export default function SettingsPage() {
  const { data: session, update } = useSession();
  
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  
  const [isUpdating, setIsUpdating] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [passwordSuccess, setPasswordSuccess] = useState("");
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);
  const [deleteError, setDeleteError] = useState("");

  // Initialize form with user data
  useEffect(() => {
    if (session?.user) {
      setName(session.user.name || "");
      setEmail(session.user.email || "");
    }
  }, [session]);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccessMessage("");
    setIsUpdating(true);
    
    try {
      const response = await fetch("/api/user/update", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          email,
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to update profile");
      }
      
      // Update the session to reflect the changes
      await update({
        ...session,
        user: {
          ...session?.user,
          name,
          email,
        },
      });
      
      setSuccessMessage("Profile updated successfully");
    } catch (err) {
      console.error("Error updating profile:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError("");
    setPasswordSuccess("");
    
    // Validate passwords
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }
    
    setIsChangingPassword(true);
    
    try {
      const response = await fetch("/api/user/change-password", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to change password");
      }
      
      setPasswordSuccess("Password changed successfully");
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (err) {
      console.error("Error changing password:", err);
      setPasswordError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsChangingPassword(false);
    }
  };

  // Get user initials for avatar
  const getInitials = () => {
    if (!session?.user?.name) return "U";
    return session.user.name
      .split(" ")
      .map(part => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const handleDeleteAccount = async (password: string) => {
    setIsDeletingAccount(true);
    setDeleteError("");
    
    try {
      const response = await fetch("/api/user/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ password }),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to delete account");
      }
      
      // Redirect to home page after successful deletion
      window.location.href = "/";
    } catch (err) {
      console.error("Error deleting account:", err);
      setDeleteError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsDeletingAccount(false);
    }
  };

  return (
    <div className="w-full">
      <Card className="bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="w-5 h-5 text-purple-400" /> Settings
          </CardTitle>
          <CardDescription className="text-gray-400">
            Manage your account preferences and settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid grid-cols-2 mb-6">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>
            
            {/* Profile Tab */}
            <TabsContent value="profile">
              <form onSubmit={handleUpdateProfile} className="space-y-6">
                <div className="flex items-center gap-4">
                  <Avatar className="w-20 h-20">
                    <AvatarFallback className="bg-purple-600 text-white text-xl">{getInitials()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-white font-medium">{session?.user?.name || "User"}</p>
                    <p className="text-gray-400 text-sm">{session?.user?.email}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-gray-200 font-medium mb-1">
                      Full Name
                    </Label>
                    <Input 
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white" 
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-gray-200 font-medium mb-1">
                      Email
                    </Label>
                    <Input 
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white" 
                    />
                  </div>
                </div>
                
                {error && (
                  <div className="flex items-center gap-2 text-red-500 text-sm bg-red-950/30 p-3 rounded-md">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
                
                {successMessage && (
                  <div className="flex items-center gap-2 text-green-500 text-sm bg-green-950/30 p-3 rounded-md">
                    <CheckCircle2 className="h-4 w-4" />
                    <span>{successMessage}</span>
                  </div>
                )}
                
                <Button 
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" /> Updating...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </form>
            </TabsContent>
            
            {/* Security Tab */}
            <TabsContent value="security">
              <form onSubmit={handleChangePassword} className="space-y-6">
                <h3 className="text-white font-medium mb-4">Change Password</h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="currentPassword" className="text-gray-200 font-medium mb-1">
                      Current Password
                    </Label>
                    <Input 
                      id="currentPassword"
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white" 
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="newPassword" className="text-gray-200 font-medium mb-1">
                      New Password
                    </Label>
                    <Input 
                      id="newPassword"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white" 
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="confirmPassword" className="text-gray-200 font-medium mb-1">
                      Confirm New Password
                    </Label>
                    <Input 
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white" 
                      required
                    />
                  </div>
                </div>
                
                {passwordError && (
                  <div className="flex items-center gap-2 text-red-500 text-sm bg-red-950/30 p-3 rounded-md">
                    <AlertCircle className="h-4 w-4" />
                    <span>{passwordError}</span>
                  </div>
                )}
                
                {passwordSuccess && (
                  <div className="flex items-center gap-2 text-green-500 text-sm bg-green-950/30 p-3 rounded-md">
                    <CheckCircle2 className="h-4 w-4" />
                    <span>{passwordSuccess}</span>
                  </div>
                )}
                
                <Button 
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                  disabled={isChangingPassword}
                >
                  {isChangingPassword ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" /> Updating...
                    </>
                  ) : (
                    "Change Password"
                  )}
                </Button>
                
                <div className="pt-6 border-t border-gray-800">
                  <h3 className="text-red-500 font-medium mb-2">Danger Zone</h3>
                  <p className="text-gray-400 text-sm mb-4">
                    Once you delete your account, there is no going back. Please be certain.
                  </p>
                  
                  {deleteError && (
                    <div className="flex items-center gap-2 text-red-500 text-sm bg-red-950/30 p-3 rounded-md mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <span>{deleteError}</span>
                    </div>
                  )}
                  
                  <Button 
                    type="button"
                    variant="destructive"
                    disabled={isDeletingAccount}
                    onClick={() => {
                      const password = prompt("Enter your password to confirm account deletion:");
                      if (password) {
                        handleDeleteAccount(password);
                      }
                    }}
                  >
                    {isDeletingAccount ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" /> Deleting...
                      </>
                    ) : (
                      "Delete Account"
                    )}
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
} 