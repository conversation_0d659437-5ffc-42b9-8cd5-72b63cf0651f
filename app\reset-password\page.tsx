import { ResetPasswordForm } from "@/components/reset-password-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import { Suspense } from "react";

export default function ResetPasswordPage() {
  return (
    <div className="auth-container flex min-h-screen flex-col items-center justify-center bg-gray-100 px-4 dark:bg-gray-900 sm:px-6 lg:px-8">
      <Card className="auth-card w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Image src="/logo.png" alt="FLOWY AI Logo" width={96} height={96} />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Reset Password
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            Create a new password for your account
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <Suspense
            fallback={<div className="text-center">Loading form...</div>}
          >
            <ResetPasswordForm />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
