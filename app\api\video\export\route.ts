import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { v4 as uuidv4 } from "uuid";
import fs from "fs";
import path from "path";
import os from "os";
import { exec } from "child_process";
import { promisify } from "util";

// Promisify exec for easier use with async/await
const execAsync = promisify(exec);

// Temporary directory for processing videos
const TMP_DIR = path.join(os.tmpdir(), 'veo-video-processing');

// Ensure temp directory exists
if (!fs.existsSync(TMP_DIR)) {
  fs.mkdirSync(TMP_DIR, { recursive: true });
}

// Check if FFmpeg is installed
async function isFFmpegInstalled() {
  try {
    await execAsync('ffmpeg -version');
    return true;
  } catch (error) {
    console.error("FFmpeg is not installed:", error);
    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Parse the form data
    const formData = await req.formData();
    const videoFile = formData.get('video');
    const audioFile = formData.get('audio');
    
    // Get editing parameters
    const trimStart = parseFloat(formData.get('trimStart') as string) || 0;
    const trimEnd = parseFloat(formData.get('trimEnd') as string) || 0;
    const scale = parseFloat(formData.get('scale') as string) || 1;
    const brightness = parseFloat(formData.get('brightness') as string) || 100;
    const contrast = parseFloat(formData.get('contrast') as string) || 100;
    const saturation = parseFloat(formData.get('saturation') as string) || 100;
    const volume = parseFloat(formData.get('volume') as string) || 100;
    
    if (!videoFile || !(videoFile instanceof Blob)) {
      return NextResponse.json({ error: "No video file provided" }, { status: 400 });
    }
    
    // Generate a unique ID for this render
    const renderId = uuidv4();
    
    // Check if FFmpeg is installed
    const ffmpegAvailable = await isFFmpegInstalled();
    
    if (!ffmpegAvailable) {
      console.log("FFmpeg is not installed. Returning the original video file.");
      
      // If FFmpeg is not available, just return the original video file
      const videoBuffer = Buffer.from(await videoFile.arrayBuffer());
      
      return new NextResponse(videoBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="video-${renderId}.mp4"`,
        },
      });
    }
    
    // Create temporary file paths
    const inputVideoPath = path.join(TMP_DIR, `input-${renderId}.mp4`);
    const outputVideoPath = path.join(TMP_DIR, `output-${renderId}.mp4`);
    const tempAudioPath = audioFile ? path.join(TMP_DIR, `audio-${renderId}.mp3`) : null;
    
    // Write the video file to disk
    const videoBuffer = Buffer.from(await videoFile.arrayBuffer());
    fs.writeFileSync(inputVideoPath, videoBuffer);
    
    // Write the audio file to disk if provided
    if (audioFile && audioFile instanceof Blob) {
      const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
      fs.writeFileSync(tempAudioPath!, audioBuffer);
    }
    
    try {
      // Calculate brightness adjustment for FFmpeg (convert from percentage to factor)
      const brightnessValue = (brightness - 100) / 100;
      
      // Build the FFmpeg filter string - ensure dimensions are even numbers
      const filterString = `scale=trunc(iw*${scale})/2*2:trunc(ih*${scale})/2*2,eq=brightness=${brightnessValue}:contrast=${contrast/100}:saturation=${saturation/100}`;
      
      // Build the FFmpeg command
      let ffmpegCommand = `ffmpeg -i "${inputVideoPath}" -ss ${trimStart} -to ${trimEnd}`;
      
      // Add filters
      ffmpegCommand += ` -vf "${filterString}"`;
      
      // Add volume adjustment
      ffmpegCommand += ` -af "volume=${volume/100}"`;
      
      // Add audio file if provided
      if (tempAudioPath && fs.existsSync(tempAudioPath)) {
        ffmpegCommand += ` -i "${tempAudioPath}" -map 0:v -map 1:a`;
      }
      
      // Add output options
      ffmpegCommand += ` -c:v libx264 -preset fast -crf 22 -c:a aac -b:a 128k "${outputVideoPath}"`;
      
      // Execute the FFmpeg command
      console.log(`Executing FFmpeg command: ${ffmpegCommand}`);
      await execAsync(ffmpegCommand);
      
      // Check if the output file exists
      if (!fs.existsSync(outputVideoPath)) {
        throw new Error("Failed to generate output video");
      }
      
      // Read the processed video
      const processedVideo = fs.readFileSync(outputVideoPath);
      
      // Clean up temporary files
      fs.unlinkSync(inputVideoPath);
      fs.unlinkSync(outputVideoPath);
      if (tempAudioPath && fs.existsSync(tempAudioPath)) fs.unlinkSync(tempAudioPath);
      
      // Return the processed video as a downloadable file
      return new NextResponse(processedVideo, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="edited-video-${renderId}.mp4"`,
        },
      });
      
    } catch (ffmpegError) {
      console.error("FFmpeg error:", ffmpegError);
      
      // Clean up temporary files on error
      if (fs.existsSync(inputVideoPath)) fs.unlinkSync(inputVideoPath);
      if (fs.existsSync(outputVideoPath)) fs.unlinkSync(outputVideoPath);
      if (tempAudioPath && fs.existsSync(tempAudioPath)) fs.unlinkSync(tempAudioPath);
      
      console.log("FFmpeg processing failed. Returning the original video as fallback.");
      
      // If FFmpeg processing fails, return the original video as a fallback
      const originalVideoBuffer = Buffer.from(await videoFile.arrayBuffer());
      
      return new NextResponse(originalVideoBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="video-${renderId}.mp4"`,
        },
      });
    }
    
  } catch (error) {
    console.error("Error exporting video:", error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to export video" 
    }, { status: 500 });
  }
} 