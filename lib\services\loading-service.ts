import React from "react";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface LoadingState {
  id: string;
  type: LoadingType;
  message: string;
  progress?: number;
  cancellable?: boolean;
  onCancel?: () => void;
  timestamp: Date;
}

export type LoadingType =
  | "media_import"
  | "ai_generation"
  | "rendering"
  | "export"
  | "saving"
  | "loading"
  | "processing";

interface LoadingServiceState {
  loadingStates: LoadingState[];
  globalLoading: boolean;

  // Actions
  startLoading: (loading: Omit<LoadingState, "id" | "timestamp">) => string;
  updateLoading: (id: string, updates: Partial<LoadingState>) => void;
  stopLoading: (id: string) => void;
  stopAllLoading: () => void;
  isLoading: (type?: LoadingType) => boolean;
  getLoadingState: (id: string) => LoadingState | undefined;
  getLoadingByType: (type: LoadingType) => LoadingState[];
}

export const useLoadingService = create<LoadingServiceState>()(
  devtools((set, get) => ({
    loadingStates: [],
    globalLoading: false,

    startLoading: (loadingData) => {
      const id = crypto.randomUUID();
      const loading: LoadingState = {
        ...loadingData,
        id,
        timestamp: new Date(),
      };

      set((state) => ({
        loadingStates: [...state.loadingStates, loading],
        globalLoading: true,
      }));

      return id;
    },

    updateLoading: (id, updates) => {
      set((state) => ({
        loadingStates: state.loadingStates.map((loading) =>
          loading.id === id ? { ...loading, ...updates } : loading
        ),
      }));
    },

    stopLoading: (id) => {
      set((state) => {
        const newLoadingStates = state.loadingStates.filter(
          (loading) => loading.id !== id
        );
        return {
          loadingStates: newLoadingStates,
          globalLoading: newLoadingStates.length > 0,
        };
      });
    },

    stopAllLoading: () => {
      set({
        loadingStates: [],
        globalLoading: false,
      });
    },

    isLoading: (type) => {
      const state = get();
      if (!type) {
        return state.globalLoading;
      }
      return state.loadingStates.some((loading) => loading.type === type);
    },

    getLoadingState: (id) => {
      return get().loadingStates.find((loading) => loading.id === id);
    },

    getLoadingByType: (type) => {
      return get().loadingStates.filter((loading) => loading.type === type);
    },
  }))
);

// Loading utilities
export const loading = {
  start: (
    type: LoadingType,
    message: string,
    options?: {
      progress?: number;
      cancellable?: boolean;
      onCancel?: () => void;
    }
  ) => {
    return useLoadingService.getState().startLoading({
      type,
      message,
      ...options,
    });
  },

  update: (id: string, updates: Partial<LoadingState>) => {
    useLoadingService.getState().updateLoading(id, updates);
  },

  stop: (id: string) => {
    useLoadingService.getState().stopLoading(id);
  },

  stopAll: () => {
    useLoadingService.getState().stopAllLoading();
  },

  isLoading: (type?: LoadingType) => {
    return useLoadingService.getState().isLoading(type);
  },

  withLoading: async <T>(
    type: LoadingType,
    message: string,
    operation: () => Promise<T>,
    options?: {
      onProgress?: (progress: number) => void;
      cancellable?: boolean;
    }
  ): Promise<T> => {
    const loadingId = loading.start(type, message, {
      cancellable: options?.cancellable,
    });

    try {
      const result = await operation();
      loading.stop(loadingId);
      return result;
    } catch (error) {
      loading.stop(loadingId);
      throw error;
    }
  },

  withProgress: async <T>(
    type: LoadingType,
    message: string,
    operation: (
      updateProgress: (progress: number, newMessage?: string) => void
    ) => Promise<T>
  ): Promise<T> => {
    const loadingId = loading.start(type, message, { progress: 0 });

    const updateProgress = (progress: number, newMessage?: string) => {
      loading.update(loadingId, {
        progress: Math.max(0, Math.min(100, progress)),
        message: newMessage || message,
      });
    };

    try {
      const result = await operation(updateProgress);
      loading.stop(loadingId);
      return result;
    } catch (error) {
      loading.stop(loadingId);
      throw error;
    }
  },
};

// Hook for component-level loading states
export function useComponentLoading() {
  const [localLoading, setLocalLoading] = React.useState<
    Record<string, boolean>
  >({});

  const startLoading = (key: string) => {
    setLocalLoading((prev) => ({ ...prev, [key]: true }));
  };

  const stopLoading = (key: string) => {
    setLocalLoading((prev) => ({ ...prev, [key]: false }));
  };

  const isLoading = (key: string) => {
    return localLoading[key] || false;
  };

  const withLoading = async <T>(
    key: string,
    operation: () => Promise<T>
  ): Promise<T> => {
    startLoading(key);
    try {
      const result = await operation();
      stopLoading(key);
      return result;
    } catch (error) {
      stopLoading(key);
      throw error;
    }
  };

  return {
    startLoading,
    stopLoading,
    isLoading,
    withLoading,
    loadingStates: localLoading,
  };
}
