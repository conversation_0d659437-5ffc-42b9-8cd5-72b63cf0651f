import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface PerformanceMetrics {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  category: PerformanceCategory;
  metadata?: Record<string, unknown>;
}

export type PerformanceCategory =
  | "rendering"
  | "timeline"
  | "asset_loading"
  | "export"
  | "ui_interaction"
  | "memory"
  | "network";

interface PerformanceState {
  metrics: PerformanceMetrics[];
  isMonitoring: boolean;
  memoryUsage: {
    used: number;
    total: number;
    timestamp: number;
  }[];
  frameRate: number[];

  // Actions
  startMeasurement: (
    name: string,
    category: PerformanceCategory,
    metadata?: Record<string, unknown>
  ) => string;
  endMeasurement: (id: string) => void;
  recordMetric: (metric: Omit<PerformanceMetrics, "id">) => void;
  updateMemoryUsage: () => void;
  updateFrameRate: (fps: number) => void;
  getMetricsByCategory: (category: PerformanceCategory) => PerformanceMetrics[];
  getAverageMetric: (name: string) => number;
  clearMetrics: () => void;
  exportMetrics: () => string;
}

export const usePerformanceService = create<PerformanceState>()(
  devtools((set, get) => ({
    metrics: [],
    isMonitoring: true,
    memoryUsage: [],
    frameRate: [],

    startMeasurement: (name, category, metadata) => {
      const id = crypto.randomUUID();
      const metric: PerformanceMetrics = {
        id,
        name,
        startTime: performance.now(),
        category,
        metadata,
      };

      set((state) => ({
        metrics: [...state.metrics, metric],
      }));

      return id;
    },

    endMeasurement: (id) => {
      const endTime = performance.now();

      set((state) => ({
        metrics: state.metrics.map((metric) =>
          metric.id === id
            ? {
                ...metric,
                endTime,
                duration: endTime - metric.startTime,
              }
            : metric
        ),
      }));
    },

    recordMetric: (metricData) => {
      const metric: PerformanceMetrics = {
        ...metricData,
        id: crypto.randomUUID(),
      };

      set((state) => ({
        metrics: [...state.metrics, metric],
      }));
    },

    updateMemoryUsage: () => {
      if ("memory" in performance) {
        const memory = (
          performance as unknown as {
            memory: { usedJSHeapSize: number; totalJSHeapSize: number };
          }
        ).memory;
        const usage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          timestamp: Date.now(),
        };

        set((state) => ({
          memoryUsage: [...state.memoryUsage.slice(-100), usage], // Keep last 100 measurements
        }));
      }
    },

    updateFrameRate: (fps) => {
      set((state) => ({
        frameRate: [...state.frameRate.slice(-60), fps], // Keep last 60 measurements
      }));
    },

    getMetricsByCategory: (category) => {
      return get().metrics.filter((metric) => metric.category === category);
    },

    getAverageMetric: (name) => {
      const metrics = get().metrics.filter(
        (metric) => metric.name === name && metric.duration !== undefined
      );

      if (metrics.length === 0) return 0;

      const total = metrics.reduce(
        (sum, metric) => sum + (metric.duration || 0),
        0
      );
      return total / metrics.length;
    },

    clearMetrics: () => {
      set({
        metrics: [],
        memoryUsage: [],
        frameRate: [],
      });
    },

    exportMetrics: () => {
      const state = get();
      return JSON.stringify(
        {
          metrics: state.metrics,
          memoryUsage: state.memoryUsage,
          frameRate: state.frameRate,
          timestamp: Date.now(),
        },
        null,
        2
      );
    },
  }))
);

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private frameCount = 0;
  private lastFrameTime = 0;
  private animationFrameId: number | null = null;

  private constructor() {
    this.startFrameRateMonitoring();
    this.startMemoryMonitoring();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private startFrameRateMonitoring() {
    const measureFrameRate = (currentTime: number) => {
      if (this.lastFrameTime) {
        const delta = currentTime - this.lastFrameTime;
        const fps = 1000 / delta;

        this.frameCount++;

        // Update FPS every 60 frames
        if (this.frameCount % 60 === 0) {
          usePerformanceService.getState().updateFrameRate(fps);
        }
      }

      this.lastFrameTime = currentTime;
      this.animationFrameId = requestAnimationFrame(measureFrameRate);
    };

    this.animationFrameId = requestAnimationFrame(measureFrameRate);
  }

  private startMemoryMonitoring() {
    // Update memory usage every 5 seconds
    setInterval(() => {
      usePerformanceService.getState().updateMemoryUsage();
    }, 5000);
  }

  stopMonitoring() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  // Measure function execution time
  static measure<T>(
    name: string,
    category: PerformanceCategory,
    fn: () => T,
    metadata?: Record<string, unknown>
  ): T {
    const { startMeasurement, endMeasurement } =
      usePerformanceService.getState();
    const id = startMeasurement(name, category, metadata);

    try {
      const result = fn();
      endMeasurement(id);
      return result;
    } catch (error) {
      endMeasurement(id);
      throw error;
    }
  }

  // Measure async function execution time
  static async measureAsync<T>(
    name: string,
    category: PerformanceCategory,
    fn: () => Promise<T>,
    metadata?: Record<string, unknown>
  ): Promise<T> {
    const { startMeasurement, endMeasurement } =
      usePerformanceService.getState();
    const id = startMeasurement(name, category, metadata);

    try {
      const result = await fn();
      endMeasurement(id);
      return result;
    } catch (error) {
      endMeasurement(id);
      throw error;
    }
  }
}

// Hook for performance monitoring
export function usePerformanceMonitoring() {
  const performanceService = usePerformanceService();

  const measureFunction = <T>(
    name: string,
    category: PerformanceCategory,
    fn: () => T,
    metadata?: Record<string, unknown>
  ): T => {
    return PerformanceMonitor.measure(name, category, fn, metadata);
  };

  const measureAsyncFunction = async <T>(
    name: string,
    category: PerformanceCategory,
    fn: () => Promise<T>,
    metadata?: Record<string, unknown>
  ): Promise<T> => {
    return PerformanceMonitor.measureAsync(name, category, fn, metadata);
  };

  return {
    ...performanceService,
    measure: measureFunction,
    measureAsync: measureAsyncFunction,
  };
}

// Initialize performance monitoring
export const performanceMonitor = PerformanceMonitor.getInstance();
