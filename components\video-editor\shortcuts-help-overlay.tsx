"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Keyboard,
  Play,
  Scissors,
  Copy,
  Move,
  ZoomIn,
  Settings,
} from "lucide-react";
import {
  useKeyboardShortcuts,
  formatShortcut,
} from "@/lib/services/keyboard-shortcut-service";

interface ShortcutsHelpOverlayProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ShortcutsHelpOverlay({
  open,
  onOpenChange,
}: ShortcutsHelpOverlayProps) {
  const { getShortcutsByCategory } = useKeyboardShortcuts();

  const categoryIcons = {
    Playback: <Play className="w-4 h-4" />,
    Selection: <Move className="w-4 h-4" />,
    Clipboard: <Copy className="w-4 h-4" />,
    Editing: <Scissors className="w-4 h-4" />,
    Timeline: <ZoomIn className="w-4 h-4" />,
    Project: <Settings className="w-4 h-4" />,
  };

  const categoryDescriptions = {
    Playback: "Control video playback and navigation",
    Selection: "Select and manage clips",
    Clipboard: "Copy, cut, paste, and duplicate operations",
    Editing: "Edit clips and timeline operations",
    Timeline: "Navigate and zoom the timeline",
    Project: "Project-level operations",
  };

  const renderShortcutGroup = (categoryName: string) => {
    const categoryShortcuts = getShortcutsByCategory(categoryName).filter(
      (shortcut) => shortcut.enabled
    );

    if (categoryShortcuts.length === 0) return null;

    return (
      <div key={categoryName} className="space-y-3">
        <div className="flex items-center space-x-2">
          {categoryIcons[categoryName as keyof typeof categoryIcons]}
          <h3 className="font-semibold text-lg">{categoryName}</h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          {
            categoryDescriptions[
              categoryName as keyof typeof categoryDescriptions
            ]
          }
        </p>

        <div className="grid grid-cols-1 gap-2">
          {categoryShortcuts.map((shortcut) => (
            <div
              key={shortcut.id}
              className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm">{shortcut.name}</div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {shortcut.description}
                </div>
              </div>
              <Badge variant="outline" className="ml-3 font-mono">
                {formatShortcut(shortcut.keys)}
              </Badge>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="w-5 h-5" />
            Keyboard Shortcuts Reference
          </DialogTitle>
          <DialogDescription>
            Quick reference for all available keyboard shortcuts. Press{" "}
            <Badge variant="outline" className="mx-1">
              ?
            </Badge>{" "}
            anytime to show this help.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 max-h-[70vh]">
          <div className="space-y-6 pr-4">
            {Object.keys(categoryIcons).map((categoryName) => (
              <React.Fragment key={categoryName}>
                {renderShortcutGroup(categoryName)}
                {categoryName !== "Project" && <Separator />}
              </React.Fragment>
            ))}
          </div>
        </ScrollArea>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Tip: You can customize these shortcuts in Settings → Keyboard
            Shortcuts
          </div>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Global shortcut to show help overlay
export function useShortcutsHelp() {
  const [showHelp, setShowHelp] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Show help on ? key (Shift + /)
      if (
        event.key === "?" &&
        !event.ctrlKey &&
        !event.metaKey &&
        !event.altKey
      ) {
        // Check if we're not in an input field
        const target = event.target as HTMLElement;
        if (
          target.tagName !== "INPUT" &&
          target.tagName !== "TEXTAREA" &&
          !target.isContentEditable
        ) {
          event.preventDefault();
          setShowHelp(true);
        }
      }

      // Hide help on Escape
      if (event.key === "Escape" && showHelp) {
        setShowHelp(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [showHelp]);

  return {
    showHelp,
    setShowHelp,
  };
}

// Quick shortcuts display component for status bar
export function QuickShortcutsDisplay() {
  const quickShortcuts = [
    { label: "Play/Pause", keys: "Space" },
    { label: "Split", keys: "S" },
    { label: "Copy", keys: "Ctrl+C" },
    { label: "Paste", keys: "Ctrl+V" },
    { label: "Undo", keys: "Ctrl+Z" },
    { label: "Help", keys: "?" },
  ];

  return (
    <div className="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-400">
      {quickShortcuts.map((shortcut, index) => (
        <div key={shortcut.label} className="flex items-center space-x-1">
          <span>{shortcut.label}:</span>
          <Badge variant="outline" className="text-xs font-mono px-1 py-0">
            {shortcut.keys}
          </Badge>
          {index < quickShortcuts.length - 1 && (
            <span className="text-gray-400">•</span>
          )}
        </div>
      ))}
    </div>
  );
}
